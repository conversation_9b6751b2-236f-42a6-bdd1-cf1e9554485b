<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://www.hazelcast.com/schema/config"
           targetNamespace="http://www.hazelcast.com/schema/config"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           version="1.1">

    <xs:element name="hazelcast">
        <xs:complexType>
            <xs:choice minOccurs="1" maxOccurs="unbounded">
                <xs:element ref="import"/>
                <xs:element name="config-replacers" type="config-replacers" minOccurs="0" maxOccurs="1"/>
                <xs:element name="cluster-name" type="xs:string" minOccurs="0" maxOccurs="1"/>
                <xs:element name="license-key" type="xs:string" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            To use Hazelcast Enterprise, you need to set the license key here or programmatically.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="instance-name" type="xs:string" minOccurs="0" maxOccurs="1"/>
                <xs:element name="management-center" type="management-center" minOccurs="0" maxOccurs="1"/>
                <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
                <xs:element name="wan-replication" type="wan-replication" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="network" type="network" minOccurs="0" maxOccurs="1"/>
                <xs:element name="partition-group" type="partition-group" minOccurs="0" maxOccurs="1"/>
                <xs:element name="executor-service" type="executor-service" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="durable-executor-service" type="durable-executor-service" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="scheduled-executor-service" type="scheduled-executor-service" minOccurs="0"
                            maxOccurs="unbounded"/>
                <xs:element name="queue" type="queue" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="map" type="map" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="multimap" type="multimap" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="replicatedmap" type="replicatedmap" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="cache" type="cache" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="list" type="list" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="set" type="set" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="topic" type="topic" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="reliable-topic" type="reliable-topic" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="ringbuffer" type="ringbuffer" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="listeners" type="listeners" minOccurs="0" maxOccurs="1"/>
                <xs:element name="serialization" type="serialization" minOccurs="0" maxOccurs="1"/>
                <xs:element name="native-memory" type="native-memory" minOccurs="0" maxOccurs="1"/>
                <xs:element name="security" type="security" minOccurs="0" maxOccurs="1"/>
                <xs:element ref="member-attributes" minOccurs="0" maxOccurs="1"/>
                <xs:element name="split-brain-protection" type="split-brain-protection" minOccurs="0" maxOccurs="1"/>
                <xs:element name="lite-member" type="lite-member" minOccurs="0" maxOccurs="1"/>
                <xs:element name="hot-restart-persistence" type="hot-restart-persistence" minOccurs="0" maxOccurs="1"/>
                <xs:element name="user-code-deployment" type="user-code-deployment" minOccurs="0" maxOccurs="1"/>
                <xs:element name="cardinality-estimator" type="cardinality-estimator" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="flake-id-generator" type="flake-id-generator" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="crdt-replication" type="crdt-replication" minOccurs="0" maxOccurs="1"/>
                <xs:element name="pn-counter" type="pn-counter" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="advanced-network" type="advanced-network" minOccurs="0" maxOccurs="1"/>
                <xs:element name="cp-subsystem" type="cp-subsystem" minOccurs="0" maxOccurs="1"/>
                <xs:element name="auditlog" type="factory-class-with-properties" minOccurs="0" maxOccurs="1"/>
                <xs:element name="metrics" type="metrics" minOccurs="0" maxOccurs="1"/>
                <xs:element name="instance-tracking" type="instance-tracking" minOccurs="0" maxOccurs="1"/>
                <xs:element name="sql" type="sql" minOccurs="0" maxOccurs="1"/>
            </xs:choice>
            <xs:attribute name="id" type="xs:string" use="optional" default="default"/>
        </xs:complexType>
    </xs:element>
    <xs:element name="import">
        <xs:complexType>
            <xs:complexContent>
                <xs:restriction base="xs:anyType">
                    <xs:attribute name="resource" type="xs:string" use="required"/>
                </xs:restriction>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="config-replacers">
        <xs:sequence>
            <xs:element name="replacer" type="replacer" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="fail-if-value-missing" use="optional" default="true">
            <xs:annotation>
                <xs:documentation>
                    Controls if missing replacement value should lead to stop the boot process.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:boolean"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="replacer">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="class-name" use="required"/>
    </xs:complexType>

    <xs:complexType name="map">
        <xs:all>
            <xs:element name="metadata-policy" type="metadata-policy" minOccurs="0" maxOccurs="1" default="OFF">
                <xs:annotation>
                    <xs:documentation>
                        Metadata policy for this map. Hazelcast may process objects of supported types ahead of time to
                        create additional metadata about them. This metadata then is used to make querying and indexing faster.
                        Metadata creation may decrease put throughput.
                        Valid values are:
                        CREATE_ON_UPDATE (default): Objects of supported types are pre-processed when they are created and updated.
                        OFF: No metadata is created.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="BINARY">
                <xs:annotation>
                    <xs:documentation>
                        Data type used to store entries.
                        Possible values:
                        BINARY (default): keys and values are stored as binary data.
                        OBJECT: values are stored in their object forms.
                        NATIVE: keys and values are stored in native memory. Only available on Hazelcast Enterprise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the map, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cache-deserialized-values" type="cache-deserialized-values" minOccurs="0" maxOccurs="1"
                        default="INDEX-ONLY">
                <xs:annotation>
                    <xs:documentation>
                        Control caching of de-serialized values. Caching makes query evaluation faster, but it cost memory.
                        Possible Values:
                        NEVER: Never cache de-serialized object
                        INDEX-ONLY: Cache values only when they are inserted into an index.
                        ALWAYS: Always cache de-serialized values.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the map will be copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the map will be copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of seconds for each entry to stay in the map. Entries that are
                        older than &lt;time-to-live-seconds&gt; and are not updated for &lt;time-to-live-seconds&gt;
                        are automatically evicted from the map.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-idle-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of seconds for each entry to stay idle in the map. Entries that are
                        idle(not touched) for more than &lt;max-idle-seconds&gt; are
                        automatically evicted from the map. The entry is touched if get, put or containsKey is called.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="eviction" type="eviction-map" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        When maximum size is reached, map is evicted based on the eviction policy.
                        IMap has no eviction by default.

                        size:
                        maximum size can be any integer between 0 and Integer.MAX_VALUE.
                        For max-size to work, set the eviction-policy property to a value other than NONE.

                        Default value is 0.

                        max-size-policy:
                        max-size-policy has these valid values:
                        PER_NODE: Maximum number of map entries in each Hazelcast instance.
                        This is the default policy.
                        PER_PARTITION: Maximum number of map entries within each partition. Storage size
                        depends on the partition count in a Hazelcast instance.
                        This attribute should not be used often.
                        Avoid using this attribute with a small cluster: if the cluster is small it will
                        be hosting more partitions, and therefore map entries, than that of a larger
                        cluster. Thus, for a small cluster, eviction of the entries will decrease
                        performance (the number of entries is large).
                        USED_HEAP_SIZE: Maximum used heap size in megabytes per map for each Hazelcast instance.
                        USED_HEAP_PERCENTAGE: Maximum used heap size percentage per map for each Hazelcast
                        instance.
                        If, for example, JVM is configured to have 1000 MB and this value is 10, then the map
                        entries will be evicted when used heap size exceeds 100 MB.
                        FREE_HEAP_SIZE: Minimum free heap size in megabytes for each JVM.
                        FREE_HEAP_PERCENTAGE: Minimum free heap size percentage for each JVM.
                        For example, if JVM is configured to have 1000 MB and this value is 10,
                        then the map entries will be evicted when free heap size is below 100 MB.
                        USED_NATIVE_MEMORY_SIZE: Maximum used native memory size in megabytes per map
                        for each Hazelcast instance.
                        USED_NATIVE_MEMORY_PERCENTAGE: Maximum used native memory size percentage per map
                        for each Hazelcast instance.
                        FREE_NATIVE_MEMORY_SIZE: Minimum free native memory size in megabytes
                        for each Hazelcast instance.
                        FREE_NATIVE_MEMORY_PERCENTAGE: Minimum free native memory size percentage
                        for each Hazelcast instance.

                        eviction-policy:
                        Eviction policy has these valid values:
                        LRU (Least Recently Used),
                        LFU (Least Frequently Used),
                        RANDOM,
                        NONE.

                        Default value is "NONE".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="read-backup-data" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True if reading local backup entries is enabled, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merkle-tree" type="merkle-tree" minOccurs="0" maxOccurs="1"/>
            <xs:element name="hot-restart" type="hot-restart" minOccurs="0" maxOccurs="1"/>
            <xs:element name="event-journal" type="event-journal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="map-store" type="map-store" minOccurs="0" maxOccurs="1"/>
            <xs:element name="near-cache" type="near-cache" minOccurs="0" maxOccurs="1"/>
            <xs:element name="wan-replication-ref" type="wan-replication-ref" minOccurs="0"/>
            <xs:element name="indexes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="index" type="index" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="attributes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="attribute" type="map-attribute" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="entry-listeners" type="entry-listeners" minOccurs="0" maxOccurs="1"/>
            <xs:element name="partition-lost-listeners" type="partition-lost-listeners" minOccurs="0" maxOccurs="1"/>
            <xs:element name="partition-strategy" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="query-caches" type="query-caches" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the map.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="cache-entry-listeners">
        <xs:sequence>
            <xs:element name="cache-entry-listener" type="cache-entry-listener" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cache-entry-listener">
        <xs:all>
            <xs:element name="cache-entry-listener-factory" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="cache-entry-event-filter-factory" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required"/>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="old-value-required" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    If true, previously assigned values for the affected keys will be sent to this
                    cache-entry-listener implementation. Setting this attribute to true
                    creates additional traffic. Default value is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="synchronous" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    If true, this cache-entry-listener implementation will be called
                    in a synchronous manner. Default value is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="cache">
        <xs:all>
            <xs:element name="key-type" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required">
                        <xs:annotation>
                            <xs:documentation>
                                The fully qualified class name of the cache key type.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="value-type" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required">
                        <xs:annotation>
                            <xs:documentation>
                                The fully qualified class name of the cache value type.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True if statistics gathering is enabled on the cache, false (default) otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="management-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True if management is enabled on the cache, false (default) otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="read-through" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True if read-through caching is used, false (default) otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="write-through" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True if write-through caching is used, false (default) otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cache-loader-factory" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required">
                        <xs:annotation>
                            <xs:documentation>
                                The cache loader factory class name.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="cache-loader" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                The cache loader class name.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="cache-writer-factory" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required">
                        <xs:annotation>
                            <xs:documentation>
                                The cache writer factory class name.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="cache-writer" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:attribute name="class-name" use="required" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                The cache writer class name.
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
            <xs:element name="expiry-policy-factory" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:all>
                        <xs:annotation>
                            <xs:documentation>
                                Defines the expiry policy factory class name or
                                defines the expiry policy factory from predefined ones with duration configuration.
                            </xs:documentation>
                        </xs:annotation>
                        <xs:element name="timed-expiry-policy-factory"
                                    type="timed-expiry-policy-factory" minOccurs="0" maxOccurs="1"/>
                    </xs:all>
                    <xs:attribute name="class-name"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="cache-entry-listeners" type="cache-entry-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        List of cache entry listeners.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="BINARY">
                <xs:annotation>
                    <xs:documentation>
                        Data type used to store entries.
                        Possible values:
                        BINARY (default): keys and values are stored as binary data.
                        OBJECT: values are stored in their object forms.
                        NATIVE: keys and values are stored in native memory. Only available on Hazelcast Enterprise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if `1` is set as the `backup-count`,
                        then all entries of the cache are copied to one other instance as synchronous for fail-safety.
                        `backup-count` + `async-backup-count` cannot be bigger than maximum backup count which is `6`.
                        Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if `1` is set as the `async-backup-count`,
                        then all entries of the cache are copied to one other instance as asynchronous for fail-safety.
                        `backup-count` + `async-backup-count` cannot be bigger than maximum backup count which is `6`.
                        Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="eviction" type="eviction" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        When maximum size is reached, cache is evicted based on the eviction policy.

                        size:
                        maximum size can be any integer between 0 and Integer.MAX_VALUE.

                        Default value is 0.

                        max-size-policy:
                        max-size-policy has these valid values:
                        ENTRY_COUNT (Maximum number of cache entries in the cache),
                        USED_NATIVE_MEMORY_SIZE (Maximum used native memory size in megabytes per cache for each Hazelcast
                        instance),
                        USED_NATIVE_MEMORY_PERCENTAGE (Maximum used native memory size percentage per cache for each Hazelcast
                        instance),
                        FREE_NATIVE_MEMORY_SIZE (Maximum free native memory size in megabytes for each Hazelcast instance),
                        FREE_NATIVE_MEMORY_PERCENTAGE (Maximum free native memory size percentage for each Hazelcast instance).

                        Default value is "ENTRY_COUNT".

                        eviction-policy:
                        Eviction policy has these valid values:
                        LRU (Least Recently Used),
                        LFU (Least Frequently Used).

                        Default value is "LRU".
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="wan-replication-ref" type="wan-replication-ref" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        WAN replication configuration for cache.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="partition-lost-listeners" type="partition-lost-listeners" minOccurs="0" maxOccurs="1"/>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="hot-restart" type="hot-restart" minOccurs="0" maxOccurs="1"/>
            <xs:element name="event-journal" type="event-journal" minOccurs="0" maxOccurs="1"/>
            <xs:element name="disable-per-entry-invalidation-events" type="xs:boolean" minOccurs="0" maxOccurs="1"
                        default="false">
                <xs:annotation>
                    <xs:documentation>
                        Disables invalidation events for per entry but full-flush invalidation events are still enabled.
                        Full-flush invalidation event means that invalidation events for all entries on clear.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>

        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the cache.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="queue">
        <xs:all>
        	<xs:element name="priority-comparator-class-name" type="non-space-string" minOccurs="0" maxOccurs="1" >
	            <xs:annotation>
	                <xs:documentation>
	                    Fully-qualified comparator's class name to be used for the priority queue.
	                    If nothing is provided, then queue behaves as a FIFO queue.

                        If this value is non-null, then Hazelcast will ignore the queue store
                        "memory-limit" configuration value.
	                </xs:documentation>
	            </xs:annotation>
	        </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the queue, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of items in the queue.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means Integer.MAX_VALUE. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the queue are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the queue are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="empty-queue-ttl" type="empty-queue-ttl" minOccurs="0" maxOccurs="1" default="-1">
                <xs:annotation>
                    <xs:documentation>
                        Used to purge unused or empty queues. If you define a value (time in seconds) for this element,
                        then your queue will be destroyed if it stays empty or unused for that time.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="item-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the queue items. You can also set the attribute
                        include-value to true if you want the item event to contain the item values, and you can set
                        local to true if you want to listen to the items on the local node.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="item-listener" type="item-listener" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element ref="queue-store" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Includes the queue store factory class name and the following properties.

                        Binary: By default, Hazelcast stores the queue items in serialized form in memory.
                        Before it inserts the queue items into datastore, it deserializes them. But if you
                        will not reach the queue store from an external application, you might prefer that the
                        items be inserted in binary form. You can get rid of the de-serialization step; this
                        would be a performance optimization. The binary feature is disabled by default.

                        Memory Limit: This is the number of items after which Hazelcast will store items only to
                        datastore. For example, if the memory limit is 1000, then the 1001st item will be put
                        only to datastore. This feature is useful when you want to avoid out-of-memory conditions.
                        The default number for memory-limit is 1000. If you want to always use memory, you can set
                        it to Integer.MAX_VALUE.

                        Bulk Load: When the queue is initialized, items are loaded from QueueStore in bulks. Bulk
                        load is the size of these bulks. By default, bulk-load is 250.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the queue.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="list">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the list, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum size of the list.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means Integer.MAX_VALUE. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the list are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the list will be copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="item-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the list items. You can also set the attribute
                        include-value to true if you want the item event to contain the item values, and you can set
                        local to true if you want to listen to the items on the local node.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="item-listener" type="item-listener" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the list.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="set">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the set, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum size of the set.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means Integer.MAX_VALUE. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the set are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the set will be copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="item-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the set items. You can also set the attribute
                        include-value to true if you want the item event to contain the item values, and you can set
                        local to true if you want to listen to the items on the local node.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="item-listener" type="item-listener" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the set.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="multimap">
        <xs:annotation>
            <xs:documentation>
                Hazelcast MultiMap is a specialized map where you can store multiple values under a single key.
                Just like any other distributed data structure implementation in Hazelcast, MultiMap is distributed
                and thread-safe. Hazelcast MultiMap is not an implementation of java.util.Map due to the difference
                in method signatures. It supports most features of Hazelcast Map except for indexing, predicates and
                MapLoader/MapStore.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the multimap are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then all entries of the multimap are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the multimap, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="binary" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        By default, BINARY in-memory format is used, meaning that the object is stored
                        in a serialized form. You can set it to false, then, the OBJECT in-memory format
                        is used, which is useful when the OBJECT in-memory format has a smaller memory
                        footprint than BINARY.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value-collection-type" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Type of the value collection : SET or LIST.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="SET"/>
                        <xs:enumeration value="LIST"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="entry-listeners" type="entry-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the multimap entries. You can also set the attribute
                        include-value to true if you want the item event to contain the entry values, and you can set
                        local to true if you want to listen to the entries on the local node.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the multimap.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="replicatedmap">
        <xs:annotation>
            <xs:documentation>
                A ReplicatedMap is a map-like data structure with weak consistency
                and values locally stored on every node of the cluster.
                Whenever a value is written asynchronously, the new value will be internally
                distributed to all existing cluster members, and eventually every node will have
                the new value.
                When a new node joins the cluster, the new node initially will request existing
                values from older nodes and replicate them locally.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="OBJECT">
                <xs:annotation>
                    <xs:documentation>
                        Data type used to store entries.
                        Possible values:
                        BINARY: keys and values are stored as binary data.
                        OBJECT (default): values are stored in their object forms.
                        NATIVE: keys and values are stored in native memory. Only available on Hazelcast Enterprise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-fillup" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True if the replicated map is available for reads before the initial
                        replication is completed, false otherwise. Default is true. If false, no Exception will be
                        thrown when the replicated map is not yet ready, but call is blocked until
                        the initial replication is completed.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the replicated map, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="entry-listeners" type="entry-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the replicated map entries. You can also set the attribute
                        include-value to true if you want the item event to contain the entry values, and you can set
                        local to true if you want to listen to the entries on the local node.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the replicated map.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="topic">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the topic, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="global-ordering-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1"
                        default="false">
                <xs:annotation>
                    <xs:documentation>
                        Default is `false`, meaning there is no global order guarantee.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="message-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the topic messages.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="message-listener" type="listener-base" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="multi-threading-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Default is `false`, meaning only one dedicated thread will handle topic messages.
                        When multi-threading enabled (true) all threads from event thread pool can be used for message handling.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    The name of the topic.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="reliable-topic">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables statistics for this reliable topic.
                        Collects the creation time, total number of published and received
                        messages for each member locally. Its default value is true.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="message-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the topic messages.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="message-listener" type="listener-base" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="read-batch-size" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the read batch size.
                        The ReliableTopic tries to read a batch of messages from the ringbuffer.
                        It will get at least one, but if there are more available, then it will
                        try to get more to increase throughput. The maximum read batch size can
                        be influenced using the read batch size.
                        Apart from influencing the number of messages to retrieve, the
                        readBatchSize also determines how many messages will be processed
                        by the thread running the MessageListener before it returns back
                        to the pool to look for other MessageListeners that need to be
                        processed. The problem with returning to the pool and looking for new work
                        is that interacting with an executor is quite expensive due to contention
                        on the work-queue. The more work that can be done without retuning to the
                        pool, the smaller the overhead.
                        If the readBatchSize is 10 and there are 50 messages available,
                        10 items are retrieved and processed consecutively before the thread goes
                        back to the pool and helps out with the processing of other messages.
                        If the readBatchSize is 10 and there are 2 items available,
                        2 items are retrieved and processed consecutively.
                        If the readBatchSize is an issue because a thread will be busy
                        too long with processing a single MessageListener and it can't
                        help out other MessageListeners, increase the size of the
                        threadpool so the other MessageListeners don't need to wait for
                        a thread, but can be processed in parallel.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="topic-overload-policy" type="topic-overload-policy" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A policy to deal with an overloaded topic; so topic where there is no place to store new messages.
                        This policy can only be used in combination with the
                        com.hazelcast.core.HazelcastInstance#getReliableTopic(String).
                        The reliable topic uses a com.hazelcast.ringbuffer.Ringbuffer to
                        store the messages. A ringbuffer doesn't track where readers are, so
                        it has no concept of a slow consumers. This provides many advantages like
                        high performance reads, but it also gives the ability to the reader to
                        re-read the same message multiple times in case of an error.
                        A ringbuffer has a limited, fixed capacity. A fast producer may overwrite
                        old messages that are still being read by a slow consumer. To prevent
                        this, we may configure a time-to-live on the ringbuffer (see
                        com.hazelcast.config.RingbufferConfig#setTimeToLiveSeconds(int).
                        Once the time-to-live is configured, the TopicOverloadPolicy
                        controls how the publisher is going to deal with the situation that a
                        ringbuffer is full and the oldest item in the ringbuffer is not old
                        enough to get overwritten.
                        Keep in mind that this retention period (time-to-live) can keep messages
                        from being overwritten, even though all readers might have already completed reading.
                        Its default value is BLOCK. Available values are as follows:
                        - DISCARD_OLDEST:
                        Using this policy, a message that has not expired can be overwritten.
                        No matter the retention period set, the overwrite will just overwrite
                        the item.
                        This can be a problem for slow consumers because they were promised a
                        certain time window to process messages. But it will benefit producers
                        and fast consumers since they are able to continue. This policy sacrifices
                        the slow producer in favor of fast producers/consumers.
                        - DISCARD_NEWEST:
                        Message that was to be published is discarded.
                        - BLOCK:
                        The caller will wait until there is space in the Ringbuffer.
                        - ERROR:
                        The publish call fails immediately.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    The name of the reliable topic.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="ringbuffer-store">
        <xs:all>
            <xs:element ref="factory-or-class-name" minOccurs="0" maxOccurs="1"/>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" default="true" type="xs:boolean"/>
    </xs:complexType>
    <xs:complexType name="ringbuffer">
        <xs:all>
            <xs:element name="capacity" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of items in the ringbuffer. If no time-to-live-seconds is set, the size will always
                        be equal to capacity after the head completed the first loop around the ring. This is
                        because no items are getting expired. The default value is 10000.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the time to live in seconds which is the maximum number of seconds
                        for each item to stay in the ringbuffer before being removed.
                        Entries that are older than time-to-liveSeconds are removed from the
                        ringbuffer on the next ringbuffer operation (read or write).
                        Time to live can be disabled by setting time-to-liveSeconds to 0.
                        It means that items won't get removed because they expire. They may only
                        be overwritten.
                        When time-to-liveSeconds is disabled and after the tail does a full
                        loop in the ring, the ringbuffer size will always be equal to the capacity.
                        The time-to-liveSeconds can be any integer between 0 and Integer#MAX_VALUE.
                        0 means infinite. The default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then the items in the ringbuffer are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the backup-count,
                        then the items in the ringbuffer are copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="BINARY">
                <xs:annotation>
                    <xs:documentation>
                        Sets the in-memory format.
                        Setting the in-memory format controls the format of the stored item in the
                        ringbuffer:
                        - OBJECT: the item is stored in deserialized format (a regular object)
                        - BINARY (default): the item is stored in serialized format (a binary blob)
                        The object in-memory format is useful when:
                        - the object stored in object format has a smaller footprint than in
                        binary format
                        - if there are readers using a filter. Since for every filter
                        invocation, the object needs to be available in object format.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ringbuffer-store" type="ringbuffer-store" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Includes the ring buffer store factory class name. The store format is the same as the
                        in-memory-format for the ringbuffer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    The name of the ringbuffer. Required.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="merge-policy">
        <xs:annotation>
            <xs:documentation>
                While recovering from split-brain (network partitioning), data structure entries in the small cluster
                merge into the bigger cluster based on the policy set here. When an entry merges into the cluster,
                an entry with the same key (or value) might already exist in the cluster.
                The merge policy resolves these conflicts with different out-of-the-box or custom strategies.
                The out-of-the-box merge polices can be references by their simple class name.
                For custom merge policies you have to provide a fully qualified class name.
                <p>
                    The out-of-the-box policies are:
                    <br/>DiscardMergePolicy: the entry from the smaller cluster will be discarded.
                    <br/>HigherHitsMergePolicy: the entry with the higher number of hits wins.
                    <br/>LatestAccessMergePolicy: the entry with the latest access wins.
                    <br/>LatestUpdateMergePolicy: the entry with the latest update wins.
                    <br/>PassThroughMergePolicy: the entry from the smaller cluster wins.
                    <br/>PutIfAbsentMergePolicy: the entry from the smaller cluster wins if it doesn't exist in the cluster.
                    <br/>The default policy is: PutIfAbsentMergePolicy
                </p>
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="batch-size" default="100" type="xs:positiveInteger" use="optional"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="network">
        <xs:all>
            <xs:element name="public-address" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Overrides the public address of a node. By default, a node selects its socket address
                        as its public address. But behind a network address translation (NAT), two endpoints (nodes)
                        may not be able to see/access each other. If both nodes set their public addresses to their
                        defined addresses on NAT, then they can communicate with each other. In this case, their
                        public addresses are not an address of a local network interface but a virtual address defined by NAT.
                        This is optional to set and useful when you have a private cloud.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="port" type="port" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The ports which Hazelcast will use to communicate between cluster members. Its default value is 5701.
                        It has the following attributes.
                        port-count: The default value is 100, meaning that Hazelcast will try to bind 100 ports.
                        If you set the value of port as 5701, as members join the cluster, Hazelcast tries to find
                        ports between 5701 and 5801. You can change the port count in cases like having large
                        instances on a single machine or you are willing to have only a few ports assigned.
                        auto-increment: Default value is true. If port is set to 5701, Hazelcast will try to find free
                        ports between 5701 and 5801. Normally, you will not need to change this value, but it comes
                        in handy when needed. You may also want to choose to use only one port. In that case, you can
                        disable the auto-increment feature of port by setting its value as false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reuse-address" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        When you shutdown a cluster member, the server socket port will be in the TIME_WAIT
                        state for the next couple of minutes. If you start the member right after shutting it down,
                        you may not be able to bind it to the same port because it is in the TIME_WAIT state. If you
                        set reuse-address to true, the TIME_WAIT state is ignored and you can bind the member to the
                        same port again. Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        By default, Hazelcast lets the system pick up an ephemeral port during socket bind operation.
                        But security policies/firewalls may require to restrict outbound ports to be used by
                        Hazelcast-enabled applications. To fulfill this requirement, you can configure Hazelcast to use
                        only defined outbound ports.
                        outbound-ports has the ports attribute to allow you to define outbound ports.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="join" type="join" minOccurs="0" maxOccurs="1"/>
            <xs:element name="interfaces" type="interfaces" minOccurs="0" maxOccurs="1"/>
            <xs:element name="ssl" type="factory-class-with-properties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        SSL configuration with com.hazelcast.nio.ssl.SSLContextFactory used as the factory type.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0" maxOccurs="1"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        encryption algorithm such as
                        DES/ECB/PKCS5Padding,
                        PBEWithMD5AndDES,
                        AES/CBC/PKCS5Padding,
                        Blowfish,
                        DESede
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="member-address-provider" type="member-address-provider" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        IMPORTANT
                        This configuration is not intended to provide addresses of other cluster members with
                        which the hazelcast instance will form a cluster. This is an SPI for advanced use in
                        cases where the DefaultAddressPicker does not pick suitable addresses to bind to
                        and publish to other cluster members. For instance, this could allow easier
                        deployment in some cases when running on Docker, AWS or other cloud environments.
                        That said, if you are just starting with Hazelcast, you will probably want to
                        set the member addresses by using the tcp-ip or multicast configuration
                        or adding a discovery strategy.
                        Member address provider allows to plug in own strategy to customize:
                        1. What address Hazelcast will bind to
                        2. What address Hazelcast will advertise to other members on which they can bind to

                        In most environments you don't need to customize this and the default strategy will work just
                        fine. However in some cloud environments the default strategy does not make the right choice and
                        the member address provider delegates the process of address picking to external code.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="failure-detector" type="failure-detector" minOccurs="0" maxOccurs="1"/>
            <xs:element name="rest-api" type="rest-api" minOccurs="0" maxOccurs="1"/>
            <xs:element name="memcache-protocol" type="memcache-protocol" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="tcp-ip">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="required-member" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        IP address of the required member. Cluster will form only if the member with this IP
                        address is found.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:sequence>
                <xs:element name="member" type="xs:string" minOccurs="0" maxOccurs="unbounded" default="127.0.0.1">
                    <xs:annotation>
                        <xs:documentation>
                            IP address(es) of one or more well known members. Once members are connected to these
                            well known ones, all member addresses will be communicated with each other.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
            <xs:element name="interface" type="xs:string" minOccurs="0" maxOccurs="1"
                        default="127.0.0.1"/>
            <xs:element name="members" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Comma separated IP addresses of one or more well known members.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="member-list" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="member" type="xs:string" minOccurs="0" maxOccurs="unbounded"
                                    default="127.0.0.1"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:choice>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the TCP/IP discovery is enabled or not. Default value is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connection-timeout-seconds" type="xs:int" use="optional" default="5">
            <xs:annotation>
                <xs:documentation>
                    The maximum amount of time Hazelcast is going to try to connect to a well known member
                    before giving up. Setting it to a too low value could mean that a member is not able
                    to connect to a cluster. Setting it to a too high value means that member startup could
                    slow down because of longer timeouts (e.g. when a well known member is not up). Increasing
                    this value is recommended if you have many IPs listed and the members cannot properly
                    build up the cluster. Its default value is 5.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="port">
        <xs:simpleContent>
            <xs:extension base="xs:unsignedShort">
                <xs:attribute name="auto-increment" type="xs:boolean" use="optional" default="true"/>
                <xs:attribute name="port-count" type="xs:unsignedInt" use="optional" default="100"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="member-address-provider">
        <xs:all>
            <xs:element name="class-name" type="non-space-string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the class implementing the com.hazelcast.spi.MemberAddressProvider interface
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the member address provider SPI is enabled or not. Values can be true or false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="outbound-ports">
        <xs:sequence>
            <xs:element name="ports" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="multicast">
        <xs:all>
            <xs:element name="multicast-group" type="xs:string" minOccurs="0" maxOccurs="1" default="*********">
                <xs:annotation>
                    <xs:documentation>
                        The multicast group IP address. Specify it when you want to create clusters within the
                        same network. Values can be between ********* and ***************. Default value is *********.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="multicast-port" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="54327">
                <xs:annotation>
                    <xs:documentation>
                        The multicast socket port through which the Hazelcast member listens and sends discovery messages.
                        Default value is 54327.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="multicast-timeout-seconds" type="xs:int" minOccurs="0" maxOccurs="1" default="2">
                <xs:annotation>
                    <xs:documentation>
                        Only when the nodes are starting up, this timeout (in seconds) specifies the period during
                        which a node waits for a multicast response from another node. For example, if you set it
                        to 60 seconds, each node will wait for 60 seconds until a leader node is selected.
                        Its default value is 2 seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="multicast-time-to-live" type="xs:int" minOccurs="0" maxOccurs="1" default="32">
                <xs:annotation>
                    <xs:documentation>
                        Time-to-live value for multicast packets sent out to control the scope of multicasts.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trusted-interfaces" type="trusted-interfaces" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Includes IP addresses of trusted members. When a node wants to join to the cluster,
                        its join request will be rejected if it is not a trusted member. You can give an IP
                        addresses range using the wildcard (*) on the last digit of the IP address
                        (e.g. 192.168.1.* or *************-110).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="true">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the multicast discovery is enabled or not. Values can be true or false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="loopbackModeEnabled" type="xs:boolean" use="optional" default="false"/>
    </xs:complexType>
    <xs:complexType name="trusted-interfaces">
        <xs:sequence>
            <xs:element name="interface" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="aliased-discovery-strategy">
        <xs:sequence>
            <xs:any minOccurs="0" maxOccurs="unbounded" processContents="skip"/>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="true">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the EC2 discovery is enabled or not. Value can be true or false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="connection-timeout-seconds" type="xs:int" use="optional" default="5">
            <xs:annotation>
                <xs:documentation>
                    The maximum amount of time Hazelcast is going to try to connect to a well known member
                    before giving up. Please check if the specific discovery strategy supports this property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="auto-detection">
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="true">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the auto-detection discovery is enabled or not. Values can be true or false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="join">
        <xs:annotation>
            <xs:documentation>
                The `join` configuration element is used to enable the Hazelcast instances to form a cluster,
                i.e. to join the members. Three ways can be used to join the members: discovery by TCP/IP, by
                multicast, and by discovery on AWS (EC2 auto-discovery).
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="multicast" type="multicast" minOccurs="0"/>
            <xs:element name="tcp-ip" type="tcp-ip" minOccurs="0"/>
            <xs:element name="aws" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="gcp" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="azure" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="kubernetes" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="eureka" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="auto-detection" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="discovery-strategies" type="discovery-strategies" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="discovery-strategies">
        <xs:sequence>
            <xs:element name="node-filter" type="discovery-node-filter" minOccurs="0" maxOccurs="1"/>
            <xs:element name="discovery-strategy" type="discovery-strategy" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="discovery-node-filter">
        <xs:attribute name="class" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="discovery-strategy">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false"/>
        <xs:attribute name="class" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="interfaces">
        <xs:annotation>
            <xs:documentation>
                You can specify which network interfaces that Hazelcast should use. Servers mostly have more
                than one network interface, so you may want to list the valid IPs. Range characters
                ('\*' and '-') can be used for simplicity. For instance, 10.3.10.\* refers to IPs between
                ********* and ***********. Interface *********-18 refers to IPs between ********* and
                ********** (4 and 18 included). If network interface configuration is enabled (it is disabled
                by default) and if Hazelcast cannot find an matching interface, then it will print a message
                on the console and will not start on that node.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="interface" type="xs:string" default="127.0.0.1" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    True to enable these interfaces, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="executor-service">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the executor task, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pool-size" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="8">
                <xs:annotation>
                    <xs:documentation>
                        The number of executor threads per member for the executor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queue-capacity" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Queue capacity of the executor task. 0 means Integer.MAX_VALUE.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the executor task.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="durable-executor-service">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the executor task, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pool-size" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="16">
                <xs:annotation>
                    <xs:documentation>
                        The number of executor threads per member for the executor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="durability" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        The durability of the executor
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="capacity" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Capacity of the executor task per partition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the durable executor.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="scheduled-executor-service">
        <xs:all>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the executor task, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pool-size" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="16">
                <xs:annotation>
                    <xs:documentation>
                        The number of executor threads per member for the executor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="durability" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        The durability of the scheduled executor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="capacity" type="xs:unsignedShort" minOccurs="0" maxOccurs="1" default="100">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of tasks that a scheduler can have at any given point
                        in time as per capacity-policy.
                        Once the capacity is reached, new tasks will be rejected.
                        Capacity is ignored upon migrations to prevent any undesirable data-loss.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="capacity-policy" type="scheduled-executor-capacity-policy" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The active policy for the capacity setting
                        capacity-policy has these valid values:
                        PER_NODE: Maximum number of tasks in each Hazelcast instance.
                        This is the default policy.
                        PER_PARTITION: Maximum number of tasks within each partition. Storage size
                        depends on the partition count in a Hazelcast instance.
                        This attribute should not be used often.
                        Avoid using this attribute with a small cluster: if the cluster is small it will
                        be hosting more partitions, and therefore tasks, than that of a larger
                        cluster.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the scheduled executor.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="cardinality-estimator">
        <xs:all>
            <xs:element name="backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        Number of synchronous backups. For example, if 1 is set as the backup-count,
                        then the cardinality estimation will be copied to one other JVM for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="async-backup-count" type="backup-count" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of asynchronous backups. For example, if 1 is set as the async-backup-count,
                        then cardinality estimation will be copied to one other JVM (asynchronously) for
                        fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="merge-policy" type="merge-policy" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        While recovering from split-brain (network partitioning), CardinalityEstimator in the small cluster
                        merge into the bigger cluster based on the policy set here. When an estimator merges into the cluster,
                        an estimator with the same name might already exist in the cluster.
                        The merge policy resolves these conflicts with different out-of-the-box strategies.
                        The out-of-the-box merge polices can be references by their simple class name.
                        <p>
                            The out-of-the-box policies are:
                            <br/>DiscardMergePolicy: the estimator from the smaller cluster will be discarded.
                            <br/>HyperLogLogMergePolicy: the estimator will merge with the existing one, using the algorithmic merge for HyperLogLog.
                            <br/>PassThroughMergePolicy: the estimator from the smaller cluster wins.
                            <br/>PutIfAbsentMergePolicy: the estimator from the smaller cluster wins if it doesn't exist in the cluster.
                            <br/>The default policy is: HyperLogLogMergePolicy
                        </p>
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the cardinality estimator.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:simpleType name="backup-count">
        <xs:restriction base="xs:byte">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="6"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="empty-queue-ttl">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="-1"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="crdt-replica-count">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="2147483647"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="cp-group-size">
        <xs:restriction base="xs:unsignedInt">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="7"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="symmetric-encryption">
        <xs:annotation>
            <xs:documentation>
                Enterprise only. Hazelcast allows you to encrypt the entire socket level communication among
                all Hazelcast members. Encryption is based on Java Cryptography Architecture. In symmetric
                encryption, each node uses the same key, so the key is shared.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="algorithm" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as DES/ECB/PKCS5Padding, PBEWithMD5AndDES, Blowfish,
                        or DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="salt" type="xs:string" default="thesalt">
                <xs:annotation>
                    <xs:documentation>
                        Salt value to use when generating the secret key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="password" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Pass phrase to use when generating the secret key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="iteration-count" type="xs:int" default="19">
                <xs:annotation>
                    <xs:documentation>
                        Iteration count to use when generating the secret key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    True to enable symmetric encryption, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="failure-detector">
        <xs:all>
            <xs:element name="icmp" type="icmp"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="icmp">
        <xs:annotation>
            <xs:documentation>
                ICMP can be used in addition to the other detectors. It operates at layer 3 detects network
                and hardware issues more quickly
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="timeout-milliseconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="1000">
                <xs:annotation>
                    <xs:documentation>Timeout in Milliseconds before declaring a failed ping</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ttl" type="xs:integer" minOccurs="0" maxOccurs="1" default="255">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of times the IP Datagram (ping) can be forwarded, in most cases
                        all Hazelcast cluster members would be within one network switch/router therefore
                        default of 0 is usually sufficient
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="parallel-mode" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>Run ICMP detection in parallel with the Heartbeat failure detector</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fail-fast-on-startup" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        Cluster Member will fail to start if it is unable to action an ICMP ping command when ICMP is enabled.
                        Failure is usually due to OS level restrictions.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-attempts" type="xs:integer" minOccurs="0" maxOccurs="1" default="2">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of consecutive failed attempts before declaring a member suspect
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interval-milliseconds" type="xs:integer" minOccurs="0" maxOccurs="1" default="1000">
                <xs:annotation>
                    <xs:documentation>Time in milliseconds between each ICMP ping</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" default="false">
            <xs:annotation>
                <xs:documentation>Enables ICMP Pings to detect and suspect dead members</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="map-store">
        <xs:all>
            <xs:element ref="factory-or-class-name" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the class implementing MapLoader and/or MapStore.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="write-delay-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The number of seconds to delay the store writes. Default value is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="write-batch-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        The number of operations to be included in each batch processing round. Default value is 1.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="write-coalescing" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        Setting this is meaningful if you are using write behind in MapStore. When write-coalescing is true,
                        only the latest store operation on a key in the write-delay-seconds time-window will be
                        reflected to MapStore. Default value is true.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" default="true" type="xs:boolean">
            <xs:annotation>
                <xs:documentation>
                    True to enable this map-store, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="initial-mode">
            <xs:annotation>
                <xs:documentation>
                    Sets the initial load mode.
                    LAZY: default load mode, where load is asynchronous.
                    EAGER: load is blocked till all partitions are loaded.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="LAZY"/>
                    <xs:enumeration value="EAGER"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="query-caches">
        <xs:sequence>
            <xs:element name="query-cache" type="query-cache" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="query-cache">
        <xs:all>
            <xs:element name="include-value" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True to enable value caching, false to disable.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="predicate" type="predicate" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The predicate to filter events which will be applied to the QueryCache.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="entry-listeners" type="entry-listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lets you add listeners (listener classes) for the query cache entries.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="BINARY">
                <xs:annotation>
                    <xs:documentation>
                        Data type used to store entries.
                        Possible values:
                        BINARY (default): keys and values are stored as binary data.
                        OBJECT: values are stored in their object forms.
                        NATIVE: keys and values are stored in native memory. Only available on Hazelcast Enterprise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="populate" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True to enable initial population of the query cache, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="coalesce" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to enable coalescing of the query cache, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="delay-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The minimum number of seconds that an event waits in the node buffer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="batch-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="1">
                <xs:annotation>
                    <xs:documentation>
                        The batch size used to determine the number of events sent
                        in a batch to the query cache.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="buffer-size" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="16">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of events which can be stored in a partition buffer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="eviction" type="eviction" minOccurs="0" maxOccurs="1"/>
            <xs:element name="indexes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="index" type="index" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="predicate">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="type" use="required">
                    <xs:simpleType>
                        <xs:restriction base="non-space-string">
                            <xs:enumeration value="class-name"/>
                            <xs:enumeration value="sql"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:element name="factory-or-class-name" abstract="true"/>
    <xs:element name="class-name" substitutionGroup="factory-or-class-name"/>
    <xs:element name="factory-class-name" substitutionGroup="factory-or-class-name"/>
    <xs:element name="queue-store">
        <xs:complexType>
            <xs:all>
                <xs:element ref="factory-or-class-name" minOccurs="0" maxOccurs="1"/>
                <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
            </xs:all>
            <xs:attribute name="enabled" default="true" type="xs:boolean"/>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="wan-replication" mixed="true">
        <xs:sequence>
            <xs:element name="batch-publisher"
                        type="wan-batch-publisher"
                        minOccurs="0"
                        maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Configuration object for the built-in WAN publisher (available in
                        Hazelcast Enterprise). The publisher sends events to another Hazelcast
                        cluster in batches, sending when either when enough events are enqueued
                        or enqueued events have waited for enough time.
                        The endpoint can be a different cluster defined by static IP's or
                        discovered using a cloud discovery mechanism.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="custom-publisher"
                        type="wan-custom-publisher"
                        minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Configuration object for a custom WAN publisher. A single publisher defines how
                        WAN events are sent to a specific endpoint.
                        The endpoint can be some other external system which is
                        not a Hazelcast cluster (e.g. JMS queue).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="consumer" type="wan-consumer" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name for your WAN replication configuration.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="wan-batch-publisher" mixed="true">
        <xs:all>
            <xs:element name="cluster-name" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the cluster name used as an endpoint cluster name for authentication
                        on the target endpoint.
                        If there is no separate publisher ID property defined, this cluster name
                        will also be used as a WAN publisher ID. This ID is then used for
                        identifying the publisher in a WanReplicationConfig.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="snapshot-enabled" type="xs:boolean" default="false" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets if key-based coalescing is configured for this WAN publisher.
                        When enabled, only the latest WanReplicationEvent
                        of a key is sent to target.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="initial-publisher-state"
                        type="initial-publisher-state"
                        default="REPLICATING" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Defines the initial state in which a WAN publisher is started.
                        - REPLICATING (default):
                        State where both enqueuing new events is allowed, enqueued events are replicated to the target cluster
                        and WAN sync is enabled.
                        - PAUSED:
                        State where new events are enqueued but they are not dequeued. Some events which have been dequeued before
                        the state was switched may still be replicated to the target cluster but further events will not be
                        replicated. WAN sync is enabled.
                        - STOPPED:
                        State where neither new events are enqueued nor dequeued. As with the PAUSED state, some events might
                        still be replicated after the publisher has switched to this state. WAN sync is enabled.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queue-capacity" type="xs:integer" default="10000" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the capacity of the primary and backup queue for WAN replication events.
                        One hazelcast instance can have up to 2*queueCapacity events since
                        we keep up to queueCapacity primary events (events with keys for
                        which the instance is the owner) and queueCapacity backup events
                        (events with keys for which the instance is the backup).
                        Events for IMap and ICache count against this limit collectively.
                        When the queue capacity is reached, backup events are dropped while normal
                        replication events behave as determined by the queue-full-behavior.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="batch-size" type="xs:integer" default="500" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum batch size that can be sent to target cluster.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="batch-max-delay-millis" type="xs:integer" default="1000" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum amount of time in milliseconds to wait before sending a
                        batch of events to target cluster, if batch-size of events
                        have not arrived within this duration.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="response-timeout-millis" type="xs:integer" default="60000" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the duration in milliseconds for the waiting time before retrying to
                        send the events to target cluster again in case of acknowledgement
                        is not arrived.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queue-full-behavior"
                        type="wan-queue-full-behavior"
                        default="DISCARD_AFTER_MUTATION"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the configured behaviour of this WAN publisher when the WAN queue is
                        full.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="acknowledge-type"
                        type="wan-acknowledge-type"
                        default="ACK_ON_OPERATION_COMPLETE"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the strategy for when the target cluster should acknowledge that
                        a WAN event batch has been processed.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="discovery-period-seconds"
                        type="xs:integer"
                        default="10"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the period in seconds in which WAN tries to discover new target
                        endpoints and reestablish connections to failed endpoints.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-target-endpoints"
                        type="xs:integer"
                        default="2147483647"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Returns the maximum number of endpoints that WAN will connect to when
                        using a discovery mechanism to define endpoints.
                        This property has no effect when static endpoint addresses are defined
                        using target-endpoints.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-concurrent-invocations"
                        type="xs:integer"
                        default="-1"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum number of WAN event batches being sent to the target
                        cluster concurrently.
                        Setting this property to anything less than 2 will only allow a
                        single batch of events to be sent to each target endpoint and will
                        maintain causality of events for a single partition.
                        Setting this property to 2 or higher will allow multiple batches
                        of WAN events to be sent to each target endpoint. Since this allows
                        reordering or batches due to network conditions, causality and ordering
                        of events for a single partition is lost and batches for a single
                        partition are now sent randomly to any available target endpoint.
                        This, however, does present faster WAN replication for certain scenarios
                        such as replicating immutable, independent map entries which are only
                        added once and where ordering of when these entries are added is not
                        necessary.
                        Keep in mind that if you set this property to a value which is less than
                        the target endpoint count, you will lose performance as not all target
                        endpoints will be used at any point in time to process WAN event batches.
                        So, for instance, if you have a target cluster with 3 members (target
                        endpoints) and you want to use this property, it makes sense to set it
                        to a value higher than 3. Otherwise, you can simply disable it
                        by setting it to less than 2 in which case WAN will use the
                        default replication strategy and adapt to the target endpoint count
                        while maintaining causality.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="use-endpoint-private-address"
                        type="xs:boolean"
                        default="false" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets whether the WAN connection manager should connect to the
                        endpoint on the private address returned by the discovery SPI.
                        By default this property is false which means the WAN connection
                        manager will always use the public address.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idle-min-park-ns"
                        type="xs:long"
                        default="10000000"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the minimum duration in nanoseconds that the WAN replication thread
                        will be parked if there are no events to replicate.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idle-max-park-ns"
                        type="xs:long"
                        default="250000000"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum duration in nanoseconds that the WAN replication thread
                        will be parked if there are no events to replicate.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="publisher-id" type="xs:string"
                        minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the publisher ID used for identifying the publisher in a
                        WanReplicationConfig.
                        If there is no publisher ID defined (it is empty), the cluster name will
                        be used as a publisher ID.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target-endpoints" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Comma separated list of target cluster members,
                        e.g. 127.0.0.1:5701, 127.0.0.1:5702.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="aws" type="aliased-discovery-strategy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="gcp" type="aliased-discovery-strategy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="azure" type="aliased-discovery-strategy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="kubernetes" type="aliased-discovery-strategy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="eureka" type="aliased-discovery-strategy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="discovery-strategies" type="discovery-strategies" minOccurs="0" maxOccurs="1"/>
            <xs:element name="sync" type="wan-sync" minOccurs="0"/>
            <xs:element name="endpoint" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Reference to the name of a WAN endpoint config or WAN server socket endpoint config.
                        The network settings from the referenced endpoint configuration will setup the network
                        configuration of connections to the target WAN cluster members.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="wan-custom-publisher" mixed="true">
        <xs:all>
            <xs:element name="publisher-id" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the publisher ID used for identifying the publisher in a
                        WanReplicationConfig.
                        If there is no publisher ID defined (it is empty), the cluster name will
                        be used as a publisher ID.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="class-name" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Fully qualified class name of WAN Replication implementation WanPublisher.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="wan-sync">
        <xs:all>
            <xs:element name="consistency-check-strategy" type="consistency-check-strategy" minOccurs="0" default="NONE">
                <xs:annotation>
                    <xs:documentation>
                        Sets the strategy for checking consistency of data between source and
                        target cluster. Any inconsistency will not be reconciled, it will be
                        merely reported via the usual mechanisms (e.g. statistics, diagnostics).
                        The user must initiate WAN sync to reconcile there differences. For the
                        check procedure to work properly, the target cluster should support the
                        chosen strategy.
                        Default value is NONE, which means the check is disabled.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="wan-consumer">
        <xs:annotation>
            <xs:documentation>
                Config for processing WAN events received from a target cluster.
                You can configure certain behaviour when processing incoming WAN events
                or even configure your own implementation for a WAN consumer. A custom
                WAN consumer allows you to define custom processing logic and is usually
                used in combination with a custom WAN publisher.
                A custom consumer is optional and you may simply omit defining it which
                will cause the default processing logic to be used.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="class-name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the fully qualified class name of the class implementing
                        a custom WAN consumer (WanConsumer).
                        If you don't define a class name, the default processing logic for
                        incoming WAN events will be used.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Properties for the custom WAN consumer. These properties are
                        accessible when initalizing the WAN consumer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="persist-wan-replicated-data" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        When true, an incoming event over WAN replication can be persisted to a
                        database for example, otherwise it will not be persisted. Default value
                        is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="factory-class-with-properties">
        <xs:all>
            <xs:element name="factory-class-name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Full factory classname.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" default="false" type="xs:boolean">
            <xs:annotation>
                <xs:documentation>
                    True to enable this configuration element, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="item-listener">
        <xs:simpleContent>
            <xs:extension base="listener-base">
                <xs:attribute name="include-value" type="xs:boolean" use="optional" default="true"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="entry-listeners">
        <xs:sequence>
            <xs:element name="entry-listener" type="entry-listener" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="entry-listener">
        <xs:simpleContent>
            <xs:extension base="item-listener">
                <xs:attribute name="local" type="xs:boolean" use="optional" default="false"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="partition-lost-listeners">
        <xs:sequence>
            <xs:element name="partition-lost-listener" type="partition-lost-listener" minOccurs="0"
                        maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="partition-lost-listener">
        <xs:simpleContent>
            <xs:extension base="listener-base"/>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="index">
        <xs:all>
            <xs:element name="attributes" type="index-attributes"/>
            <xs:element name="bitmap-index-options" type="bitmap-index-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name"/>
        <xs:attribute name="type" type="index-type" default="SORTED"/>
    </xs:complexType>
    <xs:complexType name="index-attributes">
        <xs:sequence>
            <xs:element name="attribute" type="xs:string" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="index-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="SORTED"/>
            <xs:enumeration value="HASH"/>
            <xs:enumeration value="BITMAP"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="bitmap-index-options">
        <xs:all>
            <xs:element name="unique-key" type="xs:string" default="__key" minOccurs="0"/>
            <xs:element name="unique-key-transformation" type="bitmap-index-unique-key-transformation"
                        default="OBJECT" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:simpleType name="bitmap-index-unique-key-transformation">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="OBJECT"/>
            <xs:enumeration value="LONG"/>
            <xs:enumeration value="RAW"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="map-attribute">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="extractor-class-name" type="xs:string"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="partition-group">
        <xs:sequence>
            <xs:element name="member-group" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="interface" type="xs:string" minOccurs="0" maxOccurs="255"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:boolean" default="false"/>
        <xs:attribute name="group-type">
            <xs:annotation>
                <xs:documentation>
                    When you enable partition grouping, Hazelcast presents three choices for you to configure
                    partition groups.
                    HOST_AWARE: You can group nodes automatically using the IP addresses of nodes, so nodes
                    sharing the same network interface will be grouped together. All members on the same host
                    (IP address or domain name) will be a single partition group.
                    CUSTOM: You can do custom grouping using Hazelcast's interface matching configuration.
                    This way, you can add different and multiple interfaces to a group.
                    PER_MEMBER: You can give every member its own group. Each member is a group of its own
                    and primary and backup partitions are distributed randomly (not on the same physical member).
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="HOST_AWARE"/>
                    <xs:enumeration value="CUSTOM"/>
                    <xs:enumeration value="PER_MEMBER"/>
                    <xs:enumeration value="ZONE_AWARE"/>
                    <xs:enumeration value="NODE_AWARE"/>
                    <xs:enumeration value="SPI"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="management-center">
        <xs:all>
            <xs:element name="trusted-interfaces" type="trusted-interfaces" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Includes allowed IP addresses for Management Center connections.
                        More preciselly, the element configures addresses from which clients are allowed to run cluster
                        management tasks over the Hazelcast client protocol.
                        You can give an IP addresses range using the wildcard (*) on the last digit of the IP address
                        (e.g. 192.168.1.* or *************-110).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="scripting-enabled" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>
                    True to allow scripting on the member, false to disallow.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="security">
        <xs:all>
            <xs:element name="client-permission-policy" type="security-object" minOccurs="0" maxOccurs="1"/>
            <xs:element name="client-permissions" type="permissions" minOccurs="0" maxOccurs="1"/>
            <xs:element name="security-interceptors" type="interceptors" minOccurs="0" maxOccurs="1"/>
            <xs:element name="client-block-unmapped-actions" type="xs:boolean" default="true" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Block or allow actions, submitted as tasks in an Executor from clients and have no permission mappings.

                        true: Blocks all actions that have no permission mapping
                        false: Allows all actions that have no permission mapping
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="realms" type="realms" minOccurs="0" maxOccurs="1"/>
            <xs:element name="member-authentication" type="realm-reference" minOccurs="0" maxOccurs="1"/>
            <xs:element name="client-authentication" type="realm-reference" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false"/>
    </xs:complexType>
    <xs:complexType name="interceptors">
        <xs:sequence>
            <xs:element name="interceptor" type="interceptor" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="interceptor">
        <xs:attribute name="class-name" type="non-space-string" use="required"/>
    </xs:complexType>
    <xs:complexType name="login-modules">
        <xs:sequence>
            <xs:element name="login-module" type="login-module" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="login-module">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="class-name" type="non-space-string" use="required"/>
        <xs:attribute name="usage" use="optional" default="REQUIRED">
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="REQUIRED"/>
                    <xs:enumeration value="OPTIONAL"/>
                    <xs:enumeration value="REQUISITE"/>
                    <xs:enumeration value="SUFFICIENT"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="security-object">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="class-name" type="non-space-string" use="required"/>
    </xs:complexType>
    <xs:complexType name="permissions">
        <xs:choice minOccurs="1" maxOccurs="unbounded">
            <xs:element name="all-permissions" type="base-permission" minOccurs="0" maxOccurs="1"/>
            <xs:element name="map-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="queue-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="multimap-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="topic-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="list-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="set-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="lock-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="atomic-long-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="atomic-reference-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="countdown-latch-permission" type="instance-permission" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="semaphore-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="flake-id-generator-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="executor-service-permission" type="instance-permission" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="durable-executor-service-permission" type="instance-permission" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="cardinality-estimator-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="scheduled-executor-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="pn-counter-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="transaction-permission" type="base-permission" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cache-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="user-code-deployment-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="config-permission" type="base-permission" minOccurs="0" maxOccurs="1"/>
            <xs:element name="ring-buffer-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="reliable-topic-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="replicatedmap-permission" type="instance-permission" minOccurs="0" maxOccurs="unbounded"/>
        </xs:choice>
        <xs:attribute name="on-join-operation" type="permission-on-join-operation" use="optional" default="RECEIVE"/>
    </xs:complexType>
    <xs:complexType name="base-permission">
        <xs:sequence>
            <xs:element name="endpoints" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="endpoint" minOccurs="1" maxOccurs="unbounded" default="127.0.0.1">
                            <xs:annotation>
                                <xs:documentation>
                                    Endpoint address of the principal. Wildcards(*) can be used.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string"/>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="principal" type="xs:string" use="optional" default="*">
            <xs:annotation>
                <xs:documentation>
                    Name of the principal. Wildcards(*) can be used.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="instance-permission">
        <xs:complexContent>
            <xs:extension base="base-permission">
                <xs:sequence>
                    <xs:element name="actions" type="actions" minOccurs="1" maxOccurs="1"/>
                </xs:sequence>
                <xs:attribute name="name" type="xs:string" use="required">
                    <xs:annotation>
                        <xs:documentation>
                            Name of the permission. Wildcards(*) can be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="actions">
        <xs:sequence>
            <xs:element name="action" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Permission actions that are permitted on Hazelcast instance objects.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="all"/>
                        <xs:enumeration value="create"/>
                        <xs:enumeration value="destroy"/>
                        <xs:enumeration value="modify"/>
                        <xs:enumeration value="read"/>
                        <xs:enumeration value="remove"/>
                        <xs:enumeration value="lock"/>
                        <xs:enumeration value="listen"/>
                        <xs:enumeration value="release"/>
                        <xs:enumeration value="acquire"/>
                        <xs:enumeration value="put"/>
                        <xs:enumeration value="add"/>
                        <xs:enumeration value="index"/>
                        <xs:enumeration value="intercept"/>
                        <xs:enumeration value="publish"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="listeners">
        <xs:sequence>
            <xs:element name="listener" type="listener-base" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="listener-base">
        <xs:annotation>
            <xs:documentation>
                One of the following: membership-listener, instance-listener, migration-listener or
                partition-lost-listener.
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="non-space-string"/>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="near-cache">
        <xs:all>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" maxOccurs="1" default="BINARY">
                <xs:annotation>
                    <xs:documentation>
                        Data type used to store entries.
                        Possible values:
                        BINARY (default): keys and values are stored as binary data.
                        OBJECT: values are stored in their object forms.
                        NATIVE: keys and values are stored in native memory. Only available on Hazelcast Enterprise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="serialize-keys" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Defines if the Near Cache keys should be serialized or not.
                        Keys should be serialized if they are mutable and need to be cloned via serialization.
                        NOTE: It's not supported to disable key serialization with in-memory-format NATIVE.
                        This setting will have no effect in that case.
                        Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="invalidate-on-change" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True to evict the cached entries if the entries are changed (updated or removed).
                        Default value is true.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of seconds for each entry to stay in the Near Cache. Entries that are
                        older than time-to-live-seconds will get automatically evicted from the Near Cache.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-idle-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of seconds each entry can stay in the Near Cache as untouched (not-read).
                        Entries that are not read (touched) more than max-idle-seconds value will get removed
                        from the Near Cache.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means Integer.MAX_VALUE. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="eviction" type="eviction" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cache-local-entries" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to cache local entries, which belong to the member itself.
                        This is useful when in-memory-format for Near Cache is different than the map's one.
                        Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="optional" type="xs:string" default="default"/>
    </xs:complexType>

    <xs:simpleType name="in-memory-format">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="BINARY"/>
            <xs:enumeration value="OBJECT"/>
            <xs:enumeration value="NATIVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="metadata-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CREATE_ON_UPDATE"/>
            <xs:enumeration value="OFF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="cache-deserialized-values">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NEVER"/>
            <xs:enumeration value="ALWAYS"/>
            <xs:enumeration value="INDEX-ONLY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="time-unit">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NANOSECONDS"/>
            <xs:enumeration value="MICROSECONDS"/>
            <xs:enumeration value="MILLISECONDS"/>
            <xs:enumeration value="SECONDS"/>
            <xs:enumeration value="MINUTES"/>
            <xs:enumeration value="HOURS"/>
            <xs:enumeration value="DAYS"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="expiry-policy-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="ACCESSED"/>
            <xs:enumeration value="ETERNAL"/>
            <xs:enumeration value="MODIFIED"/>
            <xs:enumeration value="TOUCHED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="timed-expiry-policy-factory">
        <xs:attribute name="expiry-policy-type" type="expiry-policy-type" use="required"/>
        <xs:attribute name="duration-amount" type="xs:unsignedLong" use="optional"/>
        <xs:attribute name="time-unit" type="time-unit" use="optional"/>
    </xs:complexType>

    <xs:simpleType name="non-space-string">
        <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:pattern value="\S+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="serialization">
        <xs:all>
            <xs:element name="portable-version" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The version of the portable serialization. Portable version is used to differentiate two
                        same classes that have changes on it like adding/removing field or changing a type of a field.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="use-native-byte-order" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to use native byte order of the underlying platform, false otherwise. Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="byte-order" minOccurs="0" maxOccurs="1" default="BIG_ENDIAN">
                <xs:annotation>
                    <xs:documentation>
                        Defines the byte order that the serialization will use.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="BIG_ENDIAN"/>
                        <xs:enumeration value="LITTLE_ENDIAN"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="enable-compression" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to enable compression if default Java serialization is used, false otherwise.
                        Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="enable-shared-object" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to enable shared object if default Java serialization is used, false otherwise.
                        Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="allow-unsafe" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        True to allow the usage of unsafe, false otherwise.
                        Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data-serializable-factories" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="data-serializable-factory" type="serialization-factory" minOccurs="0"
                                    maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Custom classes implementing com.hazelcast.nio.serialization.DataSerializableFactory to be
                                    registered. These can be used to speed up serialization/deserialization of objects.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="portable-factories" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="portable-factory" type="serialization-factory" minOccurs="0"
                                    maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    PortableFactory class to be registered.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="serializers" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:choice minOccurs="1" maxOccurs="unbounded">
                        <xs:element name="global-serializer" type="global-serializer" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    Global serializer class to be registered if no other serializer is applicable.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="serializer" type="serializer" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Defines the class name and the type class of the serializer implementation.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="check-class-def-errors" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        If true (default), serialization system will check class definitions error at start and throw a
                        Serialization Exception with error definition.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="java-serialization-filter" type="java-serialization-filter" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Basic protection against untrusted deserialization based on class/package blacklisting and whitelisting.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="serialization-factory">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="factory-id" use="required" type="xs:unsignedInt"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="serializer">
        <xs:attribute name="class-name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>
                    The name of the class that will be serialized.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="type-class" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>
                    The type of the class that will be serialized.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="global-serializer">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="override-java-serialization" type="xs:boolean" default="false" use="optional">
                    <xs:annotation>
                        <xs:documentation>
                            Java Serializable and Externalizable is prior to global serializer by default. If set true
                            the Java serialization step assumed to be handled by the global serializer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="java-serialization-filter">
        <xs:all>
            <xs:element name="blacklist" type="filter-list" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Blacklisted classes and packages, which are not allowed to be deserialized.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="whitelist" type="filter-list" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Whitelisted classes and packages, which are allowed to be deserialized. If the list is empty
                        (no class or package name provided) then all classes are allowed.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="defaults-disabled" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    Disables including default list entries (hardcoded in Hazelcast source code).
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:boolean"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="filter-list">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="class" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Name of a class to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="package" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Name of a package to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="prefix" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Class name prefix to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="socket-interceptor">
        <xs:all>
            <xs:element name="class-name" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="properties" type="properties" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="enabled" default="false" type="xs:boolean"/>
    </xs:complexType>

    <xs:complexType name="native-memory">
        <xs:all>
        <xs:element name="size" type="memory-size" minOccurs="0" maxOccurs="1"/>
            <xs:element name="min-block-size" type="xs:positiveInteger" minOccurs="0" maxOccurs="1"/>
            <xs:element name="page-size" type="xs:positiveInteger" minOccurs="0" maxOccurs="1"/>
            <xs:element name="metadata-space-percentage" minOccurs="0" maxOccurs="1">
                <xs:simpleType>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="3"/>
                        <xs:fractionDigits value="1"/>
                        <xs:minInclusive value="5"/>
                        <xs:maxInclusive value="95"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="persistent-memory-directory" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="persistent-memory" type="persistent-memory" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="allocator-type" default="POOLED" type="memory-allocator-type"/>
        <xs:attribute name="enabled" default="false" type="xs:boolean"/>
    </xs:complexType>

    <xs:complexType name="memory-size">
        <xs:attribute name="value" type="xs:int" default="128"/>
        <xs:attribute name="unit" type="memory-unit" default="MEGABYTES"/>
    </xs:complexType>

    <xs:simpleType name="memory-unit">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="BYTES"/>
            <xs:enumeration value="KILOBYTES"/>
            <xs:enumeration value="MEGABYTES"/>
            <xs:enumeration value="GIGABYTES"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="topic-overload-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="DISCARD_OLDEST"/>
            <xs:enumeration value="DISCARD_NEWEST"/>
            <xs:enumeration value="BLOCK"/>
            <xs:enumeration value="ERROR"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="memory-allocator-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="STANDARD"/>
            <xs:enumeration value="POOLED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="persistent-memory">
        <xs:annotation>
            <xs:documentation>
                Configuration for persistent memory (e.g. Intel Optane) devices.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="directories" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        List of directories where the persistent memory
                        is mounted to. Requires the mode attribute of persistent-memory
                        to be MOUNTED (default).

                        If the specified directories are not unique either in the directories
                        themselves or in the NUMA nodes specified for them,
                        the configuration is treated as invalid. Setting the NUMA
                        node on the subset of the configured directories while leaving
                        not set on others also makes the configuration invalid.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice maxOccurs="unbounded">
                        <xs:element name="directory" type="persistent-memory-directory"/>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" default="false" type="xs:boolean">
            <xs:annotation>
                <xs:documentation>
                    Sets if using persistent memory as Hazelcast native memory is enabled.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="mode" default="MOUNTED" type="persistent-memory-mode">
            <xs:annotation>
                <xs:documentation>
                    Sets the operational mode of the persistent memory configured
                    on the machine.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="persistent-memory-mode">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="MOUNTED">
                <xs:annotation>
                    <xs:documentation>
                        The persistent memory is mounted into the file system (also known as FS DAX mode).
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SYSTEM_MEMORY">
                <xs:annotation>
                    <xs:documentation>
                        The persistent memory is onlined as system memory (also known as KMEM DAX mode).
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="persistent-memory-directory">
        <xs:annotation>
            <xs:documentation>
                The directory where persistent memory is mounted to.

                If the specified directory id not unique either in the
                directory itself or in the NUMA node specified, the
                configuration will be treated as invalid. Setting the NUMA
                node on the subset of the configured directories while leaving
                not set on others also results in invalid configuration.
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="numa-node" type="xs:int" default="-1">
                    <xs:annotation>
                        <xs:documentation>
                            The NUMA node that the persistent memory mounted
                            to the given directory is attached to.

                            NUMA nodes have to be unique across the entire
                            persistent memory configuration, otherwise the
                            configuration will be treated as invalid. Similarly,
                            setting the NUMA node on the subset of the configured
                            directories while leaving not set on others also
                            results in invalid configuration.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="property">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" use="required" type="non-space-string"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="properties">
        <xs:annotation>
            <xs:documentation>
                A full list of available properties can be found at
                http://docs.hazelcast.org/docs/latest/manual/html-single/index.html#system-properties
            </xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="property" type="property"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="member-attributes">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="attribute" type="attribute" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
        <xs:unique name="uniqueAttributeConstraint">
            <xs:selector xpath="./*"/>
            <xs:field xpath="@name"/>
        </xs:unique>
    </xs:element>
    <xs:simpleType name="attributeName">
        <xs:restriction base="non-space-string"/>
    </xs:simpleType>
    <xs:complexType name="attribute">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" use="required" type="attributeName"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="attributes">
        <xs:sequence>
            <xs:element name="attribute" type="attribute" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="topology-changed-strategy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CANCEL_RUNNING_OPERATION"/>
            <xs:enumeration value="DISCARD_AND_RESTART"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="eviction-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="LRU"/>
            <xs:enumeration value="LFU"/>
            <xs:enumeration value="RANDOM"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="max-size-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="ENTRY_COUNT"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_PERCENTAGE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_PERCENTAGE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="max-size-policy-map">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="PER_NODE"/>
            <xs:enumeration value="PER_PARTITION"/>
            <xs:enumeration value="USED_HEAP_SIZE"/>
            <xs:enumeration value="USED_HEAP_PERCENTAGE"/>
            <xs:enumeration value="FREE_HEAP_SIZE"/>
            <xs:enumeration value="FREE_HEAP_PERCENTAGE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_PERCENTAGE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_PERCENTAGE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="scheduled-executor-capacity-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="PER_NODE"/>
            <xs:enumeration value="PER_PARTITION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="eviction">
        <xs:attribute name="size" type="xs:nonNegativeInteger" default="10000" use="optional"/>
        <xs:attribute name="max-size-policy" type="max-size-policy" default="ENTRY_COUNT" use="optional"/>
        <xs:attribute name="eviction-policy" type="eviction-policy" default="LRU" use="optional"/>
        <xs:attribute name="comparator-class-name" type="xs:string" use="optional"/>
    </xs:complexType>

    <xs:complexType name="eviction-map">
        <xs:attribute name="size" type="xs:nonNegativeInteger" default="0" use="optional"/>
        <xs:attribute name="max-size-policy" type="max-size-policy-map" default="PER_NODE" use="optional"/>
        <xs:attribute name="eviction-policy" type="eviction-policy" default="LRU" use="optional"/>
        <xs:attribute name="comparator-class-name" type="xs:string" use="optional"/>
    </xs:complexType>

    <xs:complexType name="wan-replication-ref">
        <xs:all>
            <xs:element name="merge-policy-class-name" type="xs:string" minOccurs="0"
                        default="com.hazelcast.spi.merge.PassThroughMergePolicy">
                <xs:annotation>
                    <xs:documentation>
                        Sets the merge policy sent to the WAN replication target to merge
                        replicated entries with existing target entries.
                        The default merge policy is com.hazelcast.spi.merge.PassThroughMergePolicy.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="filters" type="wan-replication-ref-filters" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the list of class names implementing the CacheWanEventFilter or
                        MapWanEventFilter for filtering outbound WAN replication events.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="republishing-enabled" type="xs:boolean" minOccurs="0" default="true">
                <xs:annotation>
                    <xs:documentation>
                        When enabled, an incoming event to a member is forwarded to the target cluster of that member.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the wan-replication configuration. IMap or ICache instance uses this wan-replication config.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="split-brain-protection">
        <xs:all>
            <xs:element name="minimum-cluster-size" type="minimum-cluster-size" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The minimum number of members required in a cluster for the cluster to remain in an
                        operational state. If the number of members is below the defined minimum at any time,
                        the operations are rejected and the rejected operations throw a SplitBrainProtectionException to
                        their callers.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="protect-on" type="protect-on" minOccurs="0" maxOccurs="1"/>
            <xs:element name="function-class-name" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="listeners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        You can register split brain protection listeners to be notified about split brain protection
                        results. Split brain protection listeners are local to the member that they are registered,
                        so they receive only events occurred on that local member.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="listener" type="split-brain-protection-listener" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element ref="choice-of-split-brain-protection-function" minOccurs="0" maxOccurs="1" />
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:element name="choice-of-split-brain-protection-function" abstract="true"/>

    <xs:element name="member-count-split-brain-protection" substitutionGroup="choice-of-split-brain-protection-function">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>
                    Determines split based protection based on the number of currently live cluster members.
                    The minimum number of members required in a cluster for the cluster to remain in an
                    operational state is configured separately in <code>&lt;minimum-cluster-size&gt;</code> element.
                    If the number of members is below the defined minimum at any time,
                    the operations are rejected and the rejected operations throw a SplitBrainProtectionException to
                    their callers.
                </xs:documentation>
            </xs:annotation>
        </xs:complexType>
    </xs:element>

    <xs:element name="probabilistic-split-brain-protection" substitutionGroup="choice-of-split-brain-protection-function">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>
                    A probabilistic split brain protection function based on Phi Accrual failure detector. See
                    com.hazelcast.internal.cluster.fd.PhiAccrualClusterFailureDetector for implementation
                    details. Configuration:<br/>
                    - <code>acceptable-heartbeat-pause-millis</code>: duration in milliseconds corresponding to number
                    of potentially lost/delayed heartbeats that will be accepted before considering it to be an anomaly.
                    This margin is important to be able to survive sudden, occasional, pauses in heartbeat arrivals,
                    due to for example garbage collection or network drops.<br/>
                    - <code>suspicion-threshold</code>: threshold for suspicion level. A low threshold is prone to generate
                    many wrong suspicions but ensures a quick detection in the event of a real crash. Conversely, a high
                    threshold generates fewer mistakes but needs more time to detect actual crashes.<br/>
                    - <code>max-sample-size</code>: number of samples to use for calculation of mean and standard
                    deviation of inter-arrival times.<br/>
                    - <code>heartbeat-interval-millis</code>: bootstrap the stats with heartbeats that corresponds to
                    this duration in milliseconds, with a rather high standard deviation (since environment is unknown
                    in the beginning)<br/>
                    - <code>min-std-deviation-millis</code>: minimum standard deviation (in milliseconds) to use for the normal
                    distribution used when calculating phi. Too low standard deviation might result in too much
                    sensitivity for sudden, but normal, deviations in heartbeat inter arrival times.
                </xs:documentation>
            </xs:annotation>
            <xs:attribute name="acceptable-heartbeat-pause-millis" type="xs:unsignedLong" use="optional" default="60000"/>
            <xs:attribute name="suspicion-threshold" type="xs:double" use="optional" default="10" />
            <xs:attribute name="max-sample-size" type="xs:unsignedInt" use="optional" default="200"/>
            <xs:attribute name="min-std-deviation-millis" type="xs:unsignedLong" use="optional" default="100" />
            <xs:attribute name="heartbeat-interval-millis" type="xs:unsignedLong" use="optional" default="5000"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="recently-active-split-brain-protection" substitutionGroup="choice-of-split-brain-protection-function">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>
                    A split brain protection function that keeps track of the last heartbeat timestamp per each member.
                    For a member to be considered live (for the purpose to conclude whether the minimum cluster size property is satisfied)
                    a heartbeat must have been received at most <code>heartbeat-tolerance</code> milliseconds before
                    current time.
                </xs:documentation>
            </xs:annotation>
            <xs:attribute name="heartbeat-tolerance-millis" type="xs:unsignedInt" default="5000"/>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="protect-on">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="READ"/>
            <xs:enumeration value="WRITE"/>
            <xs:enumeration value="READ_WRITE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="minimum-cluster-size">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="split-brain-protection-listener">
        <xs:simpleContent>
            <xs:extension base="listener-base">
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="lite-member">
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    True to set the node as a lite member, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="user-code-deployment">
        <xs:all>
            <xs:element name="class-cache-mode" default="ETERNAL">
                <xs:annotation>
                    <xs:documentation>
                        Controls caching of user classes loaded from remote members.

                        OFF: Never caches loaded classes. This is suitable for loading runnables, callables,
                        entry processors, etc.
                        ETERNAL: Cache indefinitely. This is suitable when you load long-living objects,
                        such as domain objects stored in a map.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="OFF"/>
                        <xs:enumeration value="ETERNAL"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="provider-mode" default="LOCAL_AND_CACHED_CLASSES">
                <xs:annotation>
                    <xs:documentation>
                        Controls how to react on receiving a classloading request from a remote member

                        OFF: Never serve classes to other members. This member will never server classes to remote
                        members.
                        LOCAL_CLASSES_ONLY: Serve classes from local classpath only. Classes loaded from other members
                        will be used locally, but they won't be served to other members.
                        LOCAL_AND_CACHED_CLASSES: Server classes loaded from both local classpath and from other members.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="OFF"/>
                        <xs:enumeration value="LOCAL_CLASSES_ONLY"/>
                        <xs:enumeration value="LOCAL_AND_CACHED_CLASSES"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="blacklist-prefixes" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Filter to constraint members to be used for classloading request when a class
                        is not available locally.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="whitelist-prefixes" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Command separated list of prefixes of classes which will be loaded remotely.

                        Use this to enable loading of explicitly selected user classes only and
                        disable remote loading for all other classes. This gives you a fine-grained control
                        over classloading.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="provider-filter" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Filter to constraint members to be used for classloading request when a class
                        is not available locally.

                        Filter format: HAS_ATTRIBUTE:foo this will send classloading requests
                        only to members which has a member attribute foo set. Value is ignored,
                        it can be any type. A present of the attribute is sufficient.

                        This facility allows to have a control on classloading. You can e.g. start Hazelcast lite
                        members dedicated for class-serving.

                        Setting the filter to null will allow to load classes from all members.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    True to enable User Code Deployment on this member, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:element name="secure-store-substitution-group" abstract="true"/>

    <xs:complexType name="keystore">
        <xs:all>
            <xs:element name="path" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The path of the KeyStore file.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:string" minOccurs="0" maxOccurs="1" default="PKCS12">
                <xs:annotation>
                    <xs:documentation>
                        The type of the KeyStore (PKCS12, JCEKS, etc.).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="password" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The password to access the KeyStore.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="current-key-alias" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The alias for the current encryption key entry (optional).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="polling-interval" type="xs:int" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The polling interval (in seconds) for checking for changes in the KeyStore.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:element name="keystore" type="keystore" substitutionGroup="secure-store-substitution-group">
        <xs:annotation>
            <xs:documentation>
                Java KeyStore Secure Store configuration.
            </xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="vault">
        <xs:all>
            <xs:element name="address" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The address of the Vault server.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="secret-path" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The Vault secret path.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="token" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The Vault access token.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ssl" type="factory-class-with-properties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        SSL/TLS configuration for HTTPS connections.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="polling-interval" type="xs:int" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The polling interval (in seconds) for checking for changes in Vault.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:element name="vault" type="vault" substitutionGroup="secure-store-substitution-group">
        <xs:annotation>
            <xs:documentation>
                HashiCorp Vault Secure Store configuration.
            </xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="secure-store">
        <xs:sequence>
            <xs:element ref="secure-store-substitution-group" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="encryption-at-rest">
        <xs:annotation>
            <xs:documentation>
                Configuration for hot restart (symmetric) encryption at rest. Encryption is based
                on Java Cryptography Architecture.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="algorithm" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as AES/CBC/PKCS5Padding, DES/ECB/PKCS5Padding, Blowfish,
                        or DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="salt" type="xs:string" minOccurs="0" default="thesalt">
                <xs:annotation>
                    <xs:documentation>
                        Salt value to use when generating the secret key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="key-size" type="xs:int" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The key size in bits used when generating encryption keys (optional).
                        The default value of 0 implies falling back to the cipher-specific
                        default key size.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="secure-store" type="secure-store">
                <xs:annotation>
                    <xs:documentation>
                        The Secure Store configuration.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>
                    True to enable symmetric encryption, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="hot-restart-persistence">
        <xs:all>
            <xs:element name="base-dir" type="xs:string" minOccurs="0" maxOccurs="1" default="hot-restart">
                <xs:annotation>
                    <xs:documentation>
                        Base directory for all hot-restart data. Can be an absolute or relative path to the node startup
                        directory.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-dir" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Base directory for hot backups. Each new backup will be created in a separate directory inside this one.
                        Can be an absolute or relative path to the node startup directory.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="parallelism" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Level of parallelism in Hot Restart Persistence. There will be this many IO threads,
                        each writing in parallel to its own files. During the Hot Restart procedure, this many
                        IO threads will be reading the files and this many Rebuilder threads will be rebuilding
                        the HR metadata.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="validation-timeout-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Validation timeout for the Hot Restart procedure. Includes the time to validate
                        cluster members expected to join and partition table of the whole cluster.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data-load-timeout-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Data load timeout for Hot Restart procedure. All members in the cluster should
                        complete restoring their local data before this timeout.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cluster-data-recovery-policy" type="xs:string" minOccurs="0" maxOccurs="1"
                        default="FULL_RECOVERY_ONLY">
                <xs:annotation>
                    <xs:documentation>
                        Specifies the policy that will be respected during hot restart cluster start. Valid values are :
                        FULL_RECOVERY_ONLY : Starts the cluster only when all expected nodes are present and correct.
                        Otherwise, it fails.
                        PARTIAL_RECOVERY_MOST_RECENT : Starts the cluster with the members which have most up-to-date
                        partition table and successfully restored their data. All other members will leave the cluster and
                        force-start themselves. If no member restores its data successfully, cluster start fails.
                        PARTIAL_RECOVERY_MOST_COMPLETE : Starts the cluster with the largest group of members which have the
                        same partition table version and successfully restored their data. All other members will leave the
                        cluster and force-start themselves. If no member restores its data successfully, cluster start fails.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="auto-remove-stale-data" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        Sets whether or not automatically removal of stale Hot Restart data is enabled.
                        When a member terminates or crashes when cluster state is ACTIVE, remaining members
                        redistributes data among themselves and data persisted on terminated member's storage becomes
                        stale. That terminated member cannot rejoin the cluster without removing Hot Restart data.
                        When auto-removal of stale Hot Restart data is enabled, while restarting that member,
                        Hot Restart data is automatically removed and it joins the cluster as a completely new member.
                        Otherwise, Hot Restart data should be removed manually.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="encryption-at-rest" type="encryption-at-rest" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Specifies parameters for encryption of Hot Restart data. This includes the encryption algorithm
                        to be used (such as AES, DESede etc.) and the Secure Store configuration for retrieving the
                        encryption keys.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    True to enable Hot Restart Persistence, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="crdt-replication">
        <xs:all>
            <xs:element name="replication-period-millis" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The period between two replications of CRDT states in milliseconds.
                        A lower value will increase the speed at which changes are disseminated
                        to other cluster members at the expense of burst-like behaviour - less
                        updates will be batched together in one replication message and one
                        update to a CRDT may cause a sudden burst of replication messages in a
                        short time interval.
                        The value must be a positive non-null integer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-concurrent-replication-targets" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The maximum number of target members that we replicate the CRDT states
                        to in one period. A higher count will lead to states being disseminated
                        more rapidly at the expense of burst-like behaviour - one update to a
                        CRDT will lead to a sudden burst in the number of replication messages
                        in a short time interval.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="hot-restart">
        <xs:all>
            <xs:element name="fsync" type="xs:boolean" default="false">
                <xs:annotation>
                    <xs:documentation>
                        If set to true, when each update operation on this structure completes,
                        it is guaranteed that it has already been persisted.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>

        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if Hot Restart Persistence is enabled, false otherwise. Only available on Hazelcast Enterprise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="event-journal">
        <xs:annotation>
            <xs:documentation>
                Configuration for an event journal. The event journal keeps events related
                to a specific partition and data structure. For instance, it could keep
                map or cache add, update, remove, merge events along with the key, old value,
                new value and so on.
                This configuration is not tied to a specific data structure and can be reused.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="capacity" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The capacity of the event journal. The capacity is the total number of items that the event journal
                        can hold at any moment. The actual number of items contained in the journal can be lower.
                        Its default value is 10000. The capacity is shared equally between all partitions.
                        This is done by assigning each partition getCapacity() / partitionCount
                        available slots in the event journal. Because of this, the effective total
                        capacity may be somewhat lower and you must take into account that the
                        configured capacity is at least greater than the partition count.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of seconds for each entry to stay in the event journal.
                        Entries that are older than &lt;time-to-live-seconds&gt; are evicted from the journal.
                        Any integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if the event journal is enabled, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="merkle-tree">
        <xs:annotation>
            <xs:documentation>
                Configuration for a merkle tree.
                The merkle tree is a data structure used for efficient comparison of the
                difference in the contents of large data structures. The precision of
                such a comparison mechanism is defined by the depth of the merkle tree.
                A larger depth means that a data synchronization mechanism will be able
                to pinpoint a smaller subset of the data structure contents in which a
                change occurred. This causes the synchronization mechanism to be more
                efficient. On the other hand, a larger tree depth means the merkle tree
                will consume more memory.
                A smaller depth means the data synchronization mechanism will have to
                transfer larger chunks of the data structure in which a possible change
                happened. On the other hand, a shallower tree consumes less memory.
                The depth must be between 2 and 27 (exclusive).
                As the comparison mechanism is iterative, a larger depth will also prolong
                the duration of the comparison mechanism. Care must be taken to not have
                large tree depths if the latency of the comparison operation is high.
                The default depth is 10.
                See https://en.wikipedia.org/wiki/Merkle_tree.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="depth" type="xs:unsignedInt" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The depth of the merkle tree.
                        A larger depth means that a data synchronization mechanism will be able
                        to pinpoint a smaller subset of the data structure contents in which a
                        change occurred. This causes the synchronization mechanism to be more
                        efficient. On the other hand, a larger tree depth means the merkle tree
                        will consume more memory.
                        A smaller depth means the data synchronization mechanism will have to
                        transfer larger chunks of the data structure in which a possible change
                        happened. On the other hand, a shallower tree consumes less memory.
                        The depth must be between 2 and 27 (exclusive). The default depth is 10.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if the merkle tree is enabled, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="wan-replication-ref-filters">
        <xs:sequence>
            <xs:element name="filter-impl" type="xs:string" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="wan-queue-full-behavior">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="DISCARD_AFTER_MUTATION"/>
            <xs:enumeration value="THROW_EXCEPTION"/>
            <xs:enumeration value="THROW_EXCEPTION_ONLY_IF_REPLICATION_ACTIVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="wan-acknowledge-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="ACK_ON_OPERATION_COMPLETE"/>
            <xs:enumeration value="ACK_ON_RECEIPT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="flake-id-generator">
        <xs:all>
            <xs:element name="prefetch-count" minOccurs="0" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Sets how many IDs are pre-fetched on the background when one call to
                        FlakeIdGenerator.newId() is made. Value must be in the range 1..100,000, default
                        is 100.

                        This setting pertains only to newId() calls made on the member that configured it.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:int">
                        <xs:minInclusive value="1" />
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>

            <xs:element name="prefetch-validity-millis" type="xs:long" minOccurs="0" default="600000">
                <xs:annotation>
                    <xs:documentation>
                        Sets for how long the pre-fetched IDs can be used. If this time elapses, a new batch of IDs
                        will be fetched. Time unit is milliseconds, default is 600,000 (10 minutes).

                        The IDs contain timestamp component, which ensures rough global ordering of IDs. If an
                        ID is assigned to an object that was created much later, it will be much out of order. If you
                        don't care about ordering, set this value to 0.

                        This setting pertains only to newId() calls made on the member that configured it.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="epoch-start" type="xs:long" minOccurs="0" default="1514764800000">
                <xs:annotation>
                    <xs:documentation>
                        Sets the offset of timestamp component. Time unit is milliseconds, default is 1514764800000
                        (1.1.2018 0:00 UTC).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="node-id-offset" type="xs:long" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the offset that will be added to the node ID assigned to cluster member for this generator.
                        Might be useful in A/B deployment scenarios where you have cluster A which you want to upgrade.
                        You create cluster B and for some time both will generate IDs and you want to have them unique.
                        In this case, configure node ID offset for generators on cluster B.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

            <xs:element name="bits-sequence" minOccurs="0" default="6">
                <xs:annotation>
                    <xs:documentation>
                        Sets the bit-length of the sequence component, default is 6 bits.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:int">
                        <xs:minInclusive value="0" />
                        <xs:maxInclusive value="63"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>

            <xs:element name="bits-node-id" minOccurs="0" default="16">
                <xs:annotation>
                    <xs:documentation>
                        Sets the bit-length of node id component. Default value is 16 bits.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:int">
                        <xs:minInclusive value="0" />
                        <xs:maxInclusive value="63"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>

            <xs:element name="allowed-future-millis" minOccurs="0" default="15000">
                <xs:annotation>
                    <xs:documentation>
                        Sets how far to the future is the generator allowed to go to generate IDs without blocking, default is 15 seconds.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:long">
                        <xs:minInclusive value="0"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>

            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the Flake ID Generator, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the ID generator.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="pn-counter">
        <xs:all>
            <xs:element name="replica-count" type="crdt-replica-count" minOccurs="0" maxOccurs="1" default="2147483647">
                <xs:annotation>
                    <xs:documentation>
                        Number of replicas on which the CRDT state will be kept. The updates are replicated
                        asynchronously between replicas.
                        The number must be greater than 1 and up to 2147483647 (Integer.MAX_VALUE).
                        The default value is 2147483647 (Integer.MAX_VALUE).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="split-brain-protection-ref" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Adds the Split Brain Protection for this data-structure which you configure using the split-brain-protection element.
                        You should set the split-brain-protection-ref's value as the split brain protection's name.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statistics-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="true">
                <xs:annotation>
                    <xs:documentation>
                        True (default) if statistics gathering is enabled on the PN counter, false otherwise.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the PN counter.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="initial-publisher-state">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="REPLICATING"/>
            <xs:enumeration value="PAUSED"/>
            <xs:enumeration value="STOPPED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="consistency-check-strategy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="MERKLE_TREES"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="memcache-protocol">
        <xs:annotation>
            <xs:documentation>
                Allows to configure Memcache text protocol.
            </xs:documentation>
        </xs:annotation>
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the Memcache text protocol support is enabled. Default value is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="advanced-network">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="join" type="join" minOccurs="0" maxOccurs="1"/>
            <xs:element name="failure-detector" type="failure-detector" minOccurs="0" maxOccurs="1"/>
            <xs:element name="member-address-provider" type="member-address-provider" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        IMPORTANT
                        This configuration is not intended to provide addresses of other cluster members with
                        which the hazelcast instance will form a cluster. This is an SPI for advanced use in
                        cases where the DefaultAddressPicker does not pick suitable addresses to bind to
                        and publish to other cluster members. For instance, this could allow easier
                        deployment in some cases when running on Docker, AWS or other cloud environments.
                        That said, if you are just starting with Hazelcast, you will probably want to
                        set the member addresses by using the tcp-ip or multicast configuration
                        or adding a discovery strategy.
                        Member address provider allows to plug in own strategy to customize:
                        1. What address Hazelcast will bind to
                        2. What address Hazelcast will advertise to other members on which they can bind to

                        In most environments you don't need to customize this and the default strategy will work just
                        fine. However in some cloud environments the default strategy does not make the right choice and
                        the member address provider delegates the process of address picking to external code.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="wan-endpoint-config" type="endpoint-config" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="wan-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="member-server-socket-endpoint-config" type="server-socket-endpoint-config"/>
            <xs:element name="client-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0" maxOccurs="1"/>
            <xs:element name="rest-server-socket-endpoint-config" type="rest-server-socket-endpoint-config" minOccurs="0" maxOccurs="1"/>
            <xs:element name="memcache-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0" maxOccurs="1"/>
        </xs:choice>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    Indicates whether the advanced network configuration is enabled or not. Default is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="rest-api">
        <xs:annotation>
            <xs:documentation>
                Controls access to Hazelcast HTTP REST API.
                The methods available through REST API are grouped to several REST endpoint groups.
                There are 2 levels of configuration:
                 - a global switch which enables/disables access to the REST API;
                 - REST endpoint group level switch. It is used to check which groups are allowed once the global switch is enabled.
            </xs:documentation>
        </xs:annotation>
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="endpoint-group" type="endpoint-group" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables named REST enpoint group.
                        If a group is not listed within the rest-api configuration, then it's 'enabledByDefault' flag is used
                        to control the behavior of the group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the HTTP REST API is enabled. Default value is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="endpoint-group">
        <xs:attribute name="name" type="endpoint-group-name" use="required"/>
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
    </xs:complexType>
    <xs:simpleType name="endpoint-group-name">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CLUSTER_READ"/>
            <xs:enumeration value="CLUSTER_WRITE"/>
            <xs:enumeration value="HEALTH_CHECK"/>
            <xs:enumeration value="HOT_RESTART"/>
            <xs:enumeration value="WAN"/>
            <xs:enumeration value="DATA"/>
            <xs:enumeration value="CP"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="endpoint-config">
        <xs:all>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        By default, Hazelcast lets the system pick up an ephemeral port during socket bind operation.
                        But security policies/firewalls may require to restrict outbound ports to be used by
                        Hazelcast-enabled applications. To fulfill this requirement, you can configure Hazelcast to use
                        only defined outbound ports.
                        outbound-ports has the ports attribute to allow you to define outbound ports.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="factory-class-with-properties" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0"/>
            <xs:element name="socket-options" type="socket-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="">
            <xs:annotation>
                <xs:documentation>
                    Name of the endpoint configuration. When using MEMBER or CLIENT protocol types,
                    name is irrelevant as a single MEMBER and CLIENT endpoint config is allowed.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="server-socket-endpoint-config">
        <xs:all>
            <xs:element name="port" type="port" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ports which Hazelcast will use to communicate between cluster members. Its default value is
                        5701.
                        It has the following attributes.
                        port-count: The default value is 100, meaning that Hazelcast will try to bind 100 ports.
                        If you set the value of port as 5701, as members join the cluster, Hazelcast tries to find
                        ports between 5701 and 5801. You can change the port count in cases like having large
                        instances on a single machine or you are willing to have only a few ports assigned.
                        auto-increment: Default value is true. If port is set to 5701, Hazelcast will try to find free
                        ports between 5701 and 5801. Normally, you will not need to change this value, but it comes
                        in handy when needed. You may also want to choose to use only one port. In that case, you can
                        disable the auto-increment feature of port by setting its value as false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="public-address" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Overrides the public address of a node. By default, a node selects its socket address
                        as its public address. But behind a network address translation (NAT), two endpoints (nodes)
                        may not be able to see/access each other. If both nodes set their public addresses to their
                        defined addresses on NAT, then they can communicate with each other. In this case, their
                        public addresses are not an address of a local network interface but a virtual address defined by
                        NAT.
                        This is optional to set and useful when you have a private cloud.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reuse-address" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        When you shutdown a cluster member, the server socket port will be in the TIME_WAIT
                        state for the next couple of minutes. If you start the member right after shutting it down,
                        you may not be able to bind it to the same port because it is in the TIME_WAIT state. If you
                        set reuse-address to true, the TIME_WAIT state is ignored and you can bind the member to the
                        same port again. Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        By default, Hazelcast lets the system pick up an ephemeral port during socket bind operation.
                        But security policies/firewalls may require to restrict outbound ports to be used by
                        Hazelcast-enabled applications. To fulfill this requirement, you can configure Hazelcast to use
                        only defined outbound ports.
                        outbound-ports has the ports attribute to allow you to define outbound ports.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="factory-class-with-properties" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0"/>
            <xs:element name="socket-options" type="socket-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="">
            <xs:annotation>
                <xs:documentation>
                    Name of the endpoint configuration. Only relevant when defining WAN server sockets.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="socket-options">
        <xs:all>
            <xs:element name="buffer-direct" type="xs:boolean" minOccurs="0"/>
            <xs:element name="tcp-no-delay" type="xs:boolean" minOccurs="0"/>
            <xs:element name="keep-alive" type="xs:boolean" minOccurs="0"/>
            <xs:element name="connect-timeout-seconds" type="xs:nonNegativeInteger" minOccurs="0"/>
            <xs:element name="send-buffer-size-kb" type="xs:positiveInteger" minOccurs="0"/>
            <xs:element name="receive-buffer-size-kb" type="xs:positiveInteger" minOccurs="0"/>
            <xs:element name="linger-seconds" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="rest-server-socket-endpoint-config">
        <xs:annotation>
            <xs:documentation>
                Controls access to Hazelcast HTTP REST API.
                The methods available through REST API are grouped to several REST endpoint groups, which can be specified
                in this section.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="port" type="port" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The ports which Hazelcast will use to communicate between cluster members. Its default value is
                        5701.
                        It has the following attributes.
                        port-count: The default value is 100, meaning that Hazelcast will try to bind 100 ports.
                        If you set the value of port as 5701, as members join the cluster, Hazelcast tries to find
                        ports between 5701 and 5801. You can change the port count in cases like having large
                        instances on a single machine or you are willing to have only a few ports assigned.
                        auto-increment: Default value is true. If port is set to 5701, Hazelcast will try to find free
                        ports between 5701 and 5801. Normally, you will not need to change this value, but it comes
                        in handy when needed. You may also want to choose to use only one port. In that case, you can
                        disable the auto-increment feature of port by setting its value as false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="public-address" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Overrides the public address of a node. By default, a node selects its socket address
                        as its public address. But behind a network address translation (NAT), two endpoints (nodes)
                        may not be able to see/access each other. If both nodes set their public addresses to their
                        defined addresses on NAT, then they can communicate with each other. In this case, their
                        public addresses are not an address of a local network interface but a virtual address defined by
                        NAT.
                        This is optional to set and useful when you have a private cloud.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reuse-address" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        When you shutdown a cluster member, the server socket port will be in the TIME_WAIT
                        state for the next couple of minutes. If you start the member right after shutting it down,
                        you may not be able to bind it to the same port because it is in the TIME_WAIT state. If you
                        set reuse-address to true, the TIME_WAIT state is ignored and you can bind the member to the
                        same port again. Default value is false.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        By default, Hazelcast lets the system pick up an ephemeral port during socket bind operation.
                        But security policies/firewalls may require to restrict outbound ports to be used by
                        Hazelcast-enabled applications. To fulfill this requirement, you can configure Hazelcast to use
                        only defined outbound ports.
                        outbound-ports has the ports attribute to allow you to define outbound ports.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="factory-class-with-properties" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0"/>
            <xs:element name="socket-options" type="socket-options" minOccurs="0"/>
            <xs:element name="endpoint-groups" type="endpoint-groups" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables named REST endpoint groups.
                        If a group is not listed within the rest-api configuration, then it's 'enabledByDefault' flag is used
                        to control the behavior of the group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="optional" default="">
            <xs:annotation>
                <xs:documentation>
                    Name of the endpoint configuration. When using MEMBER or CLIENT protocol types,
                    name is irrelevant as a single MEMBER and CLIENT endpoint config is allowed.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="endpoint-groups">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="endpoint-group" type="endpoint-group">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables named REST endpoint group.
                        If a group is not listed within the rest-api configuration, then it's 'enabledByDefault' flag is used
                        to control the behavior of the group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="permission-on-join-operation">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="RECEIVE"/>
            <xs:enumeration value="SEND"/>
            <xs:enumeration value="NONE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="cp-subsystem">
        <xs:all>
            <xs:element name="cp-member-count" type="xs:unsignedInt" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of CP members to initialize CP Subsystem. It is 0 by default,
                        meaning that CP Subsystem is disabled. CP Subsystem is enabled when
                        a positive value is set. After CP Subsystem is initialized successfully,
                        more CP members can be added at run-time and the number of active CP
                        members can go beyond the configured CP member count. The number of CP
                        members can be smaller than total member count of the Hazelcast cluster.
                        For instance, you can run 5 CP members in a Hazelcast cluster of
                        20 members. If set, must be greater than or equal to group-size.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="group-size" type="cp-group-size" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of CP members to form CP groups. If set, it must be an odd
                        number between 3 and 7.
                        Otherwise, cp-member-count is respected while forming CP groups.
                        If set, must be smaller than or equal to cp-member-count.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="session-time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="60">
                <xs:annotation>
                    <xs:documentation>
                        Duration for a CP session to be kept alive after the last received
                        session heartbeat. A CP session is closed if no session heartbeat is
                        received during this duration. Session TTL must be decided wisely. If
                        a very small value is set, a CP session can be closed prematurely if
                        its owner Hazelcast instance temporarily loses connectivity to CP
                        Subsystem because of a network partition or a GC pause. In such an
                        occasion, all CP resources of this Hazelcast instance, such as
                        FencedLock or ISemaphore, are released. On the other
                        hand, if a very large value is set, CP resources can remain assigned to
                        an actually crashed Hazelcast instance for too long and liveliness
                        problems can occur. CP Subsystem offers an API in
                        CPSessionManagementService to deal with liveliness issues
                        related to CP sessions. In order to prevent premature session expires,
                        session TTL configuration can be set a relatively large value and
                        CPSessionManagementService#forceCloseSession(String, long)
                        can be manually called to close CP session of a crashed Hazelcast
                        instance.
                        Must be greater than session-heartbeat-interval-seconds, and
                        smaller than or equal to missing-cp-member-auto-removal-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="session-heartbeat-interval-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="5">
                <xs:annotation>
                    <xs:documentation>
                        Interval for the periodically-committed CP session heartbeats.
                        A CP session is started on a CP group with the first session-based
                        request of a Hazelcast instance. After that moment, heartbeats are
                        periodically committed to the CP group.
                        Must be smaller than session-time-to-live-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="missing-cp-member-auto-removal-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Duration to wait before automatically removing a missing CP member from
                        CP Subsystem. When a CP member leaves the Hazelcast cluster, it is not
                        automatically removed from CP Subsystem, since it could be still alive
                        and left the cluster because of a network problem. On the other hand,
                        if a missing CP member actually crashed, it creates a danger for CP
                        groups, because it is still part of majority calculations. This
                        situation could lead to losing majority of CP groups if multiple CP
                        members leave the cluster over time.
                        With the default configuration, missing CP members are automatically
                        removed from CP Subsystem after 4 hours. This feature is very useful
                        in terms of fault tolerance when CP member count is also configured
                        to be larger than group size. In this case, a missing CP member is
                        safely replaced in its CP groups with other available CP members
                        in CP Subsystem. This configuration also implies that no network
                        partition is expected to be longer than the configured duration.
                        If a missing CP member comes back alive after it is removed from CP
                        Subsystem with this feature, that CP member must be terminated manually.
                        Must be greater than or equal to session-time-to-live-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fail-on-indeterminate-operation-state" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Offers a choice between at-least-once and at-most-once execution
                        of operations on top of the Raft consensus algorithm. It is disabled by
                        default and offers at-least-once execution guarantee. If enabled, it
                        switches to at-most-once execution guarantee. When you invoke an API
                        method on a CP data structure proxy, it sends an internal operation
                        to the corresponding CP group. After this operation is committed on
                        the majority of this CP group by the Raft leader node, it sends
                        a response for the public API call. If a failure causes loss of
                        the response, then the calling side cannot determine if the operation is
                        committed on the CP group or not. In this case, if this configuration is
                        disabled, the operation is replicated again to the CP group, and hence
                        could be committed multiple times. If it is enabled, the public API call
                        fails with com.hazelcast.core.IndeterminateOperationStateException.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="persistence-enabled" type="xs:boolean" minOccurs="0" maxOccurs="1" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Flag to denote whether or not CP Subsystem Persistence is enabled.
                        If enabled, CP members persist their local CP data to stable storage and
                        can recover from crashes.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="base-dir" type="xs:string" minOccurs="0" maxOccurs="1" default="cp-data">
                <xs:annotation>
                    <xs:documentation>
                        Base directory to store all CP data when persistence-enabled
                        is true. This directory can be shared between multiple CP members.
                        Each CP member creates a unique directory for itself under the base
                        directory. This is especially useful for cloud environments where CP
                        members generally use a shared filesystem.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data-load-timeout-seconds" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="120">
                <xs:annotation>
                    <xs:documentation>
                        Timeout duration for CP members to restore their data from disk.
                        A CP member fails its startup if it cannot complete its CP data restore
                        process in the configured duration.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="raft-algorithm" type="raft-algorithm" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Contains configuration options for Hazelcast's Raft consensus algorithm implementation
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="semaphores" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Configurations for CP semaphore instances
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="semaphore" type="semaphore" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="locks" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Configurations for FencedLock instances
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="fenced-lock" type="fenced-lock" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="raft-algorithm">
        <xs:all>
            <xs:element name="leader-election-timeout-in-millis" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="2000">
                <xs:annotation>
                    <xs:documentation>
                        Leader election timeout in milliseconds. If a candidate cannot win
                        majority of the votes in time, a new leader election round is initiated.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="leader-heartbeat-period-in-millis" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="5000">
                <xs:annotation>
                    <xs:documentation>
                        Duration in milliseconds for a Raft leader node to send periodic
                        heartbeat messages to its followers in order to denote its liveliness.
                        Periodic heartbeat messages are actually append entries requests and
                        can contain log entries for the lagging followers. If a too small value
                        is set, heartbeat messages are sent from Raft leaders to followers too
                        frequently and it can cause an unnecessary usage of CPU and network.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-missed-leader-heartbeat-count" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="5">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of missed Raft leader heartbeats for a follower to
                        trigger a new leader election round. For instance, if
                        leader-heartbeat-period-in-millis is 1 second and this value is set
                        to 5, then a follower triggers a new leader election round if 5 seconds
                        pass after the last heartbeat message of the current Raft leader node.
                        If this duration is too small, new leader election rounds can be
                        triggered unnecessarily if the current Raft leader temporarily slows
                        down or a network congestion occurs. If it is too large, it takes longer
                        to detect failures of Raft leaders.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="append-request-max-entry-count" type="xs:unsignedInt" minOccurs="0" maxOccurs="1" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of Raft log entries that can be sent as a batch
                        in a single append entries request. In Hazelcast's Raft consensus
                        algorithm implementation, a Raft leader maintains a separate replication
                        pipeline for each follower. It sends a new batch of Raft log entries to
                        a follower after the follower acknowledges the last append entries
                        request sent by the leader.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="commit-index-advance-count-to-snapshot" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"
                        default="10000">
                <xs:annotation>
                    <xs:documentation>
                        Number of new commits to initiate a new snapshot after the last snapshot
                        taken by the local Raft node. This value must be configured wisely as it
                        effects performance of the system in multiple ways. If a small value is
                        set, it means that snapshots are taken too frequently and Raft nodes keep
                        a very short Raft log. If snapshots are large and CP Subsystem
                        Persistence is enabled, this can create an unnecessary overhead on IO
                        performance. Moreover, a Raft leader can send too many snapshots to
                        followers and this can create an unnecessary overhead on network.
                        On the other hand, if a very large value is set, it can create a memory
                        overhead since Raft log entries are going to be kept in memory until
                        the next snapshot.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="uncommitted-entry-count-to-reject-new-appends" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"
                        default="100">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of uncommitted log entries in the leader's Raft log
                        before temporarily rejecting new requests of callers. Since Raft leaders
                        send log entries to followers in batches, they accumulate incoming
                        requests in order to improve the throughput. You can configure this
                        field by considering your degree of concurrency in your callers.
                        For instance, if you have at most 1000 threads sending requests to
                        a Raft leader, you can set this field to 1000 so that callers do not
                        get retry responses unnecessarily.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="append-request-backoff-timeout-in-millis" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"
                        default="100">
                <xs:annotation>
                    <xs:documentation>
                        Timeout duration in milliseconds to apply backoff on append entries
                        requests. After a Raft leader sends an append entries request to
                        a follower, it will not send a subsequent append entries request either
                        until the follower responds or this timeout occurs. Backoff durations
                        are increased exponentially if followers remain unresponsive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="semaphore">
        <xs:all>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Name of the CP semaphore
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="jdk-compatible" type="xs:boolean" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Enables / disables JDK compatibility of CP ISemaphore.
                        When it is JDK compatible, just as in the Semaphore#release()
                        method, a permit can be released without acquiring it first, because
                        acquired permits are not bound to threads. However, there is no
                        auto-cleanup mechanism for acquired permits upon Hazelcast
                        server / client failures. If a permit holder fails, its permits must be
                        released manually. When JDK compatibility is disabled,
                        a HazelcastInstance must acquire permits before releasing them
                        and it cannot release a permit that it has not acquired. It means, you
                        can acquire a permit from one thread and release it from another thread
                        using the same HazelcastInstance, but not different
                        HazelcastInstances. In this mode, acquired permits are
                        automatically released upon failure of the holder
                        HazelcastInstance. So there is a minor behavioral difference
                        to the Semaphore#release() method.
                        JDK compatibility is disabled by default.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="initial-permits" type="xs:unsignedInt" minOccurs="0" maxOccurs="1"
                        default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of permits to initialize the Semaphore. If a positive value is
                        set, the Semaphore is initialized with the given number of permits.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="fenced-lock">
        <xs:all>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Name of the FencedLock
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lock-acquire-limit" type="xs:nonNegativeInteger" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of reentrant lock acquires. Once a caller acquires
                        the lock this many times, it will not be able to acquire the lock again,
                        until it makes at least one unlock() call.
                        By default, no upper bound is set for the number of reentrant lock
                        acquires, which means that once a caller acquires a FencedLock,
                        all of its further lock() calls will succeed. However, for instance,
                        if you set lock-acquire-limit to 2, once a caller acquires
                        the lock, it will be able to acquire it once more, but its third lock()
                        call will not succeed.
                        If lock-acquire-limit is set to 1, then the lock becomes non-reentrant.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="metrics">
        <xs:all>
            <xs:element name="management-center" type="metrics-management-center" minOccurs="0"/>
            <xs:element name="jmx" type="metrics-jmx" minOccurs="0"/>
            <xs:element name="collection-frequency-seconds" type="xs:unsignedInt" default="5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the metrics collection frequency in seconds.
                        By default, metrics are collected every 5 seconds.
                        May be overridden by 'hazelcast.metrics.collection.frequency'
                        system property.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Master-switch for the metrics system. Controls whether
                    the metrics are collected and publishers are enabled.
                    May be overridden by 'hazelcast.metrics.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="metrics-management-center">
        <xs:all>
            <xs:element name="retention-seconds" type="xs:unsignedInt" default="5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the number of seconds the metrics will be retained on the
                        instance. By default, metrics are retained for 5 seconds (that is for
                        one collection of metrics values, if default "collection-frequency-seconds"
                        collection frequency is used). More retention means more heap memory, but
                        allows for longer client hiccups without losing a value (for example to
                        restart the Management Center).
                        May be overridden by 'hazelcast.metrics.mc.retention'
                        system property.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Controls whether the metrics collected are exposed to
                    Hazelcast Management Center. It is enabled by default.
                    Please note that the metrics are polled by the
                    Hazelcast Management Center, hence the members need to
                    buffer the collected metrics between two polls. The aim
                    for this switch is to reduce memory consumption of the
                    metrics system if the Hazelcast Management Center is not
                    used.
                    In order to expose the metrics, the metrics system need
                    to be enabled via the enabled master-switch attribute.
                    May be overridden by 'hazelcast.metrics.mc.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="metrics-jmx">
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Controls whether the metrics collected are exposed to
                    through JMX. It is enabled by default.
                    In order to expose the metrics, the metrics system need
                    to be enabled via the enabled master-switch attribute.
                    May be overridden by 'hazelcast.metrics.jmx.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="instance-tracking">
        <xs:annotation>
            <xs:documentation>
                Configures tracking of a running Hazelcast instance. For now, this is
                limited to writing information about the Hazelcast instance to a file
                while the instance is starting.
                The file is overwritten on every start of the Hazelcast instance and if
                multiple instance share the same file system, every instance will
                overwrite the tracking file of a previously started instance.
                If this instance is unable to write the file, the exception is logged and
                the instance is allowed to start.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="file-name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the name of the file which will contain the tracking metadata. If left unset
                        a file named "Hazelcast.process" will be created in the directory as returned by
                        System.getProperty("java.io.tmpdir").
                        The filename can contain placeholders that will be resolved in the same way
                        as placeholders for the format pattern.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="format-pattern" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the pattern used to render the contents of the instance tracking file.
                        It may contain placeholders for these properties:
                        - "product": The instance product name, e.g. "Hazelcast" or "Hazelcast Enterprise".
                        - "version": The instance version.
                        - "mode": The instance mode which can be "server", "embedded" or "client".
                        - "start_timestamp": The timestamp of when the instance was started expressed the difference,
                        measured in milliseconds, between the current time and midnight, January 1, 1970 UTC
                        - "licensed": If this instance is using a license or not. The value 0 signifies
                        that there is no license set and the value 1 signifies that a license is in use.
                        - "pid": Attempts to get the process ID value. The algorithm does not guarantee to get the
                        process ID on all JVMs and operating systems so please test before use. In case we are unable to
                        get the PID, the value will be -1.

                        The placeholders are defined by a $HZ_INSTANCE_TRACKING{ prefix and followed by }.
                        For instance, a placeholder for the "start_timestamp" would be
                        $HZ_INSTANCE_TRACKING{start_timestamp}.
                        The placeholders are resolved in a fail-safe manner. Any incorrect syntax
                        is ignored and only the known properties are resolved, placeholders for
                        any parameters which do not have defined values will be ignored. This also
                        means that if there is a missing closing bracket in one of the placeholders,
                        the property name will be resolved as anything from the opening bracket
                        to the next closing bracket, which might contain additional opening brackets.
                        If unset, a JSON formatted output will be used.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    Enables or disables instance tracking.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="sql">
        <xs:annotation>
            <xs:documentation>
                SQL service configuration.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="executor-pool-size" type="xs:int" minOccurs="0" maxOccurs="1" default="-1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the number of threads responsible for execution of SQL statements.
                        The default value -1 sets the pool size equal to the number of CPU cores, and should be good enough
                        for the most workloads.
                        Setting the value to less than the number of CPU cores will limit the degree of parallelism of the SQL
                        subsystem. This may be beneficial if you would like to prioritize other CPU-intensive workloads on the
                        same machine.
                        It is not recommended to set the value of this parameter greater than the number of CPU cores because it
                        may decrease the system's overall performance due to excessive context switches.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="operation-pool-size" type="xs:int" minOccurs="0" maxOccurs="1" default="-1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the number of threads responsible for network operations processing.
                        When Hazelcast members execute a statement, they send commands to each other over the network to
                        coordinate the execution. This includes requests to start or stop execution, or a request to process a
                        batch of data. These commands are processed in a separate operation thread pool, to avoid frequent
                        interruption of running SQL fragments.
                        The default value -1 sets the pool size equal to the number of CPU cores, and should be good enough
                        for the most workloads.
                        Setting the value to less than the number of CPU cores may improve the overall performance on machines
                        with large CPU count, because it will decrease the number of context switches.
                        It is not recommended to set the value of this parameter greater than the number of CPU cores because it
                        may decrease the system's overall performance due to excessive context switches.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statement-timeout-millis" type="xs:unsignedLong" minOccurs="0" maxOccurs="1" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the timeout in milliseconds that is applied to SQL statements without an explicit timeout.
                        It is possible to set a timeout through the SqlStatement.setTimeout(long) method. If the statement
                        timeout is not set, then the value of this parameter will be used.
                        Zero value means no timeout. Negative values are prohibited.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="realms">
        <xs:sequence>
            <xs:element name="realm" type="realm" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="realm">
        <xs:all>
            <xs:element name="authentication" type="authentication" minOccurs="0" maxOccurs="1"/>
            <xs:element name="identity" type="identity" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="authentication">
        <xs:choice>
            <xs:element name="jaas" type="login-modules"/>
            <xs:element name="tls" type="tls-authentication"/>
            <xs:element name="ldap" type="ldap-authentication"/>
            <xs:element name="kerberos" type="kerberos-authentication"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="basic-authentication">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="skip-identity" type="xs:boolean" minOccurs="0"/>
            <xs:element name="skip-endpoint" type="xs:boolean" minOccurs="0"/>
            <xs:element name="skip-role" type="xs:boolean" minOccurs="0"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="tls-authentication">
        <xs:complexContent>
            <xs:extension base="basic-authentication">
                <xs:attribute name="roleAttribute" type="xs:string" use="optional" default="cn" />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="ldap-authentication">
        <xs:complexContent>
            <xs:extension base="basic-authentication">
                <xs:choice maxOccurs="unbounded">
                    <xs:element name="url" type="xs:string"/>
                    <xs:element name="socket-factory-class-name" type="xs:string" minOccurs="0"/>

                    <xs:element name="parse-dn" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="role-context" type="xs:string" minOccurs="0"/>
                    <xs:element name="role-filter" type="xs:string" minOccurs="0"/>
                    <xs:element name="role-mapping-attribute" type="xs:string" minOccurs="0"/>
                    <xs:element name="role-mapping-mode" type="ldap-role-mapping-mode" minOccurs="0"/>
                    <xs:element name="role-name-attribute" type="xs:string" minOccurs="0"/>
                    <xs:element name="role-recursion-max-depth" type="xs:int" minOccurs="0"/>
                    <xs:element name="role-search-scope" type="ldap-search-scope" minOccurs="0"/>
                    <xs:element name="user-name-attribute" type="xs:string" minOccurs="0"/>

                    <xs:element name="system-user-dn" type="xs:string" minOccurs="0"/>
                    <xs:element name="system-user-password" type="xs:string" minOccurs="0"/>
                    <xs:element name="system-authentication" type="xs:string" minOccurs="0"/>
                    <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
                    <xs:element name="password-attribute" type="xs:string" minOccurs="0"/>
                    <xs:element name="user-context" type="xs:string" minOccurs="0"/>
                    <xs:element name="user-filter" type="xs:string" minOccurs="0"/>
                    <xs:element name="user-search-scope" type="ldap-search-scope" minOccurs="0"/>
                    <xs:element name="skip-authentication" type="xs:boolean" minOccurs="0"/>
                </xs:choice>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="ldap-role-mapping-mode">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="attribute"/>
            <xs:enumeration value="direct"/>
            <xs:enumeration value="reverse"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ldap-search-scope">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="object"/>
            <xs:enumeration value="one-level"/>
            <xs:enumeration value="subtree"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="kerberos-authentication">
        <xs:complexContent>
            <xs:extension base="basic-authentication">
                <xs:choice maxOccurs="unbounded">
                    <xs:element name="relax-flags-check" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="use-name-without-realm" type="xs:boolean" minOccurs="0"/>
                    <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
                    <xs:element name="principal" type="xs:string" minOccurs="0"/>
                    <xs:element name="keytab-file" type="xs:string" minOccurs="0"/>
                    <xs:element name="ldap" type="ldap-authentication" minOccurs="0"/>
                </xs:choice>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="identity">
        <xs:choice>
            <xs:element name="username-password" type="username-password"/>
            <xs:element name="credentials-factory" type="security-object"/>
            <xs:element name="token" type="token"/>
            <xs:element name="kerberos" type="kerberos-identity"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="kerberos-identity">
        <xs:all>
            <xs:element name="realm" type="xs:string" minOccurs="0"/>
            <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
            <xs:element name="principal" type="xs:string" minOccurs="0"/>
            <xs:element name="keytab-file" type="xs:string" minOccurs="0"/>
            <xs:element name="service-name-prefix" type="xs:string" minOccurs="0"/>
            <xs:element name="spn" type="xs:string" minOccurs="0"/>
            <xs:element name="use-canonical-hostname" type="xs:boolean" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="username-password">
        <xs:attribute name="username" type="xs:string" use="required" />
        <xs:attribute name="password" type="xs:string" use="required" />
    </xs:complexType>
    <xs:complexType name="token">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="encoding">
                    <xs:simpleType>
                        <xs:restriction base="non-space-string">
                            <xs:enumeration value="none"/>
                            <xs:enumeration value="base64"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="realm-reference">
        <xs:attribute name="realm" type="xs:string" use="required"/>
    </xs:complexType>
</xs:schema>
