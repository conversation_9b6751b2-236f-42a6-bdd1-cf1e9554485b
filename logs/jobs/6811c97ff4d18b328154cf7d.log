[TRACE] 2025-04-30 06:56:47.630 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 06:56:47.836 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 06:56:47.988 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 06:56:48.716 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-04-30 06:56:48.891 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 06:56:48.891 - [任务 1] - Task started 
[TRACE] 2025-04-30 06:56:49.015 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 06:56:49.016 - [任务 1][T kafka] - <PERSON>de T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 06:56:49.016 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 06:56:49.017 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 06:56:49.554 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 06:56:49.575 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 06:56:49.576 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 06:56:49.576 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 06:56:49.662 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 06:56:49.662 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 06:56:49.743 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 06:56:49.744 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 06:56:49.745 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 06:56:49.745 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 06:56:49.757 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 06:56:49.894 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 06:56:49.895 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 06:56:49.895 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 06:56:49.921 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 06:56:49.922 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 06:56:49.931 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 06:56:49.932 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 06:56:49.932 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 06:56:49.936 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 06:56:49.937 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 06:56:49.937 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 06:56:49.939 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 06:56:49.939 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 06:56:49.940 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 06:56:49.941 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 06:56:50.047 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 83111ec2-bbd4-4869-829c-2d941c2128b1 
[INFO ] 2025-04-30 06:56:50.048 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"83111ec2-bbd4-4869-829c-2d941c2128b1","offset":{"{\"server\":\"83111ec2-bbd4-4869-829c-2d941c2128b1\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 995459801
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 83111ec2-bbd4-4869-829c-2d941c2128b1
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-83111ec2-bbd4-4869-829c-2d941c2128b1
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 83111ec2-bbd4-4869-829c-2d941c2128b1
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 06:56:50.455 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 06:56:51.875 - [任务 1][T kafka] - Created topic '_kafka_test_2': true 
[INFO ] 2025-04-30 06:56:52.084 - [任务 1][T kafka] - Created topic '_kafka_test': true 
[INFO ] 2025-04-30 06:56:52.604 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 06:56:52.693 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 06:56:52.693 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 06:56:52.693 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 06:58:06.091 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 06:58:06.093 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 06:58:06.093 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 06:58:06.093 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 06:58:06.105 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996209521 
[TRACE] 2025-04-30 06:58:06.105 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996209521 
[TRACE] 2025-04-30 06:58:06.107 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 06:58:06.107 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 06:58:06.110 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 55 ms 
[TRACE] 2025-04-30 06:58:06.110 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 06:58:06.133 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 06:58:06.133 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996209472 
[TRACE] 2025-04-30 06:58:06.134 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996209472 
[TRACE] 2025-04-30 06:58:06.134 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 06:58:06.135 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 06:58:06.135 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 25 ms 
[TRACE] 2025-04-30 06:58:07.618 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 06:58:07.619 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3d07241a 
[TRACE] 2025-04-30 06:58:07.748 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 06:58:07.748 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 06:58:07.750 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 06:58:07.750 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 06:58:12.757 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 06:58:12.758 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3d07241a 
[TRACE] 2025-04-30 06:58:12.758 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 06:58:12.922 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 06:58:12.922 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 06:58:12.922 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:01:24.645 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:01:24.645 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:01:25.007 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:01:25.007 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:01:25.232 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:01:25.232 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:01:25.273 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:01:25.273 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:01:25.273 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:01:25.273 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:01:25.886 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:01:25.906 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:01:25.906 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:01:25.907 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:02:11.506 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:02:11.619 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:02:11.620 - [任务 1][T kafka] - Created topic 'kafka_test_2': true 
[INFO ] 2025-04-30 07:02:11.621 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:02:11.622 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:02:11.622 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:02:11.622 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:02:11.696 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:02:11.696 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:02:11.704 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:02:11.704 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:02:11.704 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:02:11.719 - [任务 1][T kafka] - Created topic 'kafka_test': true 
[TRACE] 2025-04-30 07:02:11.725 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:02:11.726 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:02:11.726 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:02:11.731 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:02:11.731 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:02:11.731 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:02:11.731 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:02:11.731 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:02:11.732 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:02:11.732 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:02:11.732 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:02:11.770 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: c797c9ef-3e83-4986-9629-1835cd22de4e 
[INFO ] 2025-04-30 07:02:11.771 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"c797c9ef-3e83-4986-9629-1835cd22de4e","offset":{"{\"server\":\"c797c9ef-3e83-4986-9629-1835cd22de4e\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1651169834
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c797c9ef-3e83-4986-9629-1835cd22de4e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c797c9ef-3e83-4986-9629-1835cd22de4e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: c797c9ef-3e83-4986-9629-1835cd22de4e
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:02:11.973 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:02:12.511 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:02:12.569 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:02:12.569 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:02:12.570 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:02:22.477 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:02:22.478 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:02:22.478 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:02:22.479 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:02:22.484 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996485745 
[TRACE] 2025-04-30 07:02:22.484 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996485745 
[TRACE] 2025-04-30 07:02:22.484 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:02:22.485 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:02:22.485 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 94 ms 
[TRACE] 2025-04-30 07:02:22.491 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:02:22.492 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:02:22.496 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996485802 
[TRACE] 2025-04-30 07:02:22.496 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996485802 
[TRACE] 2025-04-30 07:02:22.497 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:02:22.497 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:02:22.497 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 11 ms 
[TRACE] 2025-04-30 07:02:26.526 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:02:26.526 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d0a1e78 
[TRACE] 2025-04-30 07:02:26.657 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:02:26.657 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:02:26.657 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:02:26.657 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:02:26.661 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:02:26.661 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d0a1e78 
[TRACE] 2025-04-30 07:02:26.661 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:02:26.661 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:02:26.871 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:02:26.871 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[INFO ] 2025-04-30 07:02:52.474 - [任务 1] - This task already stopped. 
[TRACE] 2025-04-30 07:05:06.328 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:05:06.533 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:05:06.721 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:05:06.813 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:05:06.984 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:05:06.985 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:05:07.157 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:05:07.158 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:05:07.158 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:05:07.158 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:05:07.766 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:05:07.784 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:05:07.784 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:05:07.785 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:05:07.858 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:05:07.858 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:05:08.048 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:05:08.049 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:05:08.049 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:05:08.049 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:05:08.111 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:05:08.112 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:05:08.157 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:05:08.157 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:05:08.157 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:05:08.159 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 07:05:08.185 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:05:08.185 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:05:08.187 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:05:08.187 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:05:08.192 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:05:08.192 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:05:08.192 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:05:08.193 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:05:08.194 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:05:08.195 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:05:08.195 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:05:08.241 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 07:05:08.241 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: d6804b2a-c581-4084-9bd7-747a620484f5 
[INFO ] 2025-04-30 07:05:08.442 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"d6804b2a-c581-4084-9bd7-747a620484f5","offset":{"{\"server\":\"d6804b2a-c581-4084-9bd7-747a620484f5\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 669578096
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d6804b2a-c581-4084-9bd7-747a620484f5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-d6804b2a-c581-4084-9bd7-747a620484f5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: d6804b2a-c581-4084-9bd7-747a620484f5
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:05:08.526 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:05:08.929 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:05:09.097 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:05:09.097 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:05:09.097 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:05:35.314 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:05:35.314 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:05:35.315 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:05:35.315 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:05:35.326 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996707658 
[TRACE] 2025-04-30 07:05:35.326 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996707658 
[TRACE] 2025-04-30 07:05:35.327 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:05:35.328 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:05:35.329 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 29 ms 
[TRACE] 2025-04-30 07:05:35.329 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:05:35.347 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:05:35.347 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996707680 
[TRACE] 2025-04-30 07:05:35.347 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996707680 
[TRACE] 2025-04-30 07:05:35.348 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:05:35.348 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:05:35.552 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 18 ms 
[TRACE] 2025-04-30 07:05:38.532 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:05:38.535 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6fc2a6a3 
[TRACE] 2025-04-30 07:05:38.538 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:05:38.662 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:05:38.662 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:05:38.868 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:05:43.671 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:05:43.671 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6fc2a6a3 
[TRACE] 2025-04-30 07:05:43.671 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:05:43.672 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:05:43.732 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:05:43.732 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:09:25.508 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:09:25.713 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:09:25.806 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:09:25.983 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:09:25.983 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:09:26.189 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:09:26.208 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:09:26.208 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:09:26.208 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:09:26.208 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:09:26.734 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:09:26.769 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:09:26.770 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:09:26.770 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:09:26.865 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:09:26.866 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:09:27.104 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:09:27.105 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:09:27.105 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:09:27.105 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:09:27.209 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:09:27.209 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:09:27.263 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:09:27.263 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:09:27.263 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:09:27.265 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 07:09:27.293 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:09:27.293 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:09:27.295 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:09:27.295 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:09:27.300 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:09:27.300 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:09:27.300 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:09:27.301 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:09:27.301 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:09:27.303 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:09:27.303 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:09:27.358 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 07:09:27.359 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 8c0dcee7-449a-4a9b-93cf-82373649455f 
[INFO ] 2025-04-30 07:09:27.564 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"8c0dcee7-449a-4a9b-93cf-82373649455f","offset":{"{\"server\":\"8c0dcee7-449a-4a9b-93cf-82373649455f\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 460780577
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8c0dcee7-449a-4a9b-93cf-82373649455f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-8c0dcee7-449a-4a9b-93cf-82373649455f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 8c0dcee7-449a-4a9b-93cf-82373649455f
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:09:27.611 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:09:28.390 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:09:28.390 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:09:28.390 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:09:28.390 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:17:34.136 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:17:34.194 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:17:34.194 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:17:34.194 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:17:34.202 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996966628 
[TRACE] 2025-04-30 07:17:34.202 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745996966628 
[TRACE] 2025-04-30 07:17:34.204 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:17:34.205 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:17:34.208 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 72 ms 
[TRACE] 2025-04-30 07:17:34.208 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:17:34.249 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:17:34.249 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996966651 
[TRACE] 2025-04-30 07:17:34.250 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745996966651 
[TRACE] 2025-04-30 07:17:34.250 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:17:34.251 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:17:34.251 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 42 ms 
[TRACE] 2025-04-30 07:17:37.208 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:17:37.209 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4177c070 
[TRACE] 2025-04-30 07:17:37.339 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:17:37.339 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:17:37.340 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:17:37.341 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:17:42.353 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:17:42.354 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4177c070 
[TRACE] 2025-04-30 07:17:42.354 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:17:42.417 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:17:42.418 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:17:42.418 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:18:05.132 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:18:05.133 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:18:05.524 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:18:05.524 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:18:05.711 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:18:05.711 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:18:05.817 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:18:05.817 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:18:05.817 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 1 ms 
[TRACE] 2025-04-30 07:18:05.818 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:18:06.307 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:18:06.307 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:18:06.308 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:18:06.311 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:18:06.311 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:18:06.553 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:18:06.734 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:18:06.744 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:18:06.744 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:18:06.744 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:18:06.744 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:18:06.839 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:18:06.839 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:18:06.851 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:18:06.851 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:18:06.853 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 07:18:06.877 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:18:06.877 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 07:18:07.001 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:18:07.002 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:18:07.002 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:18:07.037 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:18:07.039 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:18:07.040 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:18:07.042 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:18:07.042 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:18:07.044 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:18:07.045 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:18:07.216 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 12016745-a354-4288-9a02-8b6aa69c2515 
[INFO ] 2025-04-30 07:18:07.216 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"12016745-a354-4288-9a02-8b6aa69c2515","offset":{"{\"server\":\"12016745-a354-4288-9a02-8b6aa69c2515\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1739828277
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 12016745-a354-4288-9a02-8b6aa69c2515
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-12016745-a354-4288-9a02-8b6aa69c2515
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 12016745-a354-4288-9a02-8b6aa69c2515
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2025-04-30 07:18:07.417 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:18:07.424 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:18:07.425 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:18:07.425 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:18:07.629 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[TRACE] 2025-04-30 07:18:22.056 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:18:22.168 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:18:22.169 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:18:22.169 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:18:22.174 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997486271 
[TRACE] 2025-04-30 07:18:22.174 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997486271 
[TRACE] 2025-04-30 07:18:22.175 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:18:22.175 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:18:22.176 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 122 ms 
[TRACE] 2025-04-30 07:18:22.176 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:18:22.191 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:18:22.191 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997486221 
[TRACE] 2025-04-30 07:18:22.191 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997486221 
[TRACE] 2025-04-30 07:18:22.191 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:18:22.192 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:18:22.192 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 15 ms 
[TRACE] 2025-04-30 07:18:23.088 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:18:23.088 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ddd4fe3 
[TRACE] 2025-04-30 07:18:23.205 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:18:23.205 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:18:23.217 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:18:23.217 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:18:28.234 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:18:28.234 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ddd4fe3 
[TRACE] 2025-04-30 07:18:28.235 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:18:28.235 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:18:28.320 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:18:28.320 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:19:46.490 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:19:46.491 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:19:47.043 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:19:47.043 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:19:47.229 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:19:47.229 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:19:47.326 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:19:47.327 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:19:47.327 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:19:47.327 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:19:47.924 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:19:47.947 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:19:47.947 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:19:47.948 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:19:48.036 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:19:48.037 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:19:48.266 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:19:48.267 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:19:48.267 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:19:48.267 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:19:48.273 - [任务 1][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 07:19:48.274 - [任务 1][T kafka] - The table kafka_test_2 has already exist. 
[INFO ] 2025-04-30 07:19:48.476 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:19:48.516 - [任务 1][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 07:19:48.520 - [任务 1][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 07:19:48.559 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:19:48.560 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:19:48.560 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:19:48.563 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 07:19:48.584 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:19:48.585 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:19:48.586 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:19:48.586 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:19:48.595 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:19:48.595 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:19:48.597 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:19:48.597 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:19:48.597 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:19:48.599 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:19:48.599 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:19:48.675 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 92cbb4a3-37df-4d01-a862-4ff7b0809a47 
[INFO ] 2025-04-30 07:19:48.676 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"92cbb4a3-37df-4d01-a862-4ff7b0809a47","offset":{"{\"server\":\"92cbb4a3-37df-4d01-a862-4ff7b0809a47\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 2104008350
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 92cbb4a3-37df-4d01-a862-4ff7b0809a47
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-92cbb4a3-37df-4d01-a862-4ff7b0809a47
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 92cbb4a3-37df-4d01-a862-4ff7b0809a47
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:19:48.962 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:19:48.963 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:19:49.570 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:19:49.570 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:19:49.571 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:20:05.591 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:20:05.592 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:20:05.592 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:20:05.592 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:20:05.600 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997587827 
[TRACE] 2025-04-30 07:20:05.601 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997587827 
[TRACE] 2025-04-30 07:20:05.601 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:20:05.602 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:20:05.604 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 51 ms 
[TRACE] 2025-04-30 07:20:05.604 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:20:05.622 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:20:05.622 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997587848 
[TRACE] 2025-04-30 07:20:05.622 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997587848 
[TRACE] 2025-04-30 07:20:05.622 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:20:05.623 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:20:05.623 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 19 ms 
[TRACE] 2025-04-30 07:20:09.287 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:20:09.288 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@78c4a7b9 
[TRACE] 2025-04-30 07:20:09.289 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:20:09.435 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:20:09.435 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:20:09.640 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:20:14.447 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:20:14.447 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@78c4a7b9 
[TRACE] 2025-04-30 07:20:14.447 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:20:14.448 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:20:14.550 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:20:14.550 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:22:29.343 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:22:29.548 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:22:29.766 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:22:29.912 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:22:29.912 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:22:30.118 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:22:30.139 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:22:30.139 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:22:30.140 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 1 ms 
[TRACE] 2025-04-30 07:22:30.140 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:22:30.624 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:22:30.645 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:22:30.646 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:22:30.646 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:22:30.649 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:22:30.680 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test_2'... 
[INFO ] 2025-04-30 07:22:30.680 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:22:30.986 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:22:30.987 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:22:30.987 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:22:30.987 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:22:31.002 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:22:31.189 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:22:31.189 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test'... 
[INFO ] 2025-04-30 07:22:31.266 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:22:31.284 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:22:31.284 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[INFO ] 2025-04-30 07:22:31.286 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[TRACE] 2025-04-30 07:22:31.287 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 07:22:31.319 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:22:31.319 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:22:31.320 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:22:31.320 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:22:31.325 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:22:31.326 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:22:31.326 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:22:31.327 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:22:31.327 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:22:31.328 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:22:31.329 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:22:31.486 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: e6d37884-be5f-40d2-b2e7-5b6a7b3041a0 
[INFO ] 2025-04-30 07:22:31.486 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"e6d37884-be5f-40d2-b2e7-5b6a7b3041a0","offset":{"{\"server\":\"e6d37884-be5f-40d2-b2e7-5b6a7b3041a0\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1210104718
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e6d37884-be5f-40d2-b2e7-5b6a7b3041a0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-e6d37884-be5f-40d2-b2e7-5b6a7b3041a0
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: e6d37884-be5f-40d2-b2e7-5b6a7b3041a0
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:22:31.892 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:22:32.168 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:22:32.275 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:22:32.276 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:22:32.276 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:22:55.396 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:22:55.458 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:22:55.458 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:22:55.466 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:22:55.467 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997750614 
[TRACE] 2025-04-30 07:22:55.468 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997750614 
[TRACE] 2025-04-30 07:22:55.469 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:22:55.471 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:22:55.471 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 75 ms 
[TRACE] 2025-04-30 07:22:55.472 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:22:55.481 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:22:55.488 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997750554 
[TRACE] 2025-04-30 07:22:55.488 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997750554 
[TRACE] 2025-04-30 07:22:55.488 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:22:55.489 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:22:55.489 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 17 ms 
[TRACE] 2025-04-30 07:22:57.990 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:22:57.990 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13c4ad42 
[TRACE] 2025-04-30 07:22:58.143 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:22:58.143 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:22:58.146 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:22:58.146 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:23:03.157 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:23:03.157 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13c4ad42 
[TRACE] 2025-04-30 07:23:03.157 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:23:03.158 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:23:03.207 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:23:03.208 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:23:17.900 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:23:17.903 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:23:18.065 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:23:18.066 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:23:18.119 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:23:18.120 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:23:18.138 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:23:18.139 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:23:18.139 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:23:18.140 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:23:18.753 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:23:18.784 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:23:18.785 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:23:18.785 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:23:18.786 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:23:18.786 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test_2'... 
[INFO ] 2025-04-30 07:23:18.851 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:23:18.852 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:23:18.852 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:23:18.852 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:23:18.852 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:23:18.882 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:23:18.882 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:23:18.923 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:23:18.923 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:23:18.923 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:23:18.924 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:23:18.935 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:23:18.936 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[INFO ] 2025-04-30 07:23:18.936 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test'... 
[TRACE] 2025-04-30 07:23:18.936 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 07:23:18.936 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:23:18.939 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:23:18.939 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:23:18.939 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:23:18.940 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:23:18.940 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:23:18.940 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":7646,"gtidSet":""} 
[INFO ] 2025-04-30 07:23:18.940 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:23:18.981 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7 
[INFO ] 2025-04-30 07:23:18.981 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7","offset":{"{\"server\":\"db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":7646,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 766895578
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: db2bb5d9-74fe-46e9-bbc2-35b8f4da1bb7
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:23:19.008 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:23:19.009 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 07:23:19.796 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:23:19.855 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:23:19.855 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:23:19.856 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:24:20.071 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:24:20.092 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:24:20.092 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:24:20.092 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:24:20.095 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997798660 
[TRACE] 2025-04-30 07:24:20.095 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997798660 
[TRACE] 2025-04-30 07:24:20.096 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:24:20.096 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:24:20.096 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 25 ms 
[TRACE] 2025-04-30 07:24:20.096 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:24:20.117 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:24:20.117 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997798686 
[TRACE] 2025-04-30 07:24:20.118 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997798686 
[TRACE] 2025-04-30 07:24:20.118 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:24:20.118 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:24:20.319 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 22 ms 
[TRACE] 2025-04-30 07:24:23.283 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:24:23.283 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@713d9ed8 
[TRACE] 2025-04-30 07:24:23.419 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:24:23.419 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:24:23.619 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:24:23.620 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:24:28.430 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:24:28.430 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@713d9ed8 
[TRACE] 2025-04-30 07:24:28.430 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:24:28.430 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:24:28.493 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:24:28.493 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:26:14.625 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:26:14.706 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:26:14.706 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:26:14.818 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:26:14.818 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:26:14.846 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:26:14.846 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:26:14.846 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:26:14.846 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:26:15.051 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:26:15.356 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:26:15.377 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:26:15.378 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:26:15.378 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:26:15.379 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:26:15.379 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test_2'... 
[INFO ] 2025-04-30 07:26:15.379 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:26:15.460 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:26:15.460 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:26:15.460 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:26:15.465 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:26:15.466 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:26:15.535 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:26:15.535 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:26:15.539 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:26:15.539 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:26:15.539 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:26:15.552 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test'... 
[INFO ] 2025-04-30 07:26:15.552 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:26:15.552 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:26:15.552 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 07:26:15.555 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:26:15.555 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:26:15.555 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:26:15.555 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:26:15.555 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:26:15.556 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:26:15.556 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:26:15.598 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:26:15.599 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: f08d88bd-fc57-4229-a759-ce7996f91c2c 
[INFO ] 2025-04-30 07:26:15.626 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"f08d88bd-fc57-4229-a759-ce7996f91c2c","offset":{"{\"server\":\"f08d88bd-fc57-4229-a759-ce7996f91c2c\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11029,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1673967923
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f08d88bd-fc57-4229-a759-ce7996f91c2c
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-f08d88bd-fc57-4229-a759-ce7996f91c2c
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: f08d88bd-fc57-4229-a759-ce7996f91c2c
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:26:15.626 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:26:15.831 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[WARN ] 2025-04-30 07:26:16.385 - [任务 1][T kafka] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6811d097e0867355baedf996, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[61ea522c-7478-43c7-a131-a992a919a17b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2025-04-30 07:26:16.385 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:26:16.431 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:26:16.431 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:26:16.431 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:26:28.301 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:26:28.301 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:26:28.301 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:26:28.301 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:26:28.309 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997975271 
[TRACE] 2025-04-30 07:26:28.309 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745997975271 
[TRACE] 2025-04-30 07:26:28.309 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:26:28.309 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:26:28.309 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 78 ms 
[TRACE] 2025-04-30 07:26:28.317 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:26:28.317 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:26:28.320 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997975290 
[TRACE] 2025-04-30 07:26:28.320 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745997975290 
[TRACE] 2025-04-30 07:26:28.320 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:26:28.320 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:26:28.320 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 11 ms 
[TRACE] 2025-04-30 07:26:28.603 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:26:28.603 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37742619 
[TRACE] 2025-04-30 07:26:28.603 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:26:28.732 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:26:28.732 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:26:28.935 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:26:33.751 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:26:33.751 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37742619 
[TRACE] 2025-04-30 07:26:33.751 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:26:33.751 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:26:33.800 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:26:33.800 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:26:46.498 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:26:46.499 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:26:46.733 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:26:46.855 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:26:46.855 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:26:46.874 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:26:46.874 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:26:46.874 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:26:46.876 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:26:46.876 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:26:46.999 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:26:46.999 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:26:46.999 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:26:46.999 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:26:47.001 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:26:47.149 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:26:47.149 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:26:47.149 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[INFO ] 2025-04-30 07:26:47.155 - [任务 1][S mysql INSURANCE] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-04-30 07:26:47.155 - [任务 1][S mysql INSURANCE] - Use existing batch read offset: {"kafka_test_2":{"batch_read_connector_status":"OVER"},"kafka_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:26:47.222 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:26:47.222 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:26:47.222 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:26:47.222 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:26:47.222 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:26:47.257 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: f08d88bd-fc57-4229-a759-ce7996f91c2c 
[INFO ] 2025-04-30 07:26:47.257 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"f08d88bd-fc57-4229-a759-ce7996f91c2c","offset":{"{\"server\":\"f08d88bd-fc57-4229-a759-ce7996f91c2c\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11029,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1656682178
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f08d88bd-fc57-4229-a759-ce7996f91c2c
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-f08d88bd-fc57-4229-a759-ce7996f91c2c
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: f08d88bd-fc57-4229-a759-ce7996f91c2c
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:26:47.462 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[TRACE] 2025-04-30 07:27:10.488 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:27:10.491 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:27:10.491 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:27:10.491 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:27:10.502 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998006936 
[TRACE] 2025-04-30 07:27:10.503 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998006936 
[TRACE] 2025-04-30 07:27:10.503 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:27:10.503 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:27:10.503 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 16 ms 
[TRACE] 2025-04-30 07:27:10.503 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:27:10.508 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:27:10.508 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998006918 
[TRACE] 2025-04-30 07:27:10.508 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998006918 
[TRACE] 2025-04-30 07:27:10.509 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:27:10.509 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:27:10.509 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 5 ms 
[TRACE] 2025-04-30 07:27:13.841 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:27:13.841 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@796d5240 
[TRACE] 2025-04-30 07:27:13.959 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:27:13.959 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:27:13.959 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:27:13.959 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:27:18.967 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:27:18.968 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@796d5240 
[TRACE] 2025-04-30 07:27:18.968 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:27:18.968 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:27:19.040 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:27:19.041 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:27:32.556 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:27:32.650 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:27:32.650 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:27:32.800 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:27:32.800 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:27:32.822 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:27:32.822 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:27:32.823 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:27:32.823 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:27:33.028 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:27:33.534 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:27:33.534 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:27:33.534 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:27:33.534 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:27:33.535 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:27:33.535 - [任务 1][T kafka] - Deleting topic 'sam_test'... 
[INFO ] 2025-04-30 07:27:33.535 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:27:33.551 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:27:33.551 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:27:33.551 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:27:33.551 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:27:33.566 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:27:33.566 - [任务 1][T kafka] - Ignore delete because not exist topic 'sam_test' 
[INFO ] 2025-04-30 07:27:37.911 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:27:39.018 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:27:39.018 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:27:39.374 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:27:46.221 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:27:46.222 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:27:46.222 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:27:46.223 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[TRACE] 2025-04-30 07:27:46.258 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:27:46.259 - [任务 1][T kafka] - Created topic 'sam_test': true 
[INFO ] 2025-04-30 07:27:46.278 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 97234438-c80e-44a6-99e6-0b76c222f937 
[INFO ] 2025-04-30 07:27:46.278 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"97234438-c80e-44a6-99e6-0b76c222f937","offset":{"{\"server\":\"97234438-c80e-44a6-99e6-0b76c222f937\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11029,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1375035650
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 97234438-c80e-44a6-99e6-0b76c222f937
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-97234438-c80e-44a6-99e6-0b76c222f937
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 97234438-c80e-44a6-99e6-0b76c222f937
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:27:46.315 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:27:46.315 - [任务 1][T kafka] - Deleting topic 'sam_test'... 
[TRACE] 2025-04-30 07:27:46.318 - [任务 1][T kafka] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Table model: TapTable id kafka_test name kafka_test storageEngine null charset null number of fields 8 
[WARN ] 2025-04-30 07:27:46.318 - [任务 1][T kafka] - Save error event failed: Failed to call rest api, msg no exception. 
[ERROR] 2025-04-30 07:27:46.331 - [任务 1][T kafka] - java.lang.RuntimeException: Table model: TapTable id kafka_test name kafka_test storageEngine null charset null number of fields 8 <-- Full Stack Trace -->
java.lang.RuntimeException: Table model: TapTable id kafka_test name kafka_test storageEngine null charset null number of fields 8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:119)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.lang.RuntimeException: Table model: TapTable id kafka_test name kafka_test storageEngine null charset null number of fields 8
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:161)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:112)
	... 53 more
Caused by: Table model: TapTable id kafka_test name kafka_test storageEngine null charset null number of fields 8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:238)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:176)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 56 more
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions.getTableExists()" is null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$20(HazelcastTargetPdkBaseNode.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doCreateTable(HazelcastTargetPdkBaseNode.java:332)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:439)
	... 59 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions.getTableExists()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$19(HazelcastTargetPdkBaseNode.java:446)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 63 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.pdk.apis.functions.connector.target.CreateTableOptions.getTableExists()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$18(HazelcastTargetPdkBaseNode.java:449)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 70 more

[TRACE] 2025-04-30 07:27:46.332 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:27:46.334 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:27:46.334 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:27:46.334 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[WARN ] 2025-04-30 07:27:46.338 - [任务 1][S mysql INSURANCE] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998053350 
[TRACE] 2025-04-30 07:27:46.339 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998053350 
[TRACE] 2025-04-30 07:27:46.339 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:27:46.339 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:27:46.339 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 8 ms 
[TRACE] 2025-04-30 07:27:46.339 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:28:31.404 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:28:31.422 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:28:31.422 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:28:31.830 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:28:31.986 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:28:31.986 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:28:32.108 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:28:32.207 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:28:32.207 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:28:32.208 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:28:32.208 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:28:32.754 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:28:32.799 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:28:32.799 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:28:32.802 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:28:32.802 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:28:32.827 - [任务 1][T kafka] - Deleting topic 'sam_test'... 
[INFO ] 2025-04-30 07:28:32.827 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:28:33.041 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:28:33.041 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:28:33.042 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:28:33.042 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:28:34.529 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:28:53.020 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:28:58.567 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:28:58.567 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:28:58.571 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:29:04.710 - [任务 1][T kafka] - Created topic 'sam_test': true 
[INFO ] 2025-04-30 07:29:04.721 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[TRACE] 2025-04-30 07:29:04.725 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:29:05.964 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:29:05.964 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:29:05.995 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:29:05.997 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:29:05.997 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:29:05.997 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:29:05.997 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:29:05.998 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11029,"gtidSet":""} 
[INFO ] 2025-04-30 07:29:05.998 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:29:06.043 - [任务 1][T kafka] - Deleting topic 'sam_test'... 
[INFO ] 2025-04-30 07:29:06.044 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 7b1da2a5-c653-4063-bd81-e023b426b7ef 
[INFO ] 2025-04-30 07:29:17.054 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"7b1da2a5-c653-4063-bd81-e023b426b7ef","offset":{"{\"server\":\"7b1da2a5-c653-4063-bd81-e023b426b7ef\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11029,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1041925769
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 7b1da2a5-c653-4063-bd81-e023b426b7ef
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-7b1da2a5-c653-4063-bd81-e023b426b7ef
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 7b1da2a5-c653-4063-bd81-e023b426b7ef
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2025-04-30 07:29:20.375 - [任务 1][T kafka] - Created topic 'sam_test': true 
[WARN ] 2025-04-30 07:29:20.884 - [任务 1][T kafka] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6811d14072cbe5540fb7f4e3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[61ea522c-7478-43c7-a131-a992a919a17b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2025-04-30 07:29:20.884 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:29:20.964 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[TRACE] 2025-04-30 07:29:20.965 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:29:20.965 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:29:21.169 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:30:04.537 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:30:04.598 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:30:04.599 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:30:04.609 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:30:04.610 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998112657 
[TRACE] 2025-04-30 07:30:04.611 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998112657 
[TRACE] 2025-04-30 07:30:04.611 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:30:04.613 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:30:04.613 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 76 ms 
[TRACE] 2025-04-30 07:30:04.613 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:30:04.627 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:30:04.627 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998112686 
[TRACE] 2025-04-30 07:30:04.627 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998112686 
[TRACE] 2025-04-30 07:30:04.627 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:30:04.627 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:30:04.831 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 14 ms 
[TRACE] 2025-04-30 07:30:06.622 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:30:06.622 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6063fdf8 
[TRACE] 2025-04-30 07:30:06.751 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:30:06.752 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:30:06.753 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:30:06.753 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:30:11.759 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:30:11.760 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6063fdf8 
[TRACE] 2025-04-30 07:30:11.761 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:30:11.761 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:30:11.843 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:30:11.845 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:30:57.733 - [任务 1] - Task initialization... 
[TRACE] 2025-04-30 07:30:57.738 - [任务 1] - Start task milestones: 6811c97ff4d18b328154cf7d(任务 1) 
[INFO ] 2025-04-30 07:30:57.941 - [任务 1] - Loading table structure completed 
[TRACE] 2025-04-30 07:30:58.034 - [任务 1] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:30:58.034 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:30:58.052 - [任务 1] - Task started 
[TRACE] 2025-04-30 07:30:58.052 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:30:58.053 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:30:58.053 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:30:58.053 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:30:58.580 - [任务 1][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:30:58.612 - [任务 1][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:30:58.613 - [任务 1][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:30:58.613 - [任务 1][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:30:58.613 - [任务 1][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:30:58.613 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test_2'... 
[INFO ] 2025-04-30 07:30:58.613 - [任务 1][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:30:58.647 - [任务 1][T kafka] - Ignore delete because not exist topic 'INSURANCE_kafka_test_2' 
[INFO ] 2025-04-30 07:30:58.683 - [任务 1][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:30:58.683 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:30:58.684 - [任务 1][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:30:58.684 - [任务 1][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:30:58.690 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 07:30:58.690 - [任务 1][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:30:58.754 - [任务 1][T kafka] - Deleting topic 'INSURANCE_kafka_test'... 
[INFO ] 2025-04-30 07:30:58.754 - [任务 1][T kafka] - Ignore delete because not exist topic 'INSURANCE_kafka_test' 
[INFO ] 2025-04-30 07:30:58.765 - [任务 1][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:30:58.765 - [任务 1][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:30:58.765 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:30:58.765 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:30:58.776 - [任务 1][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[TRACE] 2025-04-30 07:30:58.777 - [任务 1][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:30:58.777 - [任务 1][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:30:58.777 - [任务 1][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:30:58.780 - [任务 1][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:30:58.780 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:30:58.780 - [任务 1][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:30:58.780 - [任务 1][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:30:58.781 - [任务 1][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:30:58.781 - [任务 1][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:30:58.781 - [任务 1][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:30:58.824 - [任务 1][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 07:30:58.824 - [任务 1][S mysql INSURANCE] - Starting mysql cdc, server name: 2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb 
[INFO ] 2025-04-30 07:30:58.858 - [任务 1][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb","offset":{"{\"server\":\"2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11373,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 519594672
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2a85fc2a-2bcb-447e-8c0d-5e5d6460bbfb
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:30:58.858 - [任务 1][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:30:59.669 - [任务 1][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:30:59.677 - [任务 1][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:30:59.677 - [任务 1][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:30:59.677 - [任务 1][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:31:06.419 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:31:06.485 - [任务 1][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:31:06.485 - [任务 1][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:31:06.490 - [任务 1][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:31:06.490 - [任务 1][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998258483 
[TRACE] 2025-04-30 07:31:06.490 - [任务 1][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998258483 
[TRACE] 2025-04-30 07:31:06.490 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:31:06.491 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:31:06.491 - [任务 1][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 95 ms 
[TRACE] 2025-04-30 07:31:06.491 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] running status set to false 
[INFO ] 2025-04-30 07:31:06.503 - [任务 1][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:31:06.508 - [任务 1][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998258509 
[TRACE] 2025-04-30 07:31:06.508 - [任务 1][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_5a0c8541-52c0-4832-9327-c73443852910_1745998258509 
[TRACE] 2025-04-30 07:31:06.508 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] schema data cleaned 
[TRACE] 2025-04-30 07:31:06.509 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] monitor closed 
[TRACE] 2025-04-30 07:31:06.509 - [任务 1][T kafka] - Node T kafka[5a0c8541-52c0-4832-9327-c73443852910] close complete, cost 17 ms 
[TRACE] 2025-04-30 07:31:06.908 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:31:06.908 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@108bbb86 
[TRACE] 2025-04-30 07:31:07.035 - [任务 1] - Stop task milestones: 6811c97ff4d18b328154cf7d(任务 1)  
[TRACE] 2025-04-30 07:31:07.035 - [任务 1] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:31:07.035 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:31:07.035 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:31:12.047 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:31:12.048 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@108bbb86 
[TRACE] 2025-04-30 07:31:12.048 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:31:12.048 - [任务 1] - Task stopped. 
[TRACE] 2025-04-30 07:31:12.118 - [任务 1] - Remove memory task client succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:31:12.118 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:38:32.587 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 07:38:32.588 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 07:38:32.791 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 07:38:32.885 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:38:32.885 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:38:32.915 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 07:38:32.915 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:38:32.915 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:38:32.915 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:38:32.915 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:38:33.422 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:38:33.441 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:38:33.442 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:38:33.442 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:38:33.443 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:38:33.443 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:38:33.608 - [db - kafka transformer][T kafka] - Created topic 'kafka_test_2': true 
[INFO ] 2025-04-30 07:38:33.608 - [db - kafka transformer][T kafka] - Created topic 'kafka_test': true 
[INFO ] 2025-04-30 07:38:33.623 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:38:33.623 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:38:33.623 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:38:33.623 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:38:33.630 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:38:33.700 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:38:33.700 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:38:33.700 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:38:33.700 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:38:33.715 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[TRACE] 2025-04-30 07:38:33.715 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:38:33.715 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:38:33.715 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 07:38:33.719 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:38:33.719 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:38:33.719 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:38:33.719 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:38:33.720 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:38:33.720 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:38:33.720 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:38:33.757 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 70732a6a-14fa-48bc-9313-badf1c99ed3f 
[INFO ] 2025-04-30 07:38:33.758 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"70732a6a-14fa-48bc-9313-badf1c99ed3f","offset":{"{\"server\":\"70732a6a-14fa-48bc-9313-badf1c99ed3f\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11373,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1883300287
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 70732a6a-14fa-48bc-9313-badf1c99ed3f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-70732a6a-14fa-48bc-9313-badf1c99ed3f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 70732a6a-14fa-48bc-9313-badf1c99ed3f
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:38:33.960 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:38:34.452 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:38:34.510 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:38:34.510 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:38:34.511 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:38:54.476 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:38:54.477 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:38:54.477 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:38:54.477 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:38:54.480 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998713410 
[TRACE] 2025-04-30 07:38:54.480 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998713410 
[TRACE] 2025-04-30 07:38:54.480 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:38:54.481 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:38:54.481 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 10 ms 
[TRACE] 2025-04-30 07:38:54.481 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 07:38:54.490 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:38:54.491 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998713347 
[TRACE] 2025-04-30 07:38:54.491 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998713347 
[TRACE] 2025-04-30 07:38:54.491 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 07:38:54.491 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 07:38:54.696 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 10 ms 
[TRACE] 2025-04-30 07:38:57.612 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:38:57.612 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c35ba4f 
[TRACE] 2025-04-30 07:38:57.613 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 07:38:57.737 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:38:57.738 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:38:57.738 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:39:02.752 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:39:02.753 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c35ba4f 
[TRACE] 2025-04-30 07:39:02.753 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:39:02.753 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:39:02.807 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:39:02.808 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:39:19.364 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 07:39:19.505 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 07:39:19.505 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 07:39:19.576 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:39:19.628 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:39:19.628 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 07:39:19.643 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:39:19.643 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:39:19.643 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:39:19.643 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:39:20.193 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:39:20.193 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:39:20.193 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:39:20.193 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:39:20.194 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:39:20.195 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:39:20.248 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:39:20.248 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:39:20.248 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:39:20.249 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:39:20.263 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:39:20.263 - [db - kafka transformer][T kafka] - Created topic 'common_topic': true 
[INFO ] 2025-04-30 07:39:20.352 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:39:20.352 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:39:20.353 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:39:20.353 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:39:20.363 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:39:20.363 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:39:20.366 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 07:39:20.366 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:39:20.367 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:39:20.367 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:39:20.367 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:39:20.367 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:39:20.368 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:39:20.369 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:39:20.369 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:39:20.401 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 07:39:20.401 - [db - kafka transformer][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 07:39:20.433 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a 
[INFO ] 2025-04-30 07:39:20.435 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"4d9614bd-a681-4b32-ba3b-fe91c0fc121a","offset":{"{\"server\":\"4d9614bd-a681-4b32-ba3b-fe91c0fc121a\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11373,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1688808177
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:39:20.640 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:39:21.200 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:39:21.252 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:39:21.252 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:39:21.252 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:39:40.159 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:39:40.226 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:39:40.227 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:39:40.227 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998760054 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998760054 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 74 ms 
[TRACE] 2025-04-30 07:39:40.233 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 07:39:40.247 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:39:40.247 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998760103 
[TRACE] 2025-04-30 07:39:40.247 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998760103 
[TRACE] 2025-04-30 07:39:40.247 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 07:39:40.247 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 07:39:40.451 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 14 ms 
[TRACE] 2025-04-30 07:39:42.859 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:39:42.860 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30d6332a 
[TRACE] 2025-04-30 07:39:42.979 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 07:39:42.979 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:39:42.979 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:39:42.979 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:39:47.998 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:39:48.007 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30d6332a 
[TRACE] 2025-04-30 07:39:48.015 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:39:48.079 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:39:48.079 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:39:48.079 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:41:06.122 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 07:41:06.125 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 07:41:06.326 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 07:41:06.398 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:41:06.398 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:41:06.426 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 07:41:06.427 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:41:06.427 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:41:06.427 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:41:06.529 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:41:06.529 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:41:06.534 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:41:06.534 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:41:06.534 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:41:06.536 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:41:06.686 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:41:06.686 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:41:06.686 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[INFO ] 2025-04-30 07:41:06.686 - [db - kafka transformer][S mysql INSURANCE] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-04-30 07:41:06.693 - [db - kafka transformer][S mysql INSURANCE] - Use existing batch read offset: {"kafka_test_2":{"batch_read_connector_status":"OVER"},"kafka_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:41:06.766 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:41:06.766 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:41:06.766 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:41:06.766 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:41:06.798 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:41:06.798 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a 
[INFO ] 2025-04-30 07:41:06.826 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"4d9614bd-a681-4b32-ba3b-fe91c0fc121a","offset":{"{\"server\":\"4d9614bd-a681-4b32-ba3b-fe91c0fc121a\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11373,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1011817728
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4d9614bd-a681-4b32-ba3b-fe91c0fc121a
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:41:06.826 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[TRACE] 2025-04-30 07:41:09.864 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:41:09.937 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:41:09.937 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:41:09.937 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998866466 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998866466 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 112 ms 
[TRACE] 2025-04-30 07:41:09.947 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 07:41:09.947 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:41:09.949 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998866455 
[TRACE] 2025-04-30 07:41:09.949 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998866455 
[TRACE] 2025-04-30 07:41:09.949 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 07:41:09.949 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 07:41:09.949 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 2 ms 
[TRACE] 2025-04-30 07:41:13.156 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:41:13.156 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@466c0ac5 
[TRACE] 2025-04-30 07:41:13.157 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 07:41:13.278 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:41:13.278 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:41:13.481 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:41:18.287 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:41:18.287 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@466c0ac5 
[TRACE] 2025-04-30 07:41:18.287 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:41:18.287 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:41:18.341 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:41:18.341 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:41:22.804 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 07:41:22.805 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 07:41:22.996 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 07:41:22.997 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 07:41:23.058 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 07:41:23.058 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 07:41:23.071 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:41:23.071 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 07:41:23.072 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 07:41:23.072 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 07:41:23.607 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 07:41:23.607 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 07:41:23.607 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 07:41:23.607 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 07:41:23.608 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 07:41:23.608 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 07:41:23.637 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 07:41:23.637 - [db - kafka transformer][T kafka] - The table kafka_test_2 has already exist. 
[INFO ] 2025-04-30 07:41:23.666 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 07:41:23.666 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 07:41:23.666 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 07:41:23.666 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 07:41:23.696 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:41:23.696 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 07:41:23.696 - [db - kafka transformer][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 07:41:23.740 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 07:41:23.747 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 07:41:23.747 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 07:41:23.747 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 07:41:23.768 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 07:41:23.768 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 07:41:23.768 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 07:41:23.771 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11373,"gtidSet":""} 
[INFO ] 2025-04-30 07:41:23.772 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 07:41:23.795 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: fff685f0-cbd8-4f51-a171-210b25e2c378 
[INFO ] 2025-04-30 07:41:23.795 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"fff685f0-cbd8-4f51-a171-210b25e2c378","offset":{"{\"server\":\"fff685f0-cbd8-4f51-a171-210b25e2c378\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11373,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1446608025
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fff685f0-cbd8-4f51-a171-210b25e2c378
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-fff685f0-cbd8-4f51-a171-210b25e2c378
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: fff685f0-cbd8-4f51-a171-210b25e2c378
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 07:41:24.000 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 07:41:24.649 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 07:41:24.649 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 07:41:24.649 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 07:41:24.649 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 07:42:48.123 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 07:42:48.187 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 07:42:48.187 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 07:42:48.187 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 07:42:48.194 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998883468 
[TRACE] 2025-04-30 07:42:48.194 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1745998883468 
[TRACE] 2025-04-30 07:42:48.195 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 07:42:48.195 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 07:42:48.195 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 72 ms 
[TRACE] 2025-04-30 07:42:48.195 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 07:42:48.204 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 07:42:48.204 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998883509 
[TRACE] 2025-04-30 07:42:48.205 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1745998883509 
[TRACE] 2025-04-30 07:42:48.205 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 07:42:48.206 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 07:42:48.206 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 10 ms 
[TRACE] 2025-04-30 07:42:48.460 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:42:48.460 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d80be72 
[TRACE] 2025-04-30 07:42:48.460 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 07:42:48.583 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 07:42:48.584 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 07:42:48.584 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:42:53.595 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 07:42:53.595 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d80be72 
[TRACE] 2025-04-30 07:42:53.595 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 07:42:53.595 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 07:42:53.664 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 07:42:53.664 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 09:45:11.996 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 09:45:11.997 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 09:45:12.428 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 09:45:12.522 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 09:45:12.728 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 09:45:12.837 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 09:45:12.838 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:45:12.838 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:45:12.838 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 09:45:12.839 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 1 ms 
[INFO ] 2025-04-30 09:45:13.435 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 09:45:13.482 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 09:45:13.483 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 09:45:13.483 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 09:45:38.904 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 09:45:38.904 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 09:45:38.905 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 09:45:38.905 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 09:45:38.926 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:45:39.041 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 09:45:39.042 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 09:45:39.042 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 09:45:39.058 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[TRACE] 2025-04-30 09:45:39.058 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 09:45:39.070 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 09:45:39.071 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 09:45:39.071 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 09:45:39.076 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 09:45:39.076 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 09:45:39.076 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 09:45:39.077 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 09:45:39.077 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 09:45:39.079 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:45:39.079 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 09:45:39.174 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 2ecae1f5-dcad-474a-8caa-e03444a9d8d5 
[INFO ] 2025-04-30 09:45:39.175 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"2ecae1f5-dcad-474a-8caa-e03444a9d8d5","offset":{"{\"server\":\"2ecae1f5-dcad-474a-8caa-e03444a9d8d5\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11766,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 311057515
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2ecae1f5-dcad-474a-8caa-e03444a9d8d5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2ecae1f5-dcad-474a-8caa-e03444a9d8d5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2ecae1f5-dcad-474a-8caa-e03444a9d8d5
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 09:45:39.585 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 09:46:10.509 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 09:46:22.832 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 09:46:23.335 - [db - kafka transformer][T kafka] - Created topic 'common_topic': true 
[INFO ] 2025-04-30 09:46:23.622 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 09:46:23.622 - [db - kafka transformer][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 09:46:24.230 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 09:46:24.349 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 09:46:24.349 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 09:46:24.350 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 09:46:25.770 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 09:46:25.783 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 09:46:25.783 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 09:46:25.783 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 09:46:25.790 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006313272 
[TRACE] 2025-04-30 09:46:25.792 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006313272 
[TRACE] 2025-04-30 09:46:25.792 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 09:46:25.793 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 09:46:25.794 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 71 ms 
[TRACE] 2025-04-30 09:46:25.794 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 09:46:25.874 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 09:46:25.874 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006313362 
[TRACE] 2025-04-30 09:46:25.875 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006313362 
[TRACE] 2025-04-30 09:46:25.875 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 09:46:25.875 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 09:46:26.078 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 81 ms 
[TRACE] 2025-04-30 09:46:27.742 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 09:46:27.744 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a846a5a 
[TRACE] 2025-04-30 09:46:27.745 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 09:46:27.914 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 09:46:27.914 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 09:46:28.116 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 09:46:32.919 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 09:46:32.919 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a846a5a 
[TRACE] 2025-04-30 09:46:32.920 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 09:46:32.920 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 09:46:32.981 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 09:46:32.981 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 09:47:27.077 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 09:47:27.080 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 09:47:27.281 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 09:47:27.333 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 09:47:27.334 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 09:47:27.358 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 09:47:27.358 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:47:27.358 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:47:27.359 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 09:47:27.359 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 09:47:27.885 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 09:47:27.908 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 09:47:27.908 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 09:47:27.908 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 09:47:27.939 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 09:47:27.939 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 09:47:27.962 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 09:47:27.962 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 09:47:27.962 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 09:47:27.962 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 09:47:28.006 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:47:28.006 - [db - kafka transformer][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[INFO ] 2025-04-30 09:47:28.039 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 09:47:28.039 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 09:47:28.039 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 09:47:28.039 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 09:47:28.047 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 09:47:28.048 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 09:47:28.048 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 09:47:28.048 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 09:47:28.052 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 09:47:28.052 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 09:47:28.052 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 09:47:28.052 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 09:47:28.052 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 09:47:28.053 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:47:28.053 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 09:47:28.083 - [db - kafka transformer][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[INFO ] 2025-04-30 09:47:28.083 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: a0073ee6-0956-4416-a470-72341f80915f 
[INFO ] 2025-04-30 09:47:28.105 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"a0073ee6-0956-4416-a470-72341f80915f","offset":{"{\"server\":\"a0073ee6-0956-4416-a470-72341f80915f\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11766,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 2131171281
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a0073ee6-0956-4416-a470-72341f80915f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a0073ee6-0956-4416-a470-72341f80915f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: a0073ee6-0956-4416-a470-72341f80915f
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 09:47:28.105 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 09:47:28.480 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 09:47:28.480 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 09:47:28.480 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 09:47:28.480 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 09:47:43.747 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 09:47:43.798 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 09:47:43.799 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 09:47:43.799 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 09:47:43.805 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006447739 
[TRACE] 2025-04-30 09:47:43.805 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006447739 
[TRACE] 2025-04-30 09:47:43.805 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 09:47:43.806 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 09:47:43.806 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 79 ms 
[TRACE] 2025-04-30 09:47:43.817 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 09:47:43.817 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 09:47:43.820 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006447794 
[TRACE] 2025-04-30 09:47:43.820 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006447794 
[TRACE] 2025-04-30 09:47:43.820 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 09:47:43.820 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 09:47:44.021 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 14 ms 
[TRACE] 2025-04-30 09:47:48.035 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 09:47:48.035 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@545bc1a 
[TRACE] 2025-04-30 09:47:48.151 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 09:47:48.151 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 09:47:48.151 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 09:47:48.151 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 09:47:53.159 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 09:47:53.160 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@545bc1a 
[TRACE] 2025-04-30 09:47:53.160 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 09:47:53.288 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 09:47:53.289 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 09:47:53.289 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 09:52:32.872 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 09:52:32.873 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 09:52:33.066 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 09:52:33.066 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 09:52:33.122 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 09:52:33.122 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 09:52:33.141 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:52:33.141 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 09:52:33.141 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 09:52:33.141 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 09:52:33.802 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 09:52:33.802 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 09:52:33.802 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 09:52:33.802 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 09:52:33.816 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 09:52:33.817 - [db - kafka transformer][T kafka] - Deleting topic '_kafka_test_2'... 
[INFO ] 2025-04-30 09:52:33.817 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 09:52:33.863 - [db - kafka transformer][T kafka] - Ignore delete because not exist topic '_kafka_test_2' 
[INFO ] 2025-04-30 09:52:33.863 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 09:52:33.863 - [db - kafka transformer][T kafka] - The table kafka_test_2 has already exist. 
[INFO ] 2025-04-30 09:52:33.920 - [db - kafka transformer][T kafka] - Deleting topic '_kafka_test'... 
[INFO ] 2025-04-30 09:52:33.935 - [db - kafka transformer][T kafka] - Ignore delete because not exist topic '_kafka_test' 
[INFO ] 2025-04-30 09:52:33.935 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 09:52:33.935 - [db - kafka transformer][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 09:52:33.954 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 09:52:33.954 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 09:52:33.954 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 09:52:33.960 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 09:52:33.961 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:52:34.041 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 09:52:34.041 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 09:52:34.042 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 09:52:34.042 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 09:52:34.053 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[TRACE] 2025-04-30 09:52:34.053 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 09:52:34.053 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 09:52:34.053 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 09:52:34.056 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 09:52:34.056 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 09:52:34.056 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 09:52:34.057 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 09:52:34.057 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 09:52:34.057 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 09:52:34.057 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 09:52:34.168 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 4568e4c6-42f5-4808-bcaf-06360cabd54d 
[INFO ] 2025-04-30 09:52:34.168 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"4568e4c6-42f5-4808-bcaf-06360cabd54d","offset":{"{\"server\":\"4568e4c6-42f5-4808-bcaf-06360cabd54d\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11766,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 297735521
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4568e4c6-42f5-4808-bcaf-06360cabd54d
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4568e4c6-42f5-4808-bcaf-06360cabd54d
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4568e4c6-42f5-4808-bcaf-06360cabd54d
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 09:52:34.318 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 09:52:34.319 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 09:52:34.384 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 09:52:34.384 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 09:52:34.589 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 10:00:28.163 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 10:00:28.163 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 10:00:28.163 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 10:00:28.164 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 10:00:28.167 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006753758 
[TRACE] 2025-04-30 10:00:28.167 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746006753758 
[TRACE] 2025-04-30 10:00:28.167 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 10:00:28.167 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 10:00:28.168 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 69 ms 
[TRACE] 2025-04-30 10:00:28.168 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 10:00:28.177 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 10:00:28.177 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006753708 
[TRACE] 2025-04-30 10:00:28.177 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746006753708 
[TRACE] 2025-04-30 10:00:28.177 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 10:00:28.177 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 10:00:28.178 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 9 ms 
[TRACE] 2025-04-30 10:00:28.963 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:00:28.964 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e644027 
[TRACE] 2025-04-30 10:00:28.964 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 10:00:29.084 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 10:00:29.085 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 10:00:29.085 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:00:34.102 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:00:34.102 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e644027 
[TRACE] 2025-04-30 10:00:34.102 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 10:00:34.102 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:00:34.176 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 10:00:34.176 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 10:00:39.215 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 10:00:39.215 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 10:00:39.401 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 10:00:39.401 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 10:00:39.458 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 10:00:39.458 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 10:00:39.474 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 10:00:39.474 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 10:00:39.474 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 0 ms 
[TRACE] 2025-04-30 10:00:39.474 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 10:00:40.095 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 10:00:40.114 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 10:00:40.114 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 10:00:40.114 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 10:00:40.131 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 10:00:40.131 - [db - kafka transformer][T kafka] - Deleting topic '_kafka_test_2'... 
[INFO ] 2025-04-30 10:00:40.131 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 10:00:40.166 - [db - kafka transformer][T kafka] - Ignore delete because not exist topic '_kafka_test_2' 
[INFO ] 2025-04-30 10:00:40.166 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 10:00:40.166 - [db - kafka transformer][T kafka] - The table kafka_test_2 has already exist. 
[INFO ] 2025-04-30 10:00:40.180 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 10:00:40.180 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 10:00:40.180 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 10:00:40.180 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 10:00:40.214 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 10:00:40.214 - [db - kafka transformer][T kafka] - Deleting topic '_kafka_test'... 
[INFO ] 2025-04-30 10:00:40.232 - [db - kafka transformer][T kafka] - Ignore delete because not exist topic '_kafka_test' 
[INFO ] 2025-04-30 10:00:40.232 - [db - kafka transformer][T kafka] - The number of partitions set is equal to the number of partitions of the existing table 
[TRACE] 2025-04-30 10:00:40.232 - [db - kafka transformer][T kafka] - The table kafka_test has already exist. 
[INFO ] 2025-04-30 10:00:40.262 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 10:00:40.276 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 10:00:40.277 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 10:00:40.286 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 10:00:40.286 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 10:00:40.286 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 10:00:40.286 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[TRACE] 2025-04-30 10:00:40.288 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 10:00:40.289 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 10:00:40.290 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 10:00:40.321 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 2c46115d-fbe0-4b6c-bc31-54993af29b7d 
[INFO ] 2025-04-30 10:00:40.321 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"2c46115d-fbe0-4b6c-bc31-54993af29b7d","offset":{"{\"server\":\"2c46115d-fbe0-4b6c-bc31-54993af29b7d\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11766,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 1882446853
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2c46115d-fbe0-4b6c-bc31-54993af29b7d
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2c46115d-fbe0-4b6c-bc31-54993af29b7d
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2c46115d-fbe0-4b6c-bc31-54993af29b7d
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[TRACE] 2025-04-30 10:00:40.527 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 10:00:40.634 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 10:00:40.670 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 10:00:40.670 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 10:00:40.874 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 10:00:55.970 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 10:00:56.068 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 10:00:56.069 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 10:00:56.069 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 10:00:56.084 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746007239980 
[TRACE] 2025-04-30 10:00:56.084 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746007239980 
[TRACE] 2025-04-30 10:00:56.084 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 10:00:56.084 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 10:00:56.085 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 114 ms 
[TRACE] 2025-04-30 10:00:56.085 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746007240028 
[TRACE] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746007240028 
[TRACE] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 10:00:56.093 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 8 ms 
[TRACE] 2025-04-30 10:00:59.212 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:00:59.212 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@650872ac 
[TRACE] 2025-04-30 10:00:59.348 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 10:00:59.348 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 10:00:59.348 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 10:00:59.551 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:01:04.358 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:01:04.358 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@650872ac 
[TRACE] 2025-04-30 10:01:04.358 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 10:01:04.359 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:01:04.438 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 10:01:04.441 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 10:02:55.094 - [db - kafka transformer] - Task initialization... 
[TRACE] 2025-04-30 10:02:55.300 - [db - kafka transformer] - Start task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer) 
[INFO ] 2025-04-30 10:02:55.417 - [db - kafka transformer] - Loading table structure completed 
[TRACE] 2025-04-30 10:02:55.568 - [db - kafka transformer] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-30 10:02:55.773 - [db - kafka transformer] - The engine receives db - kafka transformer task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-30 10:02:55.895 - [db - kafka transformer] - Task started 
[TRACE] 2025-04-30 10:02:55.895 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] start preload schema,table counts: 2 
[TRACE] 2025-04-30 10:02:55.895 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] start preload schema,table counts: 2 
[TRACE] 2025-04-30 10:02:55.896 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] preload schema finished, cost 1 ms 
[TRACE] 2025-04-30 10:02:55.896 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] preload schema finished, cost 0 ms 
[INFO ] 2025-04-30 10:02:56.553 - [db - kafka transformer][T kafka] - Starting kafka_enhanced 
[INFO ] 2025-04-30 10:02:56.554 - [db - kafka transformer][T kafka] - Sink connector(T kafka) initialization completed 
[TRACE] 2025-04-30 10:02:56.554 - [db - kafka transformer][T kafka] - Node(T kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-30 10:02:56.554 - [db - kafka transformer][T kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-30 10:02:56.619 - [db - kafka transformer][T kafka] - Apply table structure to target database 
[INFO ] 2025-04-30 10:02:56.619 - [db - kafka transformer][T kafka] - Deleting topic 'INSURANCE_kafka_test_2'... 
[INFO ] 2025-04-30 10:02:56.619 - [db - kafka transformer][T kafka] - Creating adminService for kafka_enhanced 
[INFO ] 2025-04-30 10:02:56.809 - [db - kafka transformer][S mysql INSURANCE] - Source connector(S mysql INSURANCE) initialization completed 
[TRACE] 2025-04-30 10:02:56.810 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" read batch size: 100 
[TRACE] 2025-04-30 10:02:56.810 - [db - kafka transformer][S mysql INSURANCE] - Source node "S mysql INSURANCE" event queue capacity: 200 
[TRACE] 2025-04-30 10:02:56.810 - [db - kafka transformer][S mysql INSURANCE] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-30 10:02:56.958 - [db - kafka transformer][S mysql INSURANCE] - Use existing stream offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 10:02:56.959 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from 2 tables 
[TRACE] 2025-04-30 10:02:56.983 - [db - kafka transformer][S mysql INSURANCE] - Initial sync started 
[INFO ] 2025-04-30 10:02:56.984 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test_2 
[TRACE] 2025-04-30 10:02:56.996 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 is going to be initial synced 
[INFO ] 2025-04-30 10:02:56.996 - [db - kafka transformer][T kafka] - Created topic 'INSURANCE_kafka_test_2': true 
[TRACE] 2025-04-30 10:02:57.030 - [db - kafka transformer][S mysql INSURANCE] - Query snapshot row size completed: S mysql INSURANCE(61ea522c-7478-43c7-a131-a992a919a17b) 
[INFO ] 2025-04-30 10:02:57.031 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test_2 has been completed batch read 
[INFO ] 2025-04-30 10:02:57.031 - [db - kafka transformer][S mysql INSURANCE] - Starting batch read from table: kafka_test 
[TRACE] 2025-04-30 10:02:57.046 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test is going to be initial synced 
[INFO ] 2025-04-30 10:02:57.047 - [db - kafka transformer][S mysql INSURANCE] - Table kafka_test has been completed batch read 
[TRACE] 2025-04-30 10:02:57.047 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[INFO ] 2025-04-30 10:02:57.047 - [db - kafka transformer][S mysql INSURANCE] - Batch read completed. 
[TRACE] 2025-04-30 10:02:57.052 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync starting... 
[TRACE] 2025-04-30 10:02:57.055 - [db - kafka transformer][S mysql INSURANCE] - Initial sync completed 
[TRACE] 2025-04-30 10:02:57.055 - [db - kafka transformer][S mysql INSURANCE] - Starting stream read, table list: [kafka_test_2, kafka_test], offset: {"filename":"mysql-bin.000028","position":11766,"gtidSet":""} 
[INFO ] 2025-04-30 10:02:57.056 - [db - kafka transformer][S mysql INSURANCE] - Starting incremental sync using database log parser 
[INFO ] 2025-04-30 10:02:57.102 - [db - kafka transformer][T kafka] - Deleting topic 'INSURANCE_kafka_test'... 
[INFO ] 2025-04-30 10:02:57.103 - [db - kafka transformer][S mysql INSURANCE] - Starting mysql cdc, server name: 72f74594-afea-4426-94f4-f773e9846eae 
[INFO ] 2025-04-30 10:02:57.163 - [db - kafka transformer][S mysql INSURANCE] - Starting binlog reader with config {
  connector.class: io.debezium.connector.mysql.MySqlConnector
  snapshot.locking.mode: none
  max.queue.size: 800
  database.history.skip.unparseable.ddl: true
  pdk.offset.string: {"name":"72f74594-afea-4426-94f4-f773e9846eae","offset":{"{\"server\":\"72f74594-afea-4426-94f4-f773e9846eae\"}":"{\"file\":\"mysql-bin.000028\",\"pos\":11766,\"server_id\":\"223344\"}"}}
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  database.server.id: 367740246
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 72f74594-afea-4426-94f4-f773e9846eae
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-72f74594-afea-4426-94f4-f773e9846eae
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: 127.0.0.1
  database.password: ********
  name: 72f74594-afea-4426-94f4-f773e9846eae
  database.history.store.only.monitored.tables.ddl: true
  max.batch.size: 100
  table.include.list: INSURANCE.kafka_test_2,INSURANCE.kafka_test
  database.include.list: INSURANCE
  snapshot.mode: schema_only_recovery
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2025-04-30 10:02:57.164 - [db - kafka transformer][T kafka] - Created topic 'INSURANCE_kafka_test': true 
[TRACE] 2025-04-30 10:02:57.592 - [db - kafka transformer][S mysql INSURANCE] - Connector Mysql incremental start succeed, tables: [kafka_test_2, kafka_test], data change syncing 
[INFO ] 2025-04-30 10:02:57.700 - [db - kafka transformer][T kafka] - Creating producer for kafka_enhanced 
[TRACE] 2025-04-30 10:02:57.700 - [db - kafka transformer][T kafka] - Process after table "kafka_test" initial sync finished, cost: 0 ms 
[TRACE] 2025-04-30 10:02:57.700 - [db - kafka transformer][T kafka] - Process after table "kafka_test_2" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-30 10:02:57.905 - [db - kafka transformer][T kafka] - Process after all table(s) initial sync are finished，table number: 2 
[TRACE] 2025-04-30 10:05:33.206 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] running status set to false 
[INFO ] 2025-04-30 10:05:33.206 - [db - kafka transformer][S mysql INSURANCE] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2025-04-30 10:05:33.206 - [db - kafka transformer][S mysql INSURANCE] - Mysql binlog reader stopped 
[TRACE] 2025-04-30 10:05:33.206 - [db - kafka transformer][S mysql INSURANCE] - Incremental sync completed 
[TRACE] 2025-04-30 10:05:33.211 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node stopped: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746007376352 
[TRACE] 2025-04-30 10:05:33.211 - [db - kafka transformer][S mysql INSURANCE] - PDK connector node released: HazelcastSourcePdkDataNode_61ea522c-7478-43c7-a131-a992a919a17b_1746007376352 
[TRACE] 2025-04-30 10:05:33.212 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] schema data cleaned 
[TRACE] 2025-04-30 10:05:33.213 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] monitor closed 
[TRACE] 2025-04-30 10:05:33.214 - [db - kafka transformer][S mysql INSURANCE] - Node S mysql INSURANCE[61ea522c-7478-43c7-a131-a992a919a17b] close complete, cost 114 ms 
[TRACE] 2025-04-30 10:05:33.214 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] running status set to false 
[INFO ] 2025-04-30 10:05:33.226 - [db - kafka transformer][T kafka] - Stopping kafka_enhanced 
[TRACE] 2025-04-30 10:05:33.227 - [db - kafka transformer][T kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746007376431 
[TRACE] 2025-04-30 10:05:33.227 - [db - kafka transformer][T kafka] - PDK connector node released: HazelcastTargetPdkDataNode_238081c7-0aa7-4315-bd2e-2e046accd3fc_1746007376431 
[TRACE] 2025-04-30 10:05:33.227 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] schema data cleaned 
[TRACE] 2025-04-30 10:05:33.227 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] monitor closed 
[TRACE] 2025-04-30 10:05:33.432 - [db - kafka transformer][T kafka] - Node T kafka[238081c7-0aa7-4315-bd2e-2e046accd3fc] close complete, cost 13 ms 
[TRACE] 2025-04-30 10:05:37.692 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:05:37.693 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@21b9dacc 
[TRACE] 2025-04-30 10:05:37.695 - [db - kafka transformer] - Stop task milestones: 6811c97ff4d18b328154cf7d(db - kafka transformer)  
[TRACE] 2025-04-30 10:05:37.848 - [db - kafka transformer] - Stopped task aspect(s) 
[TRACE] 2025-04-30 10:05:37.848 - [db - kafka transformer] - Snapshot order controller have been removed 
[INFO ] 2025-04-30 10:05:37.848 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:05:42.859 - [db - kafka transformer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-30 10:05:42.860 - [db - kafka transformer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@21b9dacc 
[TRACE] 2025-04-30 10:05:42.860 - [db - kafka transformer] - Stopped task aspect(s) 
[INFO ] 2025-04-30 10:05:42.860 - [db - kafka transformer] - Task stopped. 
[TRACE] 2025-04-30 10:05:42.943 - [db - kafka transformer] - Remove memory task client succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
[TRACE] 2025-04-30 10:05:42.943 - [db - kafka transformer] - Destroy memory task client cache succeed, task: db - kafka transformer[6811c97ff4d18b328154cf7d] 
