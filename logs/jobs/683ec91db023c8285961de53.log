[TRACE] 2025-06-04 04:17:08.154 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:08.159 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.159 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.160 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.161 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:17:08.161 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:17:08.566 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:17:08.566 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010628447 
[TRACE] 2025-06-04 04:17:08.567 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010628447 
[TRACE] 2025-06-04 04:17:08.567 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.569 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:17:08.569 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:17:08.579 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:17:08.614 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.615 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:17:08.615 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 45 ms 
[TRACE] 2025-06-04 04:17:08.833 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:17:08.833 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-03e42bc6-1b49-4100-9c47-8ed36cf9533f 
[INFO ] 2025-06-04 04:17:08.834 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-03e42bc6-1b49-4100-9c47-8ed36cf9533f 
[INFO ] 2025-06-04 04:17:08.834 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:17:08.838 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 7 ms 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] running status set to false 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] schema data cleaned 
[TRACE] 2025-06-04 04:17:08.865 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] monitor closed 
[TRACE] 2025-06-04 04:17:08.866 - [TAP-6799(100)][53f0606d-3528-4aa5-ad0b-e71efbe2c405] - Node 53f0606d-3528-4aa5-ad0b-e71efbe2c405[53f0606d-3528-4aa5-ad0b-e71efbe2c405] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:08.870 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:08.871 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:08.871 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:08.948 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.949 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:08.950 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:17:09.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:17:09.271 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629147 
[TRACE] 2025-06-04 04:17:09.271 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629147 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:17:09.272 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] running status set to false 
[INFO ] 2025-06-04 04:17:09.473 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c324815b-4ef4-4b79-831d-fa9b0a233ebd 
[TRACE] 2025-06-04 04:17:09.473 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] schema data cleaned 
[INFO ] 2025-06-04 04:17:09.474 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c324815b-4ef4-4b79-831d-fa9b0a233ebd 
[TRACE] 2025-06-04 04:17:09.474 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] monitor closed 
[INFO ] 2025-06-04 04:17:09.474 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.474 - [TAP-6799(100)][66e17c34-77dd-4135-a109-1b4524b89f07] - Node 66e17c34-77dd-4135-a109-1b4524b89f07[66e17c34-77dd-4135-a109-1b4524b89f07] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:09.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:09.477 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:09.478 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:09.478 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:17:09.566 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:17:09.567 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:17:09.701 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629608 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010629608 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:17:09.703 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:17:09.904 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-218b0196-b807-4dd3-8d5d-bc4dd8db5f1a 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-218b0196-b807-4dd3-8d5d-bc4dd8db5f1a 
[INFO ] 2025-06-04 04:17:09.905 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:17:09.906 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 9 ms 
[TRACE] 2025-06-04 04:17:10.101 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:17:10.103 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-e6165862-8df8-43b9-b0fa-6dfbc4d1a1c3 
[INFO ] 2025-06-04 04:17:10.104 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-e6165862-8df8-43b9-b0fa-6dfbc4d1a1c3 
[INFO ] 2025-06-04 04:17:10.104 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:17:10.105 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:17:10.107 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] running status set to false 
[TRACE] 2025-06-04 04:17:10.108 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] schema data cleaned 
[TRACE] 2025-06-04 04:17:10.108 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] monitor closed 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)][1cc89333-71b2-4ffb-bea3-1bbc988a71ad] - Node 1cc89333-71b2-4ffb-bea3-1bbc988a71ad[1cc89333-71b2-4ffb-bea3-1bbc988a71ad] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:17:10.110 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:17:10.111 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:17:10.111 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.494 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.495 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:50.696 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:50.701 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:50.822 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:50.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790714 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790714 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.823 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:50.842 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790727 
[TRACE] 2025-06-04 04:19:50.883 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010790727 
[TRACE] 2025-06-04 04:19:50.884 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.884 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:50.888 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.888 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:50.907 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-15197d25-1d29-4395-8d82-e04152c17881 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-15197d25-1d29-4395-8d82-e04152c17881 
[INFO ] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.984 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.985 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:50.985 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] running status set to false 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] schema data cleaned 
[TRACE] 2025-06-04 04:19:50.997 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] monitor closed 
[TRACE] 2025-06-04 04:19:50.998 - [TAP-6799(100)][08366e4c-72c0-4dc8-a43e-d112a297f6be] - Node 08366e4c-72c0-4dc8-a43e-d112a297f6be[08366e4c-72c0-4dc8-a43e-d112a297f6be] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:50.999 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.043 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:51.043 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f000af50-49a4-4161-bcef-23633817323d 
[INFO ] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f000af50-49a4-4161-bcef-23633817323d 
[INFO ] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.044 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] running status set to false 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.055 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] monitor closed 
[TRACE] 2025-06-04 04:19:51.056 - [TAP-6799(100)][b7931ff8-dd16-4f92-b3b4-0ba306e310ab] - Node b7931ff8-dd16-4f92-b3b4-0ba306e310ab[b7931ff8-dd16-4f92-b3b4-0ba306e310ab] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.056 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.057 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.078 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.078 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.079 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.139 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.140 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.141 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.347 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791323 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791323 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.425 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.481 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791331 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791331 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.585 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:19:51.589 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] running status set to false 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] monitor closed 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][3736b996-751c-4d21-850a-7e51c94d1f6e] - Node 3736b996-751c-4d21-850a-7e51c94d1f6e[3736b996-751c-4d21-850a-7e51c94d1f6e] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8a94daa5-6483-4278-9ef7-194e0af07300 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8a94daa5-6483-4278-9ef7-194e0af07300 
[INFO ] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.593 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.656 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:51.656 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3fbe314a-c513-47bc-8ebb-5fc56c39f865 
[INFO ] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3fbe314a-c513-47bc-8ebb-5fc56c39f865 
[INFO ] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] running status set to false 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] monitor closed 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)][19a102d5-e751-4d63-b4cd-a2c2100b6f54] - Node 19a102d5-e751-4d63-b4cd-a2c2100b6f54[19a102d5-e751-4d63-b4cd-a2c2100b6f54] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:51.657 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.660 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.740 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.742 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:51.742 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:51.757 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:51.785 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:51.786 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791695 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791695 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.787 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010791775 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010791775 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:51.874 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:51.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:19:51.966 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791775 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010791775 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:51.967 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-4d5c5c94-82d9-4b36-85ff-7f53c35739f7 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-4d5c5c94-82d9-4b36-85ff-7f53c35739f7 
[INFO ] 2025-06-04 04:19:51.974 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.975 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:51.975 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.126 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:52.127 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:52.132 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-b50cfb89-4d4d-4136-86a3-7ad38670c24a 
[INFO ] 2025-06-04 04:19:52.132 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-b50cfb89-4d4d-4136-86a3-7ad38670c24a 
[INFO ] 2025-06-04 04:19:52.141 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.142 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.142 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:52.143 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:19:52.190 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] running status set to false 
[TRACE] 2025-06-04 04:19:52.190 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.193 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] monitor closed 
[TRACE] 2025-06-04 04:19:52.194 - [TAP-6799(100)][af63537f-21ba-433d-9fc0-eb901b29e142] - Node af63537f-21ba-433d-9fc0-eb901b29e142[af63537f-21ba-433d-9fc0-eb901b29e142] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.196 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.196 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.197 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.245 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.246 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:52.251 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a102bdda-7350-403f-87e9-0a1e6fcc467a 
[INFO ] 2025-06-04 04:19:52.252 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a102bdda-7350-403f-87e9-0a1e6fcc467a 
[INFO ] 2025-06-04 04:19:52.252 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.253 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.253 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.254 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 9 ms 
[TRACE] 2025-06-04 04:19:52.336 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] running status set to false 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] monitor closed 
[TRACE] 2025-06-04 04:19:52.339 - [TAP-6799(100)][fe166732-edf5-4461-8454-e981de0be14c] - Node fe166732-edf5-4461-8454-e981de0be14c[fe166732-edf5-4461-8454-e981de0be14c] close complete, cost 1 ms 
[INFO ] 2025-06-04 04:19:52.340 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0c4928f1-16eb-4096-b178-fe85e89981a4 
[INFO ] 2025-06-04 04:19:52.341 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0c4928f1-16eb-4096-b178-fe85e89981a4 
[INFO ] 2025-06-04 04:19:52.341 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:52.345 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:19:52.347 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.348 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.384 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.385 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.386 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:52.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:52.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-49f81d3a-d2e1-4a92-8e81-74964df8de96 
[INFO ] 2025-06-04 04:19:52.447 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-49f81d3a-d2e1-4a92-8e81-74964df8de96 
[INFO ] 2025-06-04 04:19:52.447 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.450 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] running status set to false 
[TRACE] 2025-06-04 04:19:52.450 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] monitor closed 
[TRACE] 2025-06-04 04:19:52.451 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:52.452 - [TAP-6799(100)][084b95c7-32e9-43a8-aba5-723d2b518bb0] - Node 084b95c7-32e9-43a8-aba5-723d2b518bb0[084b95c7-32e9-43a8-aba5-723d2b518bb0] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:52.452 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 7 ms 
[TRACE] 2025-06-04 04:19:52.454 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.455 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.455 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.525 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.526 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010792423 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010792423 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.529 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:52.530 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.896 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:19:52.898 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] running status set to false 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] monitor closed 
[INFO ] 2025-06-04 04:19:52.899 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3305b937-73e7-46a5-a013-d5b2d27b8687 
[TRACE] 2025-06-04 04:19:52.899 - [TAP-6799(100)][049010a0-e7ed-445d-87de-100a17d0dd41] - Node 049010a0-e7ed-445d-87de-100a17d0dd41[049010a0-e7ed-445d-87de-100a17d0dd41] close complete, cost 1 ms 
[INFO ] 2025-06-04 04:19:52.900 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3305b937-73e7-46a5-a013-d5b2d27b8687 
[INFO ] 2025-06-04 04:19:52.900 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:52.901 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:52.902 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:52.902 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:52.903 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:52.991 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.992 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:52.993 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:53.142 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010793048 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010793048 
[TRACE] 2025-06-04 04:19:53.145 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.146 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:53.146 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:53.351 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:53.354 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-16b89efb-8b87-452e-b6b8-746e84859c65 
[INFO ] 2025-06-04 04:19:53.354 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-16b89efb-8b87-452e-b6b8-746e84859c65 
[INFO ] 2025-06-04 04:19:53.355 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:53.356 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:19:53.549 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-75da6c48-e2c0-41cc-b7e9-0a3f6ab88104 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-75da6c48-e2c0-41cc-b7e9-0a3f6ab88104 
[INFO ] 2025-06-04 04:19:53.551 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] running status set to false 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] monitor closed 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:53.552 - [TAP-6799(100)][f486eda8-a2bb-47af-9b31-06c95bd3d62a] - Node f486eda8-a2bb-47af-9b31-06c95bd3d62a[f486eda8-a2bb-47af-9b31-06c95bd3d62a] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:53.553 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:53.553 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:53.554 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:53.759 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.075 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.077 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.077 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.078 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:19:56.079 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:19:56.210 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:19:56.210 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010796106 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010796106 
[TRACE] 2025-06-04 04:19:56.211 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.212 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:19:56.244 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 34 ms 
[TRACE] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ecb32f97-c6a5-40aa-9eef-ae5b14a28b36 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ecb32f97-c6a5-40aa-9eef-ae5b14a28b36 
[INFO ] 2025-06-04 04:19:56.378 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.380 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.380 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:19:56.381 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] running status set to false 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] monitor closed 
[TRACE] 2025-06-04 04:19:56.392 - [TAP-6799(100)][07339ff6-6769-465e-934c-5b93541b5b04] - Node 07339ff6-6769-465e-934c-5b93541b5b04[07339ff6-6769-465e-934c-5b93541b5b04] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:56.393 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:56.458 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.458 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.459 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.613 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:56.613 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:56.616 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796500 
[TRACE] 2025-06-04 04:19:56.616 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796500 
[TRACE] 2025-06-04 04:19:56.617 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.617 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:56.822 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:19:56.868 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-9e981c57-cf48-4e04-b31c-a81ac1bc4c75 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-9e981c57-cf48-4e04-b31c-a81ac1bc4c75 
[INFO ] 2025-06-04 04:19:56.869 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:56.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] running status set to false 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] schema data cleaned 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] monitor closed 
[TRACE] 2025-06-04 04:19:56.871 - [TAP-6799(100)][ac798dea-55f6-443d-a7ed-1b4e779441b2] - Node ac798dea-55f6-443d-a7ed-1b4e779441b2[ac798dea-55f6-443d-a7ed-1b4e779441b2] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:56.872 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:19:56.950 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:56.951 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:19:56.951 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:19:57.119 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:19:57.119 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:19:57.120 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796999 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010796999 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.121 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:19:57.307 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:57.307 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-27f921f2-5cce-4b53-bedc-f22f9e4fd736 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-27f921f2-5cce-4b53-bedc-f22f9e4fd736 
[INFO ] 2025-06-04 04:19:57.309 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:19:57.310 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:19:57.473 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:19:57.474 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c27b75f8-4ab1-403f-89ea-6a972073eda6 
[INFO ] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c27b75f8-4ab1-403f-89ea-6a972073eda6 
[INFO ] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.475 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:19:57.476 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] running status set to false 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] schema data cleaned 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] monitor closed 
[TRACE] 2025-06-04 04:19:57.477 - [TAP-6799(100)][1cb76505-90ee-4802-b7fc-709d97a6a5ba] - Node 1cb76505-90ee-4802-b7fc-709d97a6a5ba[1cb76505-90ee-4802-b7fc-709d97a6a5ba] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:19:57.478 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.509 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:00.510 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.642 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:00.642 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010800552 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010800552 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.643 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:00.648 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:00.648 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:00.670 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 22 ms 
[TRACE] 2025-06-04 04:20:00.820 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0596476a-edab-44fb-a7a4-fda0fc3f770e 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0596476a-edab-44fb-a7a4-fda0fc3f770e 
[INFO ] 2025-06-04 04:20:00.821 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.822 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:00.830 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:00.830 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] running status set to false 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] schema data cleaned 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] monitor closed 
[TRACE] 2025-06-04 04:20:00.831 - [TAP-6799(100)][8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] - Node 8b95dcde-ac55-4ecf-b9eb-2f561c8ec177[8b95dcde-ac55-4ecf-b9eb-2f561c8ec177] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:00.832 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:00.932 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010800993 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010800993 
[TRACE] 2025-06-04 04:20:01.116 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.117 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:01.117 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.324 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] running status set to false 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] monitor closed 
[TRACE] 2025-06-04 04:20:01.325 - [TAP-6799(100)][eb0b59af-1c21-4e1b-bb71-6b8201e4416f] - Node eb0b59af-1c21-4e1b-bb71-6b8201e4416f[eb0b59af-1c21-4e1b-bb71-6b8201e4416f] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6f7a744f-75d6-4816-a0ce-3f515369e7f7 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6f7a744f-75d6-4816-a0ce-3f515369e7f7 
[INFO ] 2025-06-04 04:20:01.326 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:01.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:01.328 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:01.398 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:01.399 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.563 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:01.563 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010801454 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010801454 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.565 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:01.740 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:01.741 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-cd9232eb-8fcb-4857-8cd3-1c5aa23d127e 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-cd9232eb-8fcb-4857-8cd3-1c5aa23d127e 
[INFO ] 2025-06-04 04:20:01.744 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:01.745 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:20:01.907 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3bc5e319-b6c3-4133-b63a-d2b2fa8734b1 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3bc5e319-b6c3-4133-b63a-d2b2fa8734b1 
[INFO ] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:01.910 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:01.911 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] running status set to false 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] schema data cleaned 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] monitor closed 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)][9a581254-b0b8-4da9-9411-416c8b6becce] - Node 9a581254-b0b8-4da9-9411-416c8b6becce[9a581254-b0b8-4da9-9411-416c8b6becce] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:01.912 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:01.916 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010801935 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010801935 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:02.025 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:02.047 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 20 ms 
[TRACE] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-d44626a6-d9c0-4a2f-a9a4-487b49ed24ef 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-d44626a6-d9c0-4a2f-a9a4-487b49ed24ef 
[INFO ] 2025-06-04 04:20:02.204 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:02.205 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.210 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] running status set to false 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] monitor closed 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)][8e968e4c-55e0-4f87-8b2d-6deca21d56a7] - Node 8e968e4c-55e0-4f87-8b2d-6deca21d56a7[8e968e4c-55e0-4f87-8b2d-6deca21d56a7] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:02.211 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:02.271 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.272 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.272 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802310 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802310 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.416 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:02.596 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.596 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-5aab8944-1a0d-440f-8600-b50171c0ba5a 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-5aab8944-1a0d-440f-8600-b50171c0ba5a 
[INFO ] 2025-06-04 04:20:02.598 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] running status set to false 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.601 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] monitor closed 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)][fa4ce548-9479-4767-b9d2-76961702ff13] - Node fa4ce548-9479-4767-b9d2-76961702ff13[fa4ce548-9479-4767-b9d2-76961702ff13] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:02.602 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:02.699 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:02.700 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802735 
[TRACE] 2025-06-04 04:20:02.851 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010802735 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:02.852 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:03.065 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-1675312b-3833-4d8e-ba2a-2e618b4a7371 
[INFO ] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-1675312b-3833-4d8e-ba2a-2e618b4a7371 
[INFO ] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:03.066 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.167 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.168 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:03.256 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:20:03.256 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] running status set to false 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] monitor closed 
[TRACE] 2025-06-04 04:20:03.257 - [TAP-6799(100)][d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] - Node d8afe0fe-56a0-4b1e-88bf-e894bac43bb4[d8afe0fe-56a0-4b1e-88bf-e894bac43bb4] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:03.257 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-65f6e354-1e9c-40a1-b913-c5d76911ee17 
[INFO ] 2025-06-04 04:20:03.257 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-65f6e354-1e9c-40a1-b913-c5d76911ee17 
[INFO ] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:03.258 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.259 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010803213 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010803213 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.319 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:03.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 04:20:03.524 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:03.524 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6d7c9992-7e81-456c-8cc0-7fe30df3c09b 
[INFO ] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6d7c9992-7e81-456c-8cc0-7fe30df3c09b 
[INFO ] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:03.525 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] running status set to false 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] monitor closed 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)][06604b9f-8f89-4ff5-a851-d06044e0ea4a] - Node 06604b9f-8f89-4ff5-a851-d06044e0ea4a[06604b9f-8f89-4ff5-a851-d06044e0ea4a] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.534 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.599 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.731 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:03.731 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010803638 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010803638 
[TRACE] 2025-06-04 04:20:03.732 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.733 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:03.923 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.923 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-af4a7f86-3500-4d9f-b4b3-8593eccb34a8 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-af4a7f86-3500-4d9f-b4b3-8593eccb34a8 
[INFO ] 2025-06-04 04:20:03.924 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.925 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.925 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] running status set to false 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] schema data cleaned 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] monitor closed 
[TRACE] 2025-06-04 04:20:03.927 - [TAP-6799(100)][0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] - Node 0bb8aa78-bb23-45c4-a498-ba6ea37a7e30[0bb8aa78-bb23-45c4-a498-ba6ea37a7e30] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:03.928 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:03.988 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:03.989 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:04.137 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010804029 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010804029 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:04.138 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:04.329 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f75f8bc9-8714-41b9-81bd-2e6746a8020c 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f75f8bc9-8714-41b9-81bd-2e6746a8020c 
[INFO ] 2025-06-04 04:20:04.332 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:04.333 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c861afdf-ddc8-4dab-b23d-7bbdda83799b 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c861afdf-ddc8-4dab-b23d-7bbdda83799b 
[INFO ] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.495 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:04.495 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:04.497 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] running status set to false 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] schema data cleaned 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] monitor closed 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)][7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] - Node 7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c[7fd705e8-7727-4e6e-9f96-3cf3f27cdc8c] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:04.498 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:04.499 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:04.499 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.027 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:06.153 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:06.153 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010806056 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010806056 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.155 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:06.156 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:06.157 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:06.175 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 18 ms 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a55c8915-edde-4b13-8e06-e03955572b21 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a55c8915-edde-4b13-8e06-e03955572b21 
[INFO ] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.337 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:06.338 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:06.341 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] running status set to false 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] monitor closed 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)][291b55b5-b5a2-459b-a16b-89fe684276a3] - Node 291b55b5-b5a2-459b-a16b-89fe684276a3[291b55b5-b5a2-459b-a16b-89fe684276a3] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:06.342 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.422 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.568 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:06.568 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806460 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806460 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:06.576 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 8 ms 
[TRACE] 2025-06-04 04:20:06.836 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-09e2a6a4-f966-4983-9e00-118d8beaa9cb 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-09e2a6a4-f966-4983-9e00-118d8beaa9cb 
[INFO ] 2025-06-04 04:20:06.838 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.839 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.839 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] running status set to false 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] schema data cleaned 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] monitor closed 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)][0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] - Node 0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1[0d6162f0-aa0c-4e1f-8d56-5f81eb31e6e1] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:06.840 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.921 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:06.922 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:07.059 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:07.059 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806966 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010806966 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:07.264 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f4847135-3f17-4c83-8586-7d95a6a1b6c2 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f4847135-3f17-4c83-8586-7d95a6a1b6c2 
[INFO ] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.267 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:07.268 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] running status set to false 
[TRACE] 2025-06-04 04:20:07.443 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.444 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] monitor closed 
[TRACE] 2025-06-04 04:20:07.444 - [TAP-6799(100)][09028c31-8bb5-4e5e-a049-cf7048c80e5d] - Node 09028c31-8bb5-4e5e-a049-cf7048c80e5d[09028c31-8bb5-4e5e-a049-cf7048c80e5d] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-edde82d0-db8e-42cf-86c1-68decf24a1e1 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-edde82d0-db8e-42cf-86c1-68decf24a1e1 
[INFO ] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:07.445 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:07.446 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.558 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 04:20:09.559 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 04:20:09.673 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010809580 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749010809580 
[TRACE] 2025-06-04 04:20:09.674 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.675 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 04:20:09.679 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:09.679 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 04:20:09.700 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 21 ms 
[TRACE] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-64d62842-4df9-4fc9-a60c-f410a21769e2 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-64d62842-4df9-4fc9-a60c-f410a21769e2 
[INFO ] 2025-06-04 04:20:09.848 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 04:20:09.849 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] running status set to false 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] schema data cleaned 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] monitor closed 
[TRACE] 2025-06-04 04:20:09.859 - [TAP-6799(100)][5b63de86-64ce-407f-a045-9c70ab5d8fbd] - Node 5b63de86-64ce-407f-a045-9c70ab5d8fbd[5b63de86-64ce-407f-a045-9c70ab5d8fbd] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:09.860 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:09.927 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010809964 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010809964 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:10.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] running status set to false 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.222 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] monitor closed 
[TRACE] 2025-06-04 04:20:10.223 - [TAP-6799(100)][21433e6b-d312-4a39-a72e-8728ed7bdf4e] - Node 21433e6b-d312-4a39-a72e-8728ed7bdf4e[21433e6b-d312-4a39-a72e-8728ed7bdf4e] close complete, cost 0 ms 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-44c5e9b2-fb77-4d24-9530-1631fa8fbe3d 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-44c5e9b2-fb77-4d24-9530-1631fa8fbe3d 
[INFO ] 2025-06-04 04:20:10.223 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:10.224 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:10.225 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:10.225 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.298 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 04:20:10.299 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.299 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 04:20:10.432 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 04:20:10.433 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010810337 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749010810337 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 04:20:10.435 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 04:20:10.604 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a28adca9-3cfe-43be-9933-dab247958936 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a28adca9-3cfe-43be-9933-dab247958936 
[INFO ] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.605 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.606 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 04:20:10.606 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 04:20:10.747 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-43817581-d946-4ba3-a8fc-fb7cdeec1175 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-43817581-d946-4ba3-a8fc-fb7cdeec1175 
[INFO ] 2025-06-04 04:20:10.748 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.749 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.749 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 1 ms 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] running status set to false 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] schema data cleaned 
[TRACE] 2025-06-04 04:20:10.750 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] monitor closed 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)][a5ede849-34da-4369-b0d1-7d96b9bbccc7] - Node a5ede849-34da-4369-b0d1-7d96b9bbccc7[a5ede849-34da-4369-b0d1-7d96b9bbccc7] close complete, cost 0 ms 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 04:20:10.751 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 06:44:30.239 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:30.240 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:30.240 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:30.240 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] start preload schema,table counts: 0 
[TRACE] 2025-06-04 06:44:30.241 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:30.242 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:30.242 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:30.242 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:30.243 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 06:44:30.243 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 06:44:30.690 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 06:44:30.691 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749019470294 
[TRACE] 2025-06-04 06:44:30.691 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 06:44:30.691 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749019470294 
[TRACE] 2025-06-04 06:44:30.692 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 06:44:30.693 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 06:44:30.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 15 ms 
[TRACE] 2025-06-04 06:44:30.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 06:44:30.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 06:44:30.937 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 43 ms 
[TRACE] 2025-06-04 06:44:30.985 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 06:44:30.985 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6b5e6efe-4bff-4a51-8c94-7ae2d273d290 
[INFO ] 2025-06-04 06:44:30.987 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6b5e6efe-4bff-4a51-8c94-7ae2d273d290 
[INFO ] 2025-06-04 06:44:30.987 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 06:44:30.991 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 06:44:30.991 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 06:44:30.991 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 8 ms 
[TRACE] 2025-06-04 06:44:31.017 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] running status set to false 
[TRACE] 2025-06-04 06:44:31.017 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.017 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] monitor closed 
[TRACE] 2025-06-04 06:44:31.020 - [TAP-6799(100)][d0b20bdb-45c5-41b0-ad2c-e82716488906] - Node d0b20bdb-45c5-41b0-ad2c-e82716488906[d0b20bdb-45c5-41b0-ad2c-e82716488906] close complete, cost 1 ms 
[TRACE] 2025-06-04 06:44:31.021 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 06:44:31.022 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 06:44:31.022 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 06:44:31.022 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 06:44:31.112 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:31.112 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] start preload schema,table counts: 0 
[TRACE] 2025-06-04 06:44:31.113 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:31.113 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.113 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.113 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.276 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 06:44:31.276 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 06:44:31.278 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749019471161 
[TRACE] 2025-06-04 06:44:31.278 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749019471161 
[TRACE] 2025-06-04 06:44:31.278 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.278 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 06:44:31.484 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 06:44:31.492 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 06:44:31.492 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-deb4284b-1c7c-4575-860b-e000488ccb44 
[INFO ] 2025-06-04 06:44:31.493 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-deb4284b-1c7c-4575-860b-e000488ccb44 
[INFO ] 2025-06-04 06:44:31.493 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.494 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 06:44:31.494 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] running status set to false 
[TRACE] 2025-06-04 06:44:31.495 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 06:44:31.495 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.495 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] monitor closed 
[TRACE] 2025-06-04 06:44:31.495 - [TAP-6799(100)][7c621899-ffb1-4070-bd3f-7c569f4b84df] - Node 7c621899-ffb1-4070-bd3f-7c569f4b84df[7c621899-ffb1-4070-bd3f-7c569f4b84df] close complete, cost 1 ms 
[TRACE] 2025-06-04 06:44:31.497 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 06:44:31.497 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 06:44:31.497 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 06:44:31.497 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 06:44:31.580 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] start preload schema,table counts: 0 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.582 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 06:44:31.582 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 06:44:31.583 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 06:44:31.730 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 06:44:31.730 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749019471616 
[TRACE] 2025-06-04 06:44:31.730 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749019471616 
[TRACE] 2025-06-04 06:44:31.730 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.731 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 06:44:31.731 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 06:44:31.932 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 06:44:31.935 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a24dadf3-1bfb-4c4c-bba3-45bda1c8ff1a 
[INFO ] 2025-06-04 06:44:31.935 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a24dadf3-1bfb-4c4c-bba3-45bda1c8ff1a 
[INFO ] 2025-06-04 06:44:31.935 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.937 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 06:44:31.937 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 06:44:31.937 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 06:44:32.156 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 06:44:32.158 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ce191db8-d110-42b0-9ffc-aba406ff9139 
[INFO ] 2025-06-04 06:44:32.158 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ce191db8-d110-42b0-9ffc-aba406ff9139 
[INFO ] 2025-06-04 06:44:32.158 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 06:44:32.159 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 06:44:32.159 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 06:44:32.160 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 4 ms 
[TRACE] 2025-06-04 06:44:32.161 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] running status set to false 
[TRACE] 2025-06-04 06:44:32.162 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] schema data cleaned 
[TRACE] 2025-06-04 06:44:32.162 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] monitor closed 
[TRACE] 2025-06-04 06:44:32.162 - [TAP-6799(100)][99638fba-e015-4fba-b50d-16b4d0608c48] - Node 99638fba-e015-4fba-b50d-16b4d0608c48[99638fba-e015-4fba-b50d-16b4d0608c48] close complete, cost 0 ms 
[TRACE] 2025-06-04 06:44:32.163 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 06:44:32.163 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 06:44:32.163 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 06:44:32.242 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:00:21.404 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:21.405 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:00:21.409 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:21.409 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:00:21.410 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:00:21.560 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:00:21.560 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:00:21.562 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024021431 
[TRACE] 2025-06-04 08:00:21.562 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024021431 
[TRACE] 2025-06-04 08:00:21.563 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:00:21.563 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:00:21.603 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 12 ms 
[TRACE] 2025-06-04 08:00:21.603 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:00:21.603 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:00:21.603 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 44 ms 
[TRACE] 2025-06-04 08:00:21.829 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:00:21.832 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ff5998d5-9648-4a4e-be28-f75bd7863835 
[INFO ] 2025-06-04 08:00:21.832 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ff5998d5-9648-4a4e-be28-f75bd7863835 
[INFO ] 2025-06-04 08:00:21.837 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:00:21.837 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:00:21.837 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:00:21.837 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 8 ms 
[TRACE] 2025-06-04 08:00:21.861 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] running status set to false 
[TRACE] 2025-06-04 08:00:21.861 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] schema data cleaned 
[TRACE] 2025-06-04 08:00:21.861 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] monitor closed 
[TRACE] 2025-06-04 08:00:21.865 - [TAP-6799(100)][bed6bee2-3c1b-4fa6-a540-bf9389a810a8] - Node bed6bee2-3c1b-4fa6-a540-bf9389a810a8[bed6bee2-3c1b-4fa6-a540-bf9389a810a8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:00:21.866 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:00:21.867 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:00:21.868 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:00:21.868 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:00:21.965 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:21.966 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024022020 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024022020 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:00:22.136 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:00:22.343 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:00:22.346 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f6856253-5e76-474e-a532-01f28b52af82 
[INFO ] 2025-06-04 08:00:22.347 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f6856253-5e76-474e-a532-01f28b52af82 
[INFO ] 2025-06-04 08:00:22.347 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.348 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.348 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] running status set to false 
[TRACE] 2025-06-04 08:00:22.348 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:00:22.349 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.349 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:00:22.349 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] monitor closed 
[TRACE] 2025-06-04 08:00:22.350 - [TAP-6799(100)][03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] - Node 03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656[03f6f94a-3c11-4f8d-a7f9-8e4b79b4f656] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:00:22.350 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:00:22.350 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:00:22.351 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:00:22.351 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:00:22.438 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:00:22.438 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:22.438 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:22.438 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:00:22.439 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:00:22.587 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:00:22.587 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024022491 
[TRACE] 2025-06-04 08:00:22.587 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024022491 
[TRACE] 2025-06-04 08:00:22.587 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.587 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:00:22.769 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:00:22.770 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:00:22.771 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0be8bc47-1f9f-48e6-86c1-ff0ec5651140 
[INFO ] 2025-06-04 08:00:22.771 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0be8bc47-1f9f-48e6-86c1-ff0ec5651140 
[INFO ] 2025-06-04 08:00:22.771 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.772 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.772 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:00:22.773 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:00:22.939 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:00:22.939 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] running status set to false 
[TRACE] 2025-06-04 08:00:22.940 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.940 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] monitor closed 
[TRACE] 2025-06-04 08:00:22.941 - [TAP-6799(100)][6853411b-957d-4c71-818a-a327513f208d] - Node 6853411b-957d-4c71-818a-a327513f208d[6853411b-957d-4c71-818a-a327513f208d] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:00:22.941 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8e539d9c-8581-4efd-85fe-1377b75ebf61 
[INFO ] 2025-06-04 08:00:22.941 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8e539d9c-8581-4efd-85fe-1377b75ebf61 
[INFO ] 2025-06-04 08:00:22.942 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.943 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:00:22.943 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:00:22.943 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:00:22.944 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:00:22.944 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:00:22.944 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:00:23.014 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:34.601 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:34.601 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:34.601 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:34.602 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:34.761 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:34.761 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024334634 
[TRACE] 2025-06-04 08:05:34.761 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024334634 
[TRACE] 2025-06-04 08:05:34.762 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:34.762 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:34.762 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:34.787 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:34.787 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:34.787 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:34.787 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 21 ms 
[TRACE] 2025-06-04 08:05:34.960 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:34.961 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-b64352a3-3d07-4458-a73d-29300dec63d7 
[INFO ] 2025-06-04 08:05:34.961 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-b64352a3-3d07-4458-a73d-29300dec63d7 
[INFO ] 2025-06-04 08:05:34.961 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:34.961 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:34.961 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:34.962 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:34.971 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] running status set to false 
[TRACE] 2025-06-04 08:05:34.971 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] schema data cleaned 
[TRACE] 2025-06-04 08:05:34.971 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] monitor closed 
[TRACE] 2025-06-04 08:05:34.971 - [TAP-6799(100)][e6579ad8-4567-4c41-8d06-3a5d6532af02] - Node e6579ad8-4567-4c41-8d06-3a5d6532af02[e6579ad8-4567-4c41-8d06-3a5d6532af02] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:34.972 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:34.972 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:34.972 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:34.972 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.060 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.203 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:35.203 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:35.208 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024335101 
[TRACE] 2025-06-04 08:05:35.208 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024335101 
[TRACE] 2025-06-04 08:05:35.208 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.208 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:35.377 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:35.377 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:05:35.379 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] running status set to false 
[TRACE] 2025-06-04 08:05:35.383 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.383 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] monitor closed 
[TRACE] 2025-06-04 08:05:35.384 - [TAP-6799(100)][89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] - Node 89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63[89e4e42b-2ef7-4bef-85cf-d2e1fc84aa63] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a48f5154-2008-4378-82f2-60ca23b69d21 
[INFO ] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a48f5154-2008-4378-82f2-60ca23b69d21 
[INFO ] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:35.384 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 6 ms 
[TRACE] 2025-06-04 08:05:35.384 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:35.385 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:35.385 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:35.385 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:35.486 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.487 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:35.655 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:35.655 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:35.658 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024335558 
[TRACE] 2025-06-04 08:05:35.658 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024335558 
[TRACE] 2025-06-04 08:05:35.658 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.658 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:35.658 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:35.830 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:35.830 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-784d62fd-9329-4d98-8067-1eaf5e3ca01d 
[INFO ] 2025-06-04 08:05:35.830 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-784d62fd-9329-4d98-8067-1eaf5e3ca01d 
[INFO ] 2025-06-04 08:05:35.830 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.830 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.831 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:35.831 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:05:35.984 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:05:35.984 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] running status set to false 
[TRACE] 2025-06-04 08:05:35.984 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.984 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] monitor closed 
[TRACE] 2025-06-04 08:05:35.985 - [TAP-6799(100)][123bef55-0dde-4c0d-a068-98aa2325e093] - Node 123bef55-0dde-4c0d-a068-98aa2325e093[123bef55-0dde-4c0d-a068-98aa2325e093] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:35.985 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-20b43076-7c70-4ed3-80db-fe055ceb97b7 
[INFO ] 2025-06-04 08:05:35.985 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-20b43076-7c70-4ed3-80db-fe055ceb97b7 
[INFO ] 2025-06-04 08:05:35.985 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.986 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:35.986 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:35.987 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:35.987 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:35.987 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:35.987 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:35.987 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.617 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:38.732 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:05:38.732 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:38.734 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024338638 
[TRACE] 2025-06-04 08:05:38.734 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024338638 
[TRACE] 2025-06-04 08:05:38.734 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.734 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:38.734 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:38.743 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:38.744 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:05:38.757 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.757 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:38.757 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 08:05:38.762 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.762 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:05:38.762 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 18 ms 
[TRACE] 2025-06-04 08:05:38.919 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:38.919 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6d761fae-e8ae-452e-adab-51fbbf19925f 
[INFO ] 2025-06-04 08:05:38.919 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6d761fae-e8ae-452e-adab-51fbbf19925f 
[INFO ] 2025-06-04 08:05:38.919 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.920 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.920 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:38.920 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:38.928 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] running status set to false 
[TRACE] 2025-06-04 08:05:38.928 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] schema data cleaned 
[TRACE] 2025-06-04 08:05:38.928 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] monitor closed 
[TRACE] 2025-06-04 08:05:38.928 - [TAP-6799(100)][87ddb558-ef26-425e-a5ad-64350d5013e5] - Node 87ddb558-ef26-425e-a5ad-64350d5013e5[87ddb558-ef26-425e-a5ad-64350d5013e5] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:38.929 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:38.929 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:38.929 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:38.929 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.019 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024339046 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024339046 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:39.141 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:39.315 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:39.317 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-be71f99a-ee61-424c-8cdd-da4f0260a85a 
[INFO ] 2025-06-04 08:05:39.317 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-be71f99a-ee61-424c-8cdd-da4f0260a85a 
[INFO ] 2025-06-04 08:05:39.317 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.318 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.318 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:39.318 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:39.318 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] running status set to false 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] monitor closed 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)][39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] - Node 39a23d65-8ea5-48ae-9e2e-2fc1094e93f7[39a23d65-8ea5-48ae-9e2e-2fc1094e93f7] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:39.319 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.395 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:39.396 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:39.396 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:39.396 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:39.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:39.581 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024339472 
[TRACE] 2025-06-04 08:05:39.581 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024339472 
[TRACE] 2025-06-04 08:05:39.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.581 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:39.750 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:39.750 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:39.755 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0e78640a-601d-4275-bde7-6203fae77783 
[INFO ] 2025-06-04 08:05:39.755 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0e78640a-601d-4275-bde7-6203fae77783 
[INFO ] 2025-06-04 08:05:39.755 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.756 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.756 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:39.756 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 7 ms 
[TRACE] 2025-06-04 08:05:39.961 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:05:39.961 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] running status set to false 
[TRACE] 2025-06-04 08:05:39.961 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.962 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] monitor closed 
[TRACE] 2025-06-04 08:05:39.962 - [TAP-6799(100)][0225950a-08c9-46cb-96fb-cfa7b0cf8a67] - Node 0225950a-08c9-46cb-96fb-cfa7b0cf8a67[0225950a-08c9-46cb-96fb-cfa7b0cf8a67] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:39.962 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-43d2c3ac-4eb4-4e3b-b5fd-79064a7ade85 
[INFO ] 2025-06-04 08:05:39.962 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-43d2c3ac-4eb4-4e3b-b5fd-79064a7ade85 
[INFO ] 2025-06-04 08:05:39.962 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.963 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:39.964 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.360 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.361 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:40.361 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:05:40.510 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:40.510 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:40.511 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024340389 
[TRACE] 2025-06-04 08:05:40.511 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024340389 
[TRACE] 2025-06-04 08:05:40.511 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.511 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:40.511 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:40.515 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:40.515 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:05:40.534 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.534 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:05:40.535 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 19 ms 
[TRACE] 2025-06-04 08:05:40.535 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.535 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:40.535 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 20 ms 
[TRACE] 2025-06-04 08:05:40.671 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f6f27e34-42b9-4bff-af61-2de7b1ce1326 
[INFO ] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f6f27e34-42b9-4bff-af61-2de7b1ce1326 
[INFO ] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:40.672 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:40.680 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] running status set to false 
[TRACE] 2025-06-04 08:05:40.680 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.681 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] monitor closed 
[TRACE] 2025-06-04 08:05:40.681 - [TAP-6799(100)][e911eb69-253b-4c7d-a2d2-e5c75d659314] - Node e911eb69-253b-4c7d-a2d2-e5c75d659314[e911eb69-253b-4c7d-a2d2-e5c75d659314] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.681 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:40.681 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:40.681 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.755 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:40.756 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:40.877 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:40.877 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024340782 
[TRACE] 2025-06-04 08:05:40.877 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024340782 
[TRACE] 2025-06-04 08:05:40.877 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:40.877 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:41.077 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:41.089 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:05:41.089 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] running status set to false 
[INFO ] 2025-06-04 08:05:41.089 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-e751fe46-cbbb-4d94-877e-034f07e02441 
[TRACE] 2025-06-04 08:05:41.090 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.090 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] monitor closed 
[INFO ] 2025-06-04 08:05:41.090 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-e751fe46-cbbb-4d94-877e-034f07e02441 
[TRACE] 2025-06-04 08:05:41.090 - [TAP-6799(100)][f68fb10d-19cf-4688-97e7-37e536d87daf] - Node f68fb10d-19cf-4688-97e7-37e536d87daf[f68fb10d-19cf-4688-97e7-37e536d87daf] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:41.090 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:41.091 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:41.184 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:41.348 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:41.350 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024341241 
[TRACE] 2025-06-04 08:05:41.350 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024341241 
[TRACE] 2025-06-04 08:05:41.351 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.351 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:41.351 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:41.609 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:41.609 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-aa53ee6b-ea20-4de8-a58e-745f6b86a6bd 
[INFO ] 2025-06-04 08:05:41.610 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-aa53ee6b-ea20-4de8-a58e-745f6b86a6bd 
[INFO ] 2025-06-04 08:05:41.610 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.612 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.613 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:41.613 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 12 ms 
[TRACE] 2025-06-04 08:05:41.840 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:05:41.841 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] running status set to false 
[INFO ] 2025-06-04 08:05:41.841 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ee139ccb-a315-47da-a7f4-01c935de5c91 
[TRACE] 2025-06-04 08:05:41.842 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] schema data cleaned 
[INFO ] 2025-06-04 08:05:41.842 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ee139ccb-a315-47da-a7f4-01c935de5c91 
[TRACE] 2025-06-04 08:05:41.843 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] monitor closed 
[INFO ] 2025-06-04 08:05:41.843 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.844 - [TAP-6799(100)][c354d47c-7ac4-4675-9b93-b5edbc988c2c] - Node c354d47c-7ac4-4675-9b93-b5edbc988c2c[c354d47c-7ac4-4675-9b93-b5edbc988c2c] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:41.845 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:41.845 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:41.846 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 7 ms 
[TRACE] 2025-06-04 08:05:41.847 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:41.848 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:41.848 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:42.050 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:44.010 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.010 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.011 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.011 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.011 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.011 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.011 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:44.012 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.012 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.012 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:05:44.013 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.013 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:44.013 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:44.162 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:44.163 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024344032 
[TRACE] 2025-06-04 08:05:44.163 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024344032 
[TRACE] 2025-06-04 08:05:44.163 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.163 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:44.165 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:44.166 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:44.213 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 45 ms 
[TRACE] 2025-06-04 08:05:44.214 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 49 ms 
[TRACE] 2025-06-04 08:05:44.381 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:44.384 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-7d042e31-814d-4716-a142-bc7e2af060b5 
[INFO ] 2025-06-04 08:05:44.384 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-7d042e31-814d-4716-a142-bc7e2af060b5 
[INFO ] 2025-06-04 08:05:44.384 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.385 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.385 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:44.385 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:44.397 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] running status set to false 
[TRACE] 2025-06-04 08:05:44.398 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.398 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] monitor closed 
[TRACE] 2025-06-04 08:05:44.398 - [TAP-6799(100)][9c95aaa7-88e7-4b6d-97fa-d9066944e22e] - Node 9c95aaa7-88e7-4b6d-97fa-d9066944e22e[9c95aaa7-88e7-4b6d-97fa-d9066944e22e] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:44.399 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:44.400 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:44.400 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:44.400 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:44.523 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:44.524 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.524 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.524 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.524 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.524 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.525 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:44.679 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:44.679 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024344569 
[TRACE] 2025-06-04 08:05:44.679 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024344569 
[TRACE] 2025-06-04 08:05:44.679 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.679 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:44.880 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:44.883 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:44.883 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c4fa62f3-fd0a-4f37-9ed1-cd0134d94b41 
[INFO ] 2025-06-04 08:05:44.883 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c4fa62f3-fd0a-4f37-9ed1-cd0134d94b41 
[INFO ] 2025-06-04 08:05:44.884 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.884 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] running status set to false 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] schema data cleaned 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] monitor closed 
[TRACE] 2025-06-04 08:05:44.885 - [TAP-6799(100)][2ee783de-fec8-46da-a602-3b5ee21ecb38] - Node 2ee783de-fec8-46da-a602-3b5ee21ecb38[2ee783de-fec8-46da-a602-3b5ee21ecb38] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:44.886 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:44.887 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:44.887 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:44.962 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:44.962 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.962 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.962 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:44.962 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:44.963 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024345014 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024345014 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:45.139 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:45.329 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:45.329 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-7baae6d9-e0b6-48fa-bd36-25004eda65d8 
[INFO ] 2025-06-04 08:05:45.329 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-7baae6d9-e0b6-48fa-bd36-25004eda65d8 
[INFO ] 2025-06-04 08:05:45.329 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.330 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.330 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:45.488 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:05:45.488 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:05:45.489 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-2b5adbce-97f2-4d4f-9070-1e3ca7e93f50 
[INFO ] 2025-06-04 08:05:45.489 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-2b5adbce-97f2-4d4f-9070-1e3ca7e93f50 
[INFO ] 2025-06-04 08:05:45.489 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.490 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.491 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:45.491 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] running status set to false 
[TRACE] 2025-06-04 08:05:45.491 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:45.491 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] schema data cleaned 
[TRACE] 2025-06-04 08:05:45.492 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] monitor closed 
[TRACE] 2025-06-04 08:05:45.492 - [TAP-6799(100)][af981d44-36f5-4901-8229-c90658a19436] - Node af981d44-36f5-4901-8229-c90658a19436[af981d44-36f5-4901-8229-c90658a19436] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:45.492 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:45.492 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:45.492 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:45.493 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:51.314 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.315 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:51.316 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.316 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.316 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:05:51.316 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:51.460 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:51.460 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024351338 
[TRACE] 2025-06-04 08:05:51.460 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024351338 
[TRACE] 2025-06-04 08:05:51.460 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.460 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:51.463 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:51.463 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:51.487 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:05:51.488 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.488 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:51.489 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 23 ms 
[TRACE] 2025-06-04 08:05:51.494 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.495 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:05:51.495 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 24 ms 
[TRACE] 2025-06-04 08:05:51.735 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:51.736 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-ae35b479-1ba9-4eca-b648-0f3c06bfd209 
[INFO ] 2025-06-04 08:05:51.736 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-ae35b479-1ba9-4eca-b648-0f3c06bfd209 
[INFO ] 2025-06-04 08:05:51.736 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.736 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.737 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:51.743 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:51.743 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] running status set to false 
[TRACE] 2025-06-04 08:05:51.743 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] schema data cleaned 
[TRACE] 2025-06-04 08:05:51.743 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] monitor closed 
[TRACE] 2025-06-04 08:05:51.744 - [TAP-6799(100)][a8a48dcd-0237-4b0f-af0c-480c6ad33353] - Node a8a48dcd-0237-4b0f-af0c-480c6ad33353[a8a48dcd-0237-4b0f-af0c-480c6ad33353] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.744 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:51.744 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:51.744 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:51.744 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:51.827 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:52.023 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:52.023 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:52.025 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024351870 
[TRACE] 2025-06-04 08:05:52.025 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024351870 
[TRACE] 2025-06-04 08:05:52.025 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.025 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:52.025 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:52.242 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:05:52.244 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] running status set to false 
[TRACE] 2025-06-04 08:05:52.244 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.244 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] monitor closed 
[TRACE] 2025-06-04 08:05:52.245 - [TAP-6799(100)][c697db33-1f93-4749-9202-6d377031dd32] - Node c697db33-1f93-4749-9202-6d377031dd32[c697db33-1f93-4749-9202-6d377031dd32] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:52.246 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-97205081-22b1-4d35-8818-299c8b70d41e 
[INFO ] 2025-06-04 08:05:52.246 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-97205081-22b1-4d35-8818-299c8b70d41e 
[INFO ] 2025-06-04 08:05:52.247 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.247 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.247 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:52.248 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:05:52.248 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:52.248 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:52.248 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:52.321 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:52.322 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:52.322 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:52.322 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:52.322 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:52.457 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:52.457 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024352368 
[TRACE] 2025-06-04 08:05:52.457 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024352368 
[TRACE] 2025-06-04 08:05:52.457 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.457 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:52.628 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:52.629 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-41b6c168-8a45-43d7-8c2d-77a18b730b2e 
[INFO ] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-41b6c168-8a45-43d7-8c2d-77a18b730b2e 
[INFO ] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:52.631 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:52.810 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:05:52.810 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] running status set to false 
[TRACE] 2025-06-04 08:05:52.810 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.811 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] monitor closed 
[TRACE] 2025-06-04 08:05:52.811 - [TAP-6799(100)][0d66c491-e6c1-400a-8e8c-63ece08c2ffa] - Node 0d66c491-e6c1-400a-8e8c-63ece08c2ffa[0d66c491-e6c1-400a-8e8c-63ece08c2ffa] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:05:52.811 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a688dc42-2bdc-4293-800a-8b5d308d2f40 
[INFO ] 2025-06-04 08:05:52.811 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a688dc42-2bdc-4293-800a-8b5d308d2f40 
[INFO ] 2025-06-04 08:05:52.811 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.812 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:52.812 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:52.812 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:52.812 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:52.813 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:52.813 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:52.813 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.596 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:57.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:57.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.597 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:05:57.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.598 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:05:57.598 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:05:57.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:05:57.733 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024357626 
[TRACE] 2025-06-04 08:05:57.733 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024357626 
[TRACE] 2025-06-04 08:05:57.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.733 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:05:57.734 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:05:57.734 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 24 ms 
[TRACE] 2025-06-04 08:05:57.759 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:05:57.944 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 23 ms 
[TRACE] 2025-06-04 08:05:57.945 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:05:57.946 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-6069a14b-c73f-47c9-9cf6-0cb1f1dc42b3 
[INFO ] 2025-06-04 08:05:57.946 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-6069a14b-c73f-47c9-9cf6-0cb1f1dc42b3 
[INFO ] 2025-06-04 08:05:57.946 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.947 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.947 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:05:57.947 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] running status set to false 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] schema data cleaned 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] monitor closed 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)][979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] - Node 979acdb7-2a6a-4cb7-9870-8b4253a7c7b3[979acdb7-2a6a-4cb7-9870-8b4253a7c7b3] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:57.956 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:57.957 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.032 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.183 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:58.183 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:58.186 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024358083 
[TRACE] 2025-06-04 08:05:58.186 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024358083 
[TRACE] 2025-06-04 08:05:58.186 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.186 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:58.186 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:58.343 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:58.343 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-297455d4-19cd-40e3-8d11-cab37c5fdafa 
[INFO ] 2025-06-04 08:05:58.343 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-297455d4-19cd-40e3-8d11-cab37c5fdafa 
[INFO ] 2025-06-04 08:05:58.343 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:58.344 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:05:58.345 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] running status set to false 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] monitor closed 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)][bd904dd2-73bb-4b77-9b34-e726604d1462] - Node bd904dd2-73bb-4b77-9b34-e726604d1462[bd904dd2-73bb-4b77-9b34-e726604d1462] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:58.346 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:05:58.419 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.420 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.420 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.420 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:05:58.420 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024358459 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024358459 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:05:58.571 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:58.748 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:05:58.750 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-cff54656-4e64-4eee-8afe-3e256ca2992d 
[INFO ] 2025-06-04 08:05:58.750 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-cff54656-4e64-4eee-8afe-3e256ca2992d 
[INFO ] 2025-06-04 08:05:58.750 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.751 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.751 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:05:58.918 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:58.918 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:05:58.919 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-99151852-0522-4c6b-9f05-c67a199a055e 
[INFO ] 2025-06-04 08:05:58.919 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-99151852-0522-4c6b-9f05-c67a199a055e 
[INFO ] 2025-06-04 08:05:58.919 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] running status set to false 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] schema data cleaned 
[TRACE] 2025-06-04 08:05:58.920 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] monitor closed 
[TRACE] 2025-06-04 08:05:58.921 - [TAP-6799(100)][2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] - Node 2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a[2f373a0e-2bc0-45ac-8a73-4d7ed6897a2a] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:05:58.921 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:05:58.921 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:05:58.921 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:05:58.921 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:27.358 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.358 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.358 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.359 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:06:27.480 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:06:27.480 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024387386 
[TRACE] 2025-06-04 08:06:27.480 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024387386 
[TRACE] 2025-06-04 08:06:27.480 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.480 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:06:27.484 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:06:27.484 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:06:27.511 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 24 ms 
[TRACE] 2025-06-04 08:06:27.512 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 27 ms 
[TRACE] 2025-06-04 08:06:27.661 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:06:27.661 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-cc97f596-014b-4c50-baeb-ed8baa0ae4aa 
[INFO ] 2025-06-04 08:06:27.661 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-cc97f596-014b-4c50-baeb-ed8baa0ae4aa 
[INFO ] 2025-06-04 08:06:27.661 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.662 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.662 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:06:27.662 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] running status set to false 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] schema data cleaned 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] monitor closed 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)][d2d9f066-33eb-438b-a14f-22d63cc0b989] - Node d2d9f066-33eb-438b-a14f-22d63cc0b989[d2d9f066-33eb-438b-a14f-22d63cc0b989] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:27.668 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:27.669 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:27.669 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:27.870 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:28.019 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:28.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:28.022 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024387915 
[TRACE] 2025-06-04 08:06:28.022 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024387915 
[TRACE] 2025-06-04 08:06:28.022 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.022 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:28.023 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:06:28.196 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:06:28.196 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] running status set to false 
[TRACE] 2025-06-04 08:06:28.196 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.196 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] monitor closed 
[INFO ] 2025-06-04 08:06:28.196 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-a0826445-50f5-4216-b23a-2239285c4a7d 
[TRACE] 2025-06-04 08:06:28.196 - [TAP-6799(100)][4f22a2f0-acae-44ca-84b6-1554860b0dbe] - Node 4f22a2f0-acae-44ca-84b6-1554860b0dbe[4f22a2f0-acae-44ca-84b6-1554860b0dbe] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:06:28.196 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-a0826445-50f5-4216-b23a-2239285c4a7d 
[INFO ] 2025-06-04 08:06:28.196 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:28.197 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:28.292 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:28.455 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:28.455 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:28.456 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024388345 
[TRACE] 2025-06-04 08:06:28.456 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024388345 
[TRACE] 2025-06-04 08:06:28.456 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.456 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:28.456 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:06:28.621 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:06:28.621 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8b7be44b-8c22-457c-8c61-aa6f2aadcbea 
[INFO ] 2025-06-04 08:06:28.621 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8b7be44b-8c22-457c-8c61-aa6f2aadcbea 
[INFO ] 2025-06-04 08:06:28.621 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.622 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.622 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:28.622 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:28.792 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:06:28.792 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] running status set to false 
[TRACE] 2025-06-04 08:06:28.792 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.792 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] monitor closed 
[TRACE] 2025-06-04 08:06:28.792 - [TAP-6799(100)][3cabd345-c66a-4a1c-825a-42ce794e7ffb] - Node 3cabd345-c66a-4a1c-825a-42ce794e7ffb[3cabd345-c66a-4a1c-825a-42ce794e7ffb] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:06:28.792 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-44f07ea9-29d7-4d00-96e8-5cf5bdff0018 
[INFO ] 2025-06-04 08:06:28.793 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-44f07ea9-29d7-4d00-96e8-5cf5bdff0018 
[INFO ] 2025-06-04 08:06:28.793 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.793 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:06:28.793 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:06:28.793 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:28.793 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:28.793 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:28.794 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:28.794 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 1 ms 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:06:38.765 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:38.766 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:06:38.766 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:06:38.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:06:38.916 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024398792 
[TRACE] 2025-06-04 08:06:38.916 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024398792 
[TRACE] 2025-06-04 08:06:38.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:06:38.916 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:06:38.917 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:06:38.917 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:06:38.939 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:06:38.939 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:06:38.939 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:06:38.939 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 22 ms 
[TRACE] 2025-06-04 08:06:38.943 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:06:38.943 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:06:38.943 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 20 ms 
[TRACE] 2025-06-04 08:06:39.114 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:06:39.114 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-822c0ee6-59d2-4662-81fa-8f6512e7b44e 
[INFO ] 2025-06-04 08:06:39.114 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-822c0ee6-59d2-4662-81fa-8f6512e7b44e 
[INFO ] 2025-06-04 08:06:39.114 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.115 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.115 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:06:39.115 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:39.122 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] running status set to false 
[TRACE] 2025-06-04 08:06:39.122 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.122 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] monitor closed 
[TRACE] 2025-06-04 08:06:39.122 - [TAP-6799(100)][0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] - Node 0fcf8068-cd2a-4d52-abd4-37944ba3a0ac[0fcf8068-cd2a-4d52-abd4-37944ba3a0ac] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.123 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:39.123 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:39.123 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:39.123 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:39.184 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:39.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:39.184 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.184 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.185 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:39.185 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.185 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024399223 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024399223 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:39.341 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:06:39.511 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:06:39.511 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-90f4771a-239f-46af-8441-186fa8bc83d2 
[INFO ] 2025-06-04 08:06:39.511 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-90f4771a-239f-46af-8441-186fa8bc83d2 
[INFO ] 2025-06-04 08:06:39.511 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.512 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.512 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:39.512 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] running status set to false 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] monitor closed 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)][f76bb03d-0ffa-4a31-a692-1ae65406bb29] - Node f76bb03d-0ffa-4a31-a692-1ae65406bb29[f76bb03d-0ffa-4a31-a692-1ae65406bb29] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:39.514 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.593 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:39.594 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:39.594 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:06:39.594 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.594 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:39.594 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:39.747 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:39.747 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024399631 
[TRACE] 2025-06-04 08:06:39.747 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024399631 
[TRACE] 2025-06-04 08:06:39.747 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.747 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:39.748 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 4 ms 
[TRACE] 2025-06-04 08:06:39.919 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:06:39.919 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-97f817c4-15e7-46b3-869b-7277663bf537 
[INFO ] 2025-06-04 08:06:39.919 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-97f817c4-15e7-46b3-869b-7277663bf537 
[INFO ] 2025-06-04 08:06:39.919 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:39.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:39.920 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:40.061 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:06:40.061 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-7da7af8b-f668-4c89-b986-830d08547dd7 
[INFO ] 2025-06-04 08:06:40.061 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-7da7af8b-f668-4c89-b986-830d08547dd7 
[INFO ] 2025-06-04 08:06:40.061 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:40.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:06:40.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:06:40.062 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:40.064 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] running status set to false 
[TRACE] 2025-06-04 08:06:40.064 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] schema data cleaned 
[TRACE] 2025-06-04 08:06:40.064 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] monitor closed 
[TRACE] 2025-06-04 08:06:40.065 - [TAP-6799(100)][631b39eb-6f6b-4415-b124-202ca15a9fb6] - Node 631b39eb-6f6b-4415-b124-202ca15a9fb6[631b39eb-6f6b-4415-b124-202ca15a9fb6] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:40.065 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:40.065 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:40.065 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:40.266 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:06:54.444 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.445 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:06:54.574 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:06:54.574 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024414463 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024414463 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:06:54.575 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:54.596 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.596 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:06:54.596 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 21 ms 
[TRACE] 2025-06-04 08:06:54.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:06:54.597 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 23 ms 
[TRACE] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-1e5d49d8-9333-4f46-b372-e34a73c0180d 
[INFO ] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-1e5d49d8-9333-4f46-b372-e34a73c0180d 
[INFO ] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:06:54.747 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:06:54.754 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] running status set to false 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] monitor closed 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)][d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] - Node d4873dc1-2c17-4aeb-aa81-dd9425e6dcff[d4873dc1-2c17-4aeb-aa81-dd9425e6dcff] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:54.755 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:54.813 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:54.814 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:54.955 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:54.955 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024414852 
[TRACE] 2025-06-04 08:06:54.955 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024414852 
[TRACE] 2025-06-04 08:06:54.956 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:54.956 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:55.128 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:55.128 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:06:55.130 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] running status set to false 
[TRACE] 2025-06-04 08:06:55.130 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.130 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] monitor closed 
[TRACE] 2025-06-04 08:06:55.130 - [TAP-6799(100)][9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] - Node 9bebe6d3-814d-4cd7-9ec7-66b1515dd59f[9bebe6d3-814d-4cd7-9ec7-66b1515dd59f] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-7808d576-48e8-4180-ba9f-353bd99e9beb 
[INFO ] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-7808d576-48e8-4180-ba9f-353bd99e9beb 
[INFO ] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:55.131 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:06:55.132 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:55.132 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:55.132 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:55.132 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:06:55.201 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:06:55.202 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:06:55.352 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:06:55.354 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024415255 
[TRACE] 2025-06-04 08:06:55.354 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024415255 
[TRACE] 2025-06-04 08:06:55.355 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.355 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:06:55.355 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:55.548 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:06:55.549 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-bbdbd265-5d64-4669-93ce-159fc543e407 
[INFO ] 2025-06-04 08:06:55.549 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-bbdbd265-5d64-4669-93ce-159fc543e407 
[INFO ] 2025-06-04 08:06:55.549 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.549 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.549 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:06:55.690 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 6 ms 
[TRACE] 2025-06-04 08:06:55.690 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:06:55.692 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c46f1de1-86f7-49b1-b175-39857f373dfc 
[INFO ] 2025-06-04 08:06:55.692 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c46f1de1-86f7-49b1-b175-39857f373dfc 
[INFO ] 2025-06-04 08:06:55.692 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.693 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.693 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:06:55.693 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:06:55.695 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] running status set to false 
[TRACE] 2025-06-04 08:06:55.695 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] schema data cleaned 
[TRACE] 2025-06-04 08:06:55.695 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] monitor closed 
[TRACE] 2025-06-04 08:06:55.695 - [TAP-6799(100)][ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] - Node ee0c494f-9bb8-47f0-8eb4-c6724901f8a7[ee0c494f-9bb8-47f0-8eb4-c6724901f8a7] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:06:55.696 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:06:55.696 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:06:55.696 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:06:55.696 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:08.743 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:08.743 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:08.743 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:08.743 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:08.743 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:07:08.744 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:07:08.890 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:07:08.891 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024428784 
[TRACE] 2025-06-04 08:07:08.891 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024428784 
[TRACE] 2025-06-04 08:07:08.891 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:07:08.891 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:07:08.891 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:07:08.896 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:07:08.896 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:07:08.911 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:07:08.911 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:07:08.911 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 19 ms 
[TRACE] 2025-06-04 08:07:08.914 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:07:08.914 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:07:08.914 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 18 ms 
[TRACE] 2025-06-04 08:07:09.078 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:07:09.078 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0d622d6b-0e25-473d-a0ba-0d49b7c15093 
[INFO ] 2025-06-04 08:07:09.078 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0d622d6b-0e25-473d-a0ba-0d49b7c15093 
[INFO ] 2025-06-04 08:07:09.078 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.079 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.079 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:07:09.079 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:09.090 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] running status set to false 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] monitor closed 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)][2417d7eb-1ef0-495d-94a3-e01a329c5bc7] - Node 2417d7eb-1ef0-495d-94a3-e01a329c5bc7[2417d7eb-1ef0-495d-94a3-e01a329c5bc7] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:09.091 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.158 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.159 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.325 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:07:09.325 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:07:09.326 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024429224 
[TRACE] 2025-06-04 08:07:09.327 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024429224 
[TRACE] 2025-06-04 08:07:09.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:07:09.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:07:09.497 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:07:09.497 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] running status set to false 
[TRACE] 2025-06-04 08:07:09.497 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.498 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] monitor closed 
[TRACE] 2025-06-04 08:07:09.498 - [TAP-6799(100)][08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] - Node 08e6f17f-68d6-4adb-a5aa-84e9c6f9248e[08e6f17f-68d6-4adb-a5aa-84e9c6f9248e] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:07:09.498 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-b51b9329-6ca7-4b38-aa16-dabd8775c7a5 
[INFO ] 2025-06-04 08:07:09.498 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-b51b9329-6ca7-4b38-aa16-dabd8775c7a5 
[INFO ] 2025-06-04 08:07:09.498 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:09.499 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:09.558 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:09.558 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:09.558 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:09.559 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:07:09.699 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:07:09.700 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024429596 
[TRACE] 2025-06-04 08:07:09.700 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024429596 
[TRACE] 2025-06-04 08:07:09.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:07:09.700 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:09.875 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-0c96778d-7f9d-4c11-ae50-13ca26fe2c47 
[INFO ] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-0c96778d-7f9d-4c11-ae50-13ca26fe2c47 
[INFO ] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:07:09.876 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:07:10.033 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:07:10.035 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-8c446430-eee4-4ca3-90ec-84f07b04da4f 
[INFO ] 2025-06-04 08:07:10.035 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-8c446430-eee4-4ca3-90ec-84f07b04da4f 
[INFO ] 2025-06-04 08:07:10.035 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] running status set to false 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] schema data cleaned 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] monitor closed 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)][b67a2ecc-afb2-4618-9495-af5fe75bff82] - Node b67a2ecc-afb2-4618-9495-af5fe75bff82[b67a2ecc-afb2-4618-9495-af5fe75bff82] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:10.036 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:10.238 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:13.979 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:13.979 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:07:13.980 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:07:14.110 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:07:14.110 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:07:14.111 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:07:14.111 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024434000 
[TRACE] 2025-06-04 08:07:14.111 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749024434000 
[TRACE] 2025-06-04 08:07:14.111 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.111 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:07:14.112 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:07:14.112 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 19 ms 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:07:14.131 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 20 ms 
[TRACE] 2025-06-04 08:07:14.264 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:07:14.264 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-7ea24d13-fd77-4d12-b162-85face87bf30 
[INFO ] 2025-06-04 08:07:14.264 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-7ea24d13-fd77-4d12-b162-85face87bf30 
[INFO ] 2025-06-04 08:07:14.264 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.265 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.265 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:07:14.265 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:14.275 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] running status set to false 
[TRACE] 2025-06-04 08:07:14.276 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.276 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] monitor closed 
[TRACE] 2025-06-04 08:07:14.276 - [TAP-6799(100)][85712da1-5997-4525-935c-b308d8091a4d] - Node 85712da1-5997-4525-935c-b308d8091a4d[85712da1-5997-4525-935c-b308d8091a4d] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.277 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:14.277 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:14.277 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:14.277 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:14.484 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:14.484 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:14.484 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:14.484 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.485 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.485 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.485 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:07:14.627 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:07:14.628 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024434518 
[TRACE] 2025-06-04 08:07:14.628 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024434518 
[TRACE] 2025-06-04 08:07:14.628 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.628 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:07:14.797 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:14.797 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:07:14.799 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] running status set to false 
[TRACE] 2025-06-04 08:07:14.799 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.799 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] monitor closed 
[TRACE] 2025-06-04 08:07:14.799 - [TAP-6799(100)][a3a16dad-b136-437e-8fb4-8fd89e300a91] - Node a3a16dad-b136-437e-8fb4-8fd89e300a91[a3a16dad-b136-437e-8fb4-8fd89e300a91] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:07:14.799 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-c5d43558-da42-46cd-8d2c-85c950e1d11d 
[INFO ] 2025-06-04 08:07:14.799 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-c5d43558-da42-46cd-8d2c-85c950e1d11d 
[INFO ] 2025-06-04 08:07:14.800 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:14.800 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.863 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:07:14.864 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:07:14.864 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:07:15.018 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:07:15.018 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024434901 
[TRACE] 2025-06-04 08:07:15.018 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749024434901 
[TRACE] 2025-06-04 08:07:15.018 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:07:15.018 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:07:15.019 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:07:15.178 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:07:15.178 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-f9ef244b-9aa8-4cad-b9cb-cf25ea10a4fa 
[INFO ] 2025-06-04 08:07:15.178 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-f9ef244b-9aa8-4cad-b9cb-cf25ea10a4fa 
[INFO ] 2025-06-04 08:07:15.178 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:15.179 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:07:15.179 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:07:15.179 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:07:15.357 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[TRACE] 2025-06-04 08:07:15.357 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] running status set to false 
[INFO ] 2025-06-04 08:07:15.357 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-5ecb43db-e345-4f68-9a2e-c84ec6dd9273 
[TRACE] 2025-06-04 08:07:15.357 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] schema data cleaned 
[INFO ] 2025-06-04 08:07:15.357 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-5ecb43db-e345-4f68-9a2e-c84ec6dd9273 
[TRACE] 2025-06-04 08:07:15.357 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] monitor closed 
[INFO ] 2025-06-04 08:07:15.357 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:07:15.357 - [TAP-6799(100)][b4d6d19d-3fbf-4547-85e3-ec95502ed39c] - Node b4d6d19d-3fbf-4547-85e3-ec95502ed39c[b4d6d19d-3fbf-4547-85e3-ec95502ed39c] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:07:15.358 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:07:15.358 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:07:15.358 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:07:15.359 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:07:15.359 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:07:15.359 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:07:15.388 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:37:51.621 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:51.624 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] preload schema finished, cost 1 ms 
[TRACE] 2025-06-04 08:37:51.625 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:51.626 - [TAP-6799(100)][MDM_packages] - Node js_processor(MDM_packages: 8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d) enable batch process 
[TRACE] 2025-06-04 08:37:51.626 - [TAP-6799(100)][MDM_packages] - Node field_add_del_processor(MDM_packages: 3080ce1e-1a69-43e1-8283-6bfbdbad03d9) enable batch process 
[TRACE] 2025-06-04 08:37:51.626 - [TAP-6799(100)][字段改名] - Node field_rename_processor(字段改名: 7039d742-6c00-4bf3-a8ff-dfc29df80ac2) enable batch process 
[TRACE] 2025-06-04 08:37:51.807 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] running status set to false 
[TRACE] 2025-06-04 08:37:51.807 - [TAP-6799(100)][MDM_packages] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749026271656 
[TRACE] 2025-06-04 08:37:51.807 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] running status set to false 
[TRACE] 2025-06-04 08:37:51.808 - [TAP-6799(100)][MDM_packages] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7b05c753-5d0e-4b81-91b5-ef539e7f6ac8_1749026271656 
[TRACE] 2025-06-04 08:37:51.808 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] schema data cleaned 
[TRACE] 2025-06-04 08:37:51.810 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] monitor closed 
[TRACE] 2025-06-04 08:37:51.810 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[7b05c753-5d0e-4b81-91b5-ef539e7f6ac8] close complete, cost 9 ms 
[TRACE] 2025-06-04 08:37:51.854 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] running status set to false 
[TRACE] 2025-06-04 08:37:51.854 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] schema data cleaned 
[TRACE] 2025-06-04 08:37:51.854 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] schema data cleaned 
[TRACE] 2025-06-04 08:37:51.854 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] monitor closed 
[TRACE] 2025-06-04 08:37:51.855 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] monitor closed 
[TRACE] 2025-06-04 08:37:51.855 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[3080ce1e-1a69-43e1-8283-6bfbdbad03d9] close complete, cost 48 ms 
[TRACE] 2025-06-04 08:37:51.855 - [TAP-6799(100)][字段改名] - Node 字段改名[7039d742-6c00-4bf3-a8ff-dfc29df80ac2] close complete, cost 44 ms 
[TRACE] 2025-06-04 08:37:52.174 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] running status set to false 
[INFO ] 2025-06-04 08:37:52.174 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-d411bd96-f4b9-4c66-a1a8-90924ee92b2b 
[INFO ] 2025-06-04 08:37:52.175 - [TAP-6799(100)][MDM_packages][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-d411bd96-f4b9-4c66-a1a8-90924ee92b2b 
[INFO ] 2025-06-04 08:37:52.175 - [TAP-6799(100)][MDM_packages][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.179 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.179 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] monitor closed 
[TRACE] 2025-06-04 08:37:52.179 - [TAP-6799(100)][MDM_packages] - Node MDM_packages[8ae13f48-9e50-4db1-bf1b-b375ea3a9b4d] close complete, cost 7 ms 
[TRACE] 2025-06-04 08:37:52.202 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] running status set to false 
[TRACE] 2025-06-04 08:37:52.202 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.202 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] monitor closed 
[TRACE] 2025-06-04 08:37:52.205 - [TAP-6799(100)][54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] - Node 54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f[54cbe0e7-8e86-4fe1-ac6d-ecf841702f8f] close complete, cost 1 ms 
[TRACE] 2025-06-04 08:37:52.206 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:37:52.206 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:37:52.207 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:37:52.207 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:37:52.326 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:52.326 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:37:52.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:52.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.327 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.327 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.327 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749026272369 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749026272369 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:37:52.483 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 5 ms 
[TRACE] 2025-06-04 08:37:52.684 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[TRACE] 2025-06-04 08:37:52.688 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] running status set to false 
[TRACE] 2025-06-04 08:37:52.688 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.688 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] monitor closed 
[TRACE] 2025-06-04 08:37:52.688 - [TAP-6799(100)][b7386088-5c19-4dc4-a0d7-d6311ad1571f] - Node b7386088-5c19-4dc4-a0d7-d6311ad1571f[b7386088-5c19-4dc4-a0d7-d6311ad1571f] close complete, cost 0 ms 
[INFO ] 2025-06-04 08:37:52.696 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-65ed9f8c-5885-49fb-bdf4-7137795c7ff8 
[INFO ] 2025-06-04 08:37:52.696 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-65ed9f8c-5885-49fb-bdf4-7137795c7ff8 
[INFO ] 2025-06-04 08:37:52.698 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.698 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.698 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:37:52.698 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 15 ms 
[TRACE] 2025-06-04 08:37:52.699 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:37:52.699 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:37:52.699 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:37:52.820 - [TAP-6799(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-04 08:37:52.820 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:52.820 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:52.820 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] start preload schema,table counts: 0 
[TRACE] 2025-06-04 08:37:52.821 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] start preload schema,table counts: 1 
[TRACE] 2025-06-04 08:37:52.821 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.821 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.821 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.821 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-04 08:37:52.822 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 1674c3ea-16fc-4674-9a27-0cd5951e0f48) enable batch process 
[TRACE] 2025-06-04 08:37:52.822 - [TAP-6799(100)][FDM_product] - Node js_processor(FDM_product: 73fce465-47ee-46f4-abb7-173648d43889) enable batch process 
[TRACE] 2025-06-04 08:37:52.977 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] running status set to false 
[TRACE] 2025-06-04 08:37:52.979 - [TAP-6799(100)][FDM_product] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749026272868 
[TRACE] 2025-06-04 08:37:52.979 - [TAP-6799(100)][FDM_product] - PDK connector node released: HazelcastSampleSourcePdkDataNode_0e0a3626-c9f8-4b06-995c-45c95039db3b_1749026272868 
[TRACE] 2025-06-04 08:37:52.979 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] schema data cleaned 
[TRACE] 2025-06-04 08:37:52.979 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] monitor closed 
[TRACE] 2025-06-04 08:37:53.167 - [TAP-6799(100)][FDM_product] - Node FDM_product[0e0a3626-c9f8-4b06-995c-45c95039db3b] close complete, cost 2 ms 
[TRACE] 2025-06-04 08:37:53.167 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] running status set to false 
[INFO ] 2025-06-04 08:37:53.168 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-3eb8cf85-6ed1-408d-955f-9c457dedd155 
[INFO ] 2025-06-04 08:37:53.169 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-3eb8cf85-6ed1-408d-955f-9c457dedd155 
[INFO ] 2025-06-04 08:37:53.169 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-73fce465-47ee-46f4-abb7-173648d43889-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:37:53.170 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] schema data cleaned 
[TRACE] 2025-06-04 08:37:53.170 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] monitor closed 
[TRACE] 2025-06-04 08:37:53.170 - [TAP-6799(100)][FDM_product] - Node FDM_product[73fce465-47ee-46f4-abb7-173648d43889] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:37:53.406 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] running status set to false 
[INFO ] 2025-06-04 08:37:53.407 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node stopped: ScriptExecutor-S Agent Local DaaS_1-01426059-74e1-4f88-84ea-0e9a51f9ddb0 
[INFO ] 2025-06-04 08:37:53.407 - [TAP-6799(100)][FDM_product][src=user_script]  - PDK connector node released: ScriptExecutor-S Agent Local DaaS_1-01426059-74e1-4f88-84ea-0e9a51f9ddb0 
[INFO ] 2025-06-04 08:37:53.407 - [TAP-6799(100)][FDM_product][src=user_script]  - [ScriptExecutorsManager-683ec91db023c8285961de53-1674c3ea-16fc-4674-9a27-0cd5951e0f48-63468098c87faf3ba64fece0] schema data cleaned 
[TRACE] 2025-06-04 08:37:53.408 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] schema data cleaned 
[TRACE] 2025-06-04 08:37:53.408 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] monitor closed 
[TRACE] 2025-06-04 08:37:53.410 - [TAP-6799(100)][FDM_product] - Node FDM_product[1674c3ea-16fc-4674-9a27-0cd5951e0f48] close complete, cost 3 ms 
[TRACE] 2025-06-04 08:37:53.410 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] running status set to false 
[TRACE] 2025-06-04 08:37:53.410 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] schema data cleaned 
[TRACE] 2025-06-04 08:37:53.410 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] monitor closed 
[TRACE] 2025-06-04 08:37:53.411 - [TAP-6799(100)][aa2e6d50-567f-40a8-bb0f-f885292b0d5b] - Node aa2e6d50-567f-40a8-bb0f-f885292b0d5b[aa2e6d50-567f-40a8-bb0f-f885292b0d5b] close complete, cost 0 ms 
[TRACE] 2025-06-04 08:37:53.411 - [TAP-6799(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-04 08:37:53.411 - [TAP-6799(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-04 08:37:53.412 - [TAP-6799(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-04 08:37:53.412 - [TAP-6799(100)] - Stopped task aspect(s) 
