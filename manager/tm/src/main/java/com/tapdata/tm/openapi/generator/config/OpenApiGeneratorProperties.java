package com.tapdata.tm.openapi.generator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * OpenAPI Generator configuration properties
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@ConfigurationProperties(prefix = "openapi.generator")
public class OpenApiGeneratorProperties {

    /**
     * JAR file configuration
     */
    private Jar jar = new Jar();

    /**
     * Template configuration
     */
    private Template template = new Template();

    /**
     * Temporary directory configuration
     */
    private Temp temp = new Temp();

    /**
     * Java configuration
     */
    private Java java = new Java();

    @Data
    public static class Jar {
        /**
         * Path to OpenAPI Generator CLI JAR file
         */
        private String path = "classpath:openapi-generator/openapi-generator-cli.jar";
    }

    @Data
    public static class Template {
        /**
         * Path to template directory
         */
        private String path = "classpath:openapi-generator/templates";
    }

    @Data
    public static class Temp {
        /**
         * Temporary directory for code generation
         */
        private String dir = System.getProperty("java.io.tmpdir");
    }

    @Data
    public static class Java {
        /**
         * Java version for code generation
         */
        private int version = 17;
    }
}
