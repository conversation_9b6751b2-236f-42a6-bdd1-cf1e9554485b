package com.tapdata.tm.openapi.generator.service;

import com.tapdata.tm.openapi.generator.config.OpenApiGeneratorProperties;
import com.tapdata.tm.openapi.generator.dto.CodeGenerationRequest;
import com.tapdata.tm.openapi.generator.exception.CodeGenerationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OpenAPI code generation service
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Service
@Slf4j
public class OpenApiGeneratorService {

	public static final String DEFAULT_JAVA_TEMPLATE_LIBRARY = "okhttp-gson";
	private final OpenApiGeneratorProperties properties;

	public OpenApiGeneratorService(OpenApiGeneratorProperties properties) {
		this.properties = properties;
	}

	@PostConstruct
	public void init() {
		log.info("OpenAPI Generator Service initialized with configuration:");
		log.info("  JAR path: {}", properties.getJar().getPath());
		log.info("  Template path: {}", properties.getTemplate().getPath());
		log.info("  Temp directory: {}", properties.getTemp().getDir());
		log.info("  Java version: {}", properties.getJava().getVersion());
	}

	/**
	 * Generate code and return JAR or ZIP file (only supports Java language)
	 */
	public ResponseEntity<InputStreamResource> generateCode(CodeGenerationRequest request) throws Exception {
		log.info("Starting code generation with request parameters: {}", request);

		// Validate language support - only Java is supported
		if (!"java".equalsIgnoreCase(request.getLan())) {
			throw new CodeGenerationException(
					String.format("Unsupported language: %s. Currently only 'java' language is supported.", request.getLan())
			);
		}

		// Validate Java runtime version - requires Java 17+
		validateJavaVersion();

		// Create temporary directory
		String sessionId = UUID.randomUUID().toString();
		Path outputDir = Paths.get(properties.getTemp().getDir(), "openapi-generator", sessionId);
		Files.createDirectories(outputDir);

		try {
			// Execute code generation
			log.info("Generator parameters: {}, output dir: {}", request, outputDir);
			executeGenerator(request, outputDir.toString());

			// Check Maven availability and generate appropriate response
			return generateResponse(request, outputDir);

		} finally {
			// Clean up temporary files - temporarily disabled for debugging
			log.info("Temporary files preserved for debugging at: {}", outputDir);
//			cleanupTempDirectory(outputDir);
		}
	}

	/**
	 * Generate response based on Maven availability - JAR if Maven available, ZIP otherwise
	 */
	private ResponseEntity<InputStreamResource> generateResponse(CodeGenerationRequest request, Path outputDir) throws Exception {
		// Check if Maven is available
		boolean mavenAvailable = isMavenAvailable();

		if (mavenAvailable) {
			log.info("Maven is available, generating JAR package");
			return generateJarResponse(request, outputDir);
		} else {
			log.info("Maven is not available, generating ZIP package");
			return generateZipResponse(request, outputDir);
		}
	}

	/**
	 * Check if Maven is available on the system
	 */
	private boolean isMavenAvailable() {
		try {
			// First try mvnw from resources
			log.debug("Checking if mvnw from resources is available");
			String mvnwResourcePath = resolveResourcePath("classpath:mvnw");
			Path mvnwPath = Paths.get(mvnwResourcePath);
			if (Files.exists(mvnwPath)) {
				log.debug("mvnw found in resources: {}", mvnwPath);
				return true;
			}
		} catch (Exception e) {
			log.debug("mvnw from resources not available: {}", e.getMessage());
		}

		// Then try system Maven
		try {
			log.debug("Checking if system Maven is available");
			ProcessBuilder processBuilder = new ProcessBuilder("mvn", "--version");
			processBuilder.redirectErrorStream(true);
			Process process = processBuilder.start();
			int exitCode = process.waitFor();
			boolean available = exitCode == 0;
			log.debug("System Maven available: {}", available);
			return available;
		} catch (Exception e) {
			log.debug("System Maven not available: {}", e.getMessage());
			return false;
		}
	}

	/**
	 * Generate JAR response for Java language
	 */
	private ResponseEntity<InputStreamResource> generateJarResponse(CodeGenerationRequest request, Path outputDir) throws Exception {
		// Compile the generated code to JAR
		compileToJar(outputDir.toString());

		// Find and return the generated JAR file
		Path jarFile = findGeneratedJarFile(request, outputDir);
		if (jarFile == null || !Files.exists(jarFile)) {
			throw new CodeGenerationException("Generated JAR file not found in output directory");
		}

		// Prepare response
		String fileName = String.format("%s.jar", request.getArtifactId());
		InputStreamResource resource = new InputStreamResource(Files.newInputStream(jarFile));

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
		headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
		headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(Files.size(jarFile)));

		log.info("Code generation completed, JAR file size: {} bytes", Files.size(jarFile));

		return ResponseEntity.ok()
				.headers(headers)
				.body(resource);
	}

	/**
	 * Generate ZIP response for Java language
	 */
	private ResponseEntity<InputStreamResource> generateZipResponse(CodeGenerationRequest request, Path outputDir) throws Exception {
		// Create ZIP file containing the generated source code
		Path zipFile = createZipFile(request, outputDir);

		// Prepare response
		String fileName = String.format("%s-%s.zip", request.getArtifactId(), request.getLan());
		InputStreamResource resource = new InputStreamResource(Files.newInputStream(zipFile));

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
		headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
		headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(Files.size(zipFile)));

		log.info("Code generation completed, ZIP file size: {} bytes", Files.size(zipFile));

		return ResponseEntity.ok()
				.headers(headers)
				.body(resource);
	}

	/**
	 * Create ZIP file containing the generated source code
	 */
	private Path createZipFile(CodeGenerationRequest request, Path outputDir) throws IOException {
		String fileName = String.format("%s-%s.zip", request.getArtifactId(), request.getLan());
		Path zipFilePath = outputDir.getParent().resolve(fileName);

		log.info("Creating ZIP file: {}", zipFilePath);

		try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFilePath.toFile()))) {
			// Add all files from the generated project directory
			addDirectoryToZip(outputDir, outputDir, zipOut);
		}

		log.info("ZIP file created successfully: {} (size: {} bytes)", zipFilePath, Files.size(zipFilePath));
		return zipFilePath;
	}

	/**
	 * Recursively add directory contents to ZIP file
	 */
	private void addDirectoryToZip(Path sourceDir, Path baseDir, ZipOutputStream zipOut) throws IOException {
		try (var pathStream = Files.walk(sourceDir)) {
			pathStream.forEach(sourcePath -> {
				try {
					if (Files.isDirectory(sourcePath)) {
						return; // Skip directories, only add files
					}

					// Calculate relative path from base directory
					Path relativePath = baseDir.relativize(sourcePath);
					String zipEntryName = relativePath.toString().replace('\\', '/');

					// Create ZIP entry
					ZipEntry zipEntry = new ZipEntry(zipEntryName);
					zipOut.putNextEntry(zipEntry);

					// Copy file content to ZIP
					try (FileInputStream fis = new FileInputStream(sourcePath.toFile())) {
						byte[] buffer = new byte[8192];
						int length;
						while ((length = fis.read(buffer)) > 0) {
							zipOut.write(buffer, 0, length);
						}
					}

					zipOut.closeEntry();
					log.debug("Added to ZIP: {}", zipEntryName);

				} catch (IOException e) {
					throw new RuntimeException("Failed to add file to ZIP: " + sourcePath, e);
				}
			});
		}
	}

	/**
	 * Resolve resource path, supports classpath and absolute paths
	 */
	private String resolveResourcePath(String path) throws IOException {
		if (StringUtils.hasText(path) && path.startsWith("classpath:")) {
			String resourcePath = path.substring("classpath:".length());
			Resource resource = new ClassPathResource(resourcePath);
			if (resource.exists()) {
				return resource.getFile().getAbsolutePath();
			} else {
				throw new IOException("Classpath resource not found: " + resourcePath);
			}
		}
		return path;
	}

	/**
	 * Auto-detect and resolve JAR path
	 */
	private String resolveJarPath() throws IOException {
		String configuredPath = properties.getJar().getPath();

		if (!"auto".equals(configuredPath)) {
			return resolveResourcePath(configuredPath);
		}

		// Auto-detection logic
		// 1. Try development path (source code)
		String devPath = "openapi-generator/openapi-generator-cli.jar";
		Path devJarPath = Paths.get(devPath);
		if (Files.exists(devJarPath)) {
			log.info("Using development JAR path: {}", devJarPath.toAbsolutePath());
			return devJarPath.toAbsolutePath().toString();
		}

		// 2. Try production path (etc directory)
		String prodPath = "etc/openapi-generator/openapi-generator-cli.jar";
		Path prodJarPath = Paths.get(prodPath);
		if (Files.exists(prodJarPath)) {
			log.info("Using production JAR path: {}", prodJarPath.toAbsolutePath());
			return prodJarPath.toAbsolutePath().toString();
		}

		// 3. Try classpath as fallback
		try {
			String classpathPath = resolveResourcePath("classpath:openapi-generator/openapi-generator-cli.jar");
			log.info("Using classpath JAR path: {}", classpathPath);
			return classpathPath;
		} catch (IOException e) {
			log.debug("Classpath JAR not found: {}", e.getMessage());
		}

		throw new IOException("OpenAPI Generator JAR not found in any of the expected locations: " +
				devPath + ", " + prodPath + ", classpath:openapi-generator/openapi-generator-cli.jar");
	}

	/**
	 * Auto-detect and resolve template path
	 */
	private String resolveTemplatePath() throws IOException {
		String configuredPath = properties.getTemplate().getPath();

		if (!"auto".equals(configuredPath)) {
			return resolveResourcePath(configuredPath);
		}

		// Auto-detection logic
		// 1. Try development path (source code)
		String devPath = "openapi-generator/templates";
		Path devTemplatePath = Paths.get(devPath);
		if (Files.exists(devTemplatePath) && Files.isDirectory(devTemplatePath)) {
			log.info("Using development template path: {}", devTemplatePath.toAbsolutePath());
			return devTemplatePath.toAbsolutePath().toString();
		}

		// 2. Try production path (etc directory)
		String prodPath = "etc/openapi-generator/templates";
		Path prodTemplatePath = Paths.get(prodPath);
		if (Files.exists(prodTemplatePath) && Files.isDirectory(prodTemplatePath)) {
			log.info("Using production template path: {}", prodTemplatePath.toAbsolutePath());
			return prodTemplatePath.toAbsolutePath().toString();
		}

		// 3. Try classpath as fallback
		try {
			String classpathPath = resolveResourcePath("classpath:openapi-generator/templates");
			log.info("Using classpath template path: {}", classpathPath);
			return classpathPath;
		} catch (IOException e) {
			log.debug("Classpath template not found: {}", e.getMessage());
		}

		throw new IOException("OpenAPI Generator templates not found in any of the expected locations: " +
				devPath + ", " + prodPath + ", classpath:openapi-generator/templates");
	}

	/**
	 * Execute OpenAPI Generator
	 */
	private void executeGenerator(CodeGenerationRequest request, String outputDir) throws Exception {
		// Resolve JAR path with auto-detection
		String resolvedJarPath = resolveJarPath();

		List<String> command = new ArrayList<>();
		command.add("java");
		command.add("-jar");
		command.add(resolvedJarPath);
		command.add("generate");
		command.add("-i");
		command.add(request.getOas());
		command.add("-g");
		command.add(request.getLan());
		command.add("-o");
		command.add(outputDir);
		command.add("--invoker-package");
		command.add(request.getPackageName());
		command.add("--api-package");
		command.add(request.getPackageName() + ".api");
		command.add("--model-package");
		command.add(request.getPackageName() + ".model");
		command.add("--artifact-id");
		command.add(request.getArtifactId());
		command.add("--group-id");
		command.add(request.getGroupId());
		command.add("--skip-validate-spec");

		// Add additional properties to ensure JAR generation with Java 17
		command.add("--additional-properties");
		var javaVersion = properties.getJava().getVersion();
		var additionalProps = String.format(
				"generatePom=true,generateApiTests=false,generateModelTests=false,java8=false,dateLibrary=java8,sourceFolder=src/main/java,javaVersion=%d",
				javaVersion
		);
		command.add(additionalProps);

		// Add template parameters if available
		try {
			String resolvedTemplatePath = resolveTemplatePath() + File.separator + request.getLan();
			log.info("Template path from config: {}", properties.getTemplate().getPath());
			log.info("Resolved template path: {}", resolvedTemplatePath);

			// Check if template directory exists
			Path templatePath = Paths.get(resolvedTemplatePath);
			if (Files.exists(templatePath)) {
				// List template files for debugging
				try {
					log.info("Template directory contents:");
					Files.walk(templatePath, 2)
						.filter(Files::isRegularFile)
						.forEach(file -> log.info("  - {}", file));
				} catch (IOException e) {
					log.warn("Failed to list template directory contents: {}", e.getMessage());
				}

				command.add("-t");
				command.add(resolvedTemplatePath);
				command.add("--library");
				command.add(DEFAULT_JAVA_TEMPLATE_LIBRARY);
				log.info("Using custom template path: {}, library: {}", resolvedTemplatePath, DEFAULT_JAVA_TEMPLATE_LIBRARY);
			} else {
				log.warn("Template path does not exist, using default templates: {}", resolvedTemplatePath);
			}
	} catch (IOException e) {
		log.warn("Failed to resolve template path: {}, using default OpenAPI Generator templates", e.getMessage());
	}

		log.info("Executing command: {}", String.join(" ", command));

		ProcessBuilder processBuilder = new ProcessBuilder(command);
		processBuilder.redirectErrorStream(true);

		Process process = processBuilder.start();

		// Read output
		StringBuilder output = new StringBuilder();
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
			String line;
			while ((line = reader.readLine()) != null) {
				output.append(line).append("\n");
				log.info("Generator output: {}", line);
			}
		}

		int exitCode = process.waitFor();
		if (exitCode != 0) {
			log.error("Code generation failed, exit code: {}, output: {}", exitCode, output);
			throw new CodeGenerationException("Code generation failed: " + output);
		}

		log.info("Code generation successful");

		// Verify that source files were generated
		verifyGeneratedSources(outputDir);

		// Verify that our custom template was used
		verifyCustomTemplate(outputDir);
	}

	/**
	 * Compile generated code to JAR file using Maven
	 */
	private void compileToJar(String outputDir) throws Exception {
		log.info("Starting Maven compilation in directory: {}", outputDir);

		// Try to use mvnw from resources first, fallback to system maven
		String mavenCommand;
		try {
			mavenCommand = getMavenCommand(outputDir);
		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new CodeGenerationException("Maven command selection was interrupted: " + e.getMessage());
		}
		log.info("Using Maven command: {}", mavenCommand);

		List<String> command = new ArrayList<>();
		command.add(mavenCommand);
		command.add("clean");
		command.add("package");
		command.add("-DskipTests");
		// Remove quiet mode for better debugging, but don't use -X as it's too verbose

		ProcessBuilder processBuilder = new ProcessBuilder(command);
		processBuilder.directory(new File(outputDir));
		processBuilder.redirectErrorStream(true);

		Process process = processBuilder.start();

		// Read output
		StringBuilder output = new StringBuilder();
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
			String line;
			while ((line = reader.readLine()) != null) {
				output.append(line).append("\n");
				log.info("Maven output: {}", line); // Changed to info level for better debugging
			}
		}

		int exitCode = process.waitFor();
		if (exitCode != 0) {
			log.error("Maven compilation failed, exit code: {}, output: {}", exitCode, output);
			throw new CodeGenerationException("Maven compilation failed: " + output);
		}

		log.info("Maven compilation successful");
	}

	/**
	 * Get Maven command to use - try mvnw from resources first, fallback to system maven
	 */
	private String getMavenCommand(String outputDir) throws IOException, InterruptedException {
		// First try to use mvnw from resources
		try {
			log.info("Attempting to use mvnw from resources directory");

			// Copy Maven Wrapper files to output directory
			copyMavenWrapperFiles(outputDir);

			// Use local mvnw in the output directory
			Path localMvnwPath = Paths.get(outputDir, "mvnw");

			// Ensure mvnw has execute permissions
			File mvnwFile = localMvnwPath.toFile();
			if (!mvnwFile.canExecute()) {
				boolean success = mvnwFile.setExecutable(true);
				if (!success) {
					log.warn("Failed to set execute permission on mvnw file: {}", localMvnwPath);
				} else {
					log.info("Set execute permission on mvnw file: {}", localMvnwPath);
				}
			}

			// Test if mvnw works
			ProcessBuilder testBuilder = new ProcessBuilder(localMvnwPath.toString(), "--version");
			testBuilder.directory(new File(outputDir));
			testBuilder.redirectErrorStream(true);

			Process testProcess = testBuilder.start();
			int testExitCode = testProcess.waitFor();

			if (testExitCode == 0) {
				log.info("Successfully using mvnw from resources: {}", localMvnwPath);
				return localMvnwPath.toString();
			} else {
				log.warn("mvnw from resources failed with exit code: {}, falling back to system maven", testExitCode);
			}

		} catch (Exception e) {
			log.warn("Failed to use mvnw from resources: {}, falling back to system maven", e.getMessage());
		}

		// Fallback to system maven
		log.info("Using system maven command: mvn");
		return "mvn";
	}

	/**
	 * Copy Maven Wrapper files from resources to output directory
	 */
	private void copyMavenWrapperFiles(String outputDir) throws IOException {
		log.debug("Copying Maven Wrapper files to output directory: {}", outputDir);

		Path outputPath = Paths.get(outputDir);

		// Copy mvnw script
		String mvnwResourcePath = resolveResourcePath("classpath:mvnw");
		Path mvnwSource = Paths.get(mvnwResourcePath);
		Path mvnwTarget = outputPath.resolve("mvnw");
		Files.copy(mvnwSource, mvnwTarget);
		log.debug("Copied mvnw to: {}", mvnwTarget);

		// Copy mvnw.cmd script (for Windows compatibility)
		try {
			String mvnwCmdResourcePath = resolveResourcePath("classpath:mvnw.cmd");
			Path mvnwCmdSource = Paths.get(mvnwCmdResourcePath);
			Path mvnwCmdTarget = outputPath.resolve("mvnw.cmd");
			Files.copy(mvnwCmdSource, mvnwCmdTarget);
			log.debug("Copied mvnw.cmd to: {}", mvnwCmdTarget);
		} catch (IOException e) {
			log.debug("Failed to copy mvnw.cmd (this is optional): {}", e.getMessage());
		}

		// Copy .mvn directory and its contents
		String mvnDirResourcePath = resolveResourcePath("classpath:.mvn");
		Path mvnDirSource = Paths.get(mvnDirResourcePath);
		Path mvnDirTarget = outputPath.resolve(".mvn");

		copyDirectory(mvnDirSource, mvnDirTarget);
		log.debug("Copied .mvn directory to: {}", mvnDirTarget);

		log.debug("Maven Wrapper files copied successfully");
	}

	/**
	 * Recursively copy directory contents
	 */
	private void copyDirectory(Path source, Path target) throws IOException {
		if (!Files.exists(source)) {
			throw new IOException("Source directory does not exist: " + source);
		}

		Files.createDirectories(target);

		try (var pathStream = Files.walk(source)) {
			pathStream.forEach(sourcePath -> {
				try {
					Path targetPath = target.resolve(source.relativize(sourcePath));
					if (Files.isDirectory(sourcePath)) {
						Files.createDirectories(targetPath);
					} else {
						Files.copy(sourcePath, targetPath);
					}
				} catch (IOException e) {
					throw new RuntimeException("Failed to copy: " + sourcePath, e);
				}
			});
		}
	}

	/**
	 * Find the generated JAR file in the target directory
	 */
	private Path findGeneratedJarFile(CodeGenerationRequest request, Path outputDir) throws IOException {
		Path targetDir = outputDir.resolve("target");
		if (!Files.exists(targetDir)) {
			log.warn("Target directory not found: {}", targetDir);
			return null;
		}

		log.info("Searching for JAR files in: {}", targetDir);

		try (var pathStream = Files.walk(targetDir)) {
			List<Path> jarFiles = pathStream
					.filter(path -> path.toString().contains(request.getArtifactId() + ".jar"))
					.collect(Collectors.toList());

			log.info("Found JAR files: {}", jarFiles);

			// Prefer the fat JAR created by shade plugin (artifactId.jar without version)
			for (Path jarFile : jarFiles) {
				if (Files.size(jarFile) > 0) {
					String fileName = jarFile.getFileName().toString();
					// Look for the exact match: artifactId.jar (fat JAR from shade plugin)
					if (fileName.equals(request.getArtifactId() + ".jar")) {
						log.info("Selected fat JAR file: {} (size: {} bytes)", jarFile, Files.size(jarFile));
						return jarFile;
					}
				}
			}

			// Fallback to any non-empty JAR file
			for (Path jarFile : jarFiles) {
				if (Files.size(jarFile) > 0) {
					log.info("Selected JAR file: {} (size: {} bytes)", jarFile, Files.size(jarFile));
					return jarFile;
				}
			}

			log.warn("No valid JAR files found in target directory");
			return null;
		}
	}

	/**
	 * Verify that source files were generated by OpenAPI Generator
	 */
	private void verifyGeneratedSources(String outputDir) throws IOException {
		Path outputPath = Paths.get(outputDir);
		Path srcMainJava = outputPath.resolve("src/main/java");

		log.info("Verifying generated sources in: {}", srcMainJava);

		if (!Files.exists(srcMainJava)) {
			throw new CodeGenerationException("Source directory not found: " + srcMainJava);
		}

		// Count Java files
		try (var pathStream = Files.walk(srcMainJava)) {
			long javaFileCount = pathStream
					.filter(path -> path.toString().endsWith(".java"))
					.count();

			log.info("Found {} Java source files", javaFileCount);

			if (javaFileCount == 0) {
				throw new CodeGenerationException("No Java source files were generated");
			}
		}

		// Check for pom.xml
		Path pomFile = outputPath.resolve("pom.xml");
		if (!Files.exists(pomFile)) {
			throw new CodeGenerationException("pom.xml not found in generated project");
		}

		log.info("Source verification completed successfully");
	}

	/**
	 * Verify that our custom template was used by checking pom.xml content
	 */
	private void verifyCustomTemplate(String outputDir) throws IOException {
		Path pomFile = Paths.get(outputDir, "pom.xml");
		if (!Files.exists(pomFile)) {
			log.warn("pom.xml not found, cannot verify custom template usage");
			return;
		}

		String pomContent = Files.readString(pomFile);
		log.info("Checking pom.xml for custom template indicators...");

		// Check for Maven Shade Plugin (our custom template indicator)
		if (pomContent.contains("maven-shade-plugin")) {
			log.info("✓ Custom template detected: maven-shade-plugin found in pom.xml");
		} else {
			log.warn("✗ Custom template NOT detected: maven-shade-plugin not found in pom.xml");
		}

		// Check for finalName configuration
		if (pomContent.contains("<finalName>{{artifactId}}</finalName>") ||
			pomContent.contains("<finalName>" + pomContent.substring(pomContent.indexOf("<artifactId>") + 12, pomContent.indexOf("</artifactId>")) + "</finalName>")) {
			log.info("✓ Custom template detected: finalName configuration found");
		} else {
			log.warn("✗ Custom template NOT detected: finalName configuration not found");
		}

		// Check Java version
		if (pomContent.contains("<source>11</source>") || pomContent.contains("<target>11</target>")) {
			log.info("✓ Custom template detected: Java 11 configuration found");
		} else {
			log.warn("✗ Custom template NOT detected: expected Java version configuration not found");
		}

		// Log first few lines of pom.xml for debugging
		String[] lines = pomContent.split("\n");
		log.info("pom.xml content preview (first 15 lines):");
		for (int i = 0; i < Math.min(15, lines.length); i++) {
			log.info("  {}: {}", i + 1, lines[i]);
		}
	}

	/**
	 * Validate Java runtime version - requires Java 11+
	 * Checks both runtime version and command line java version
	 */
	private void validateJavaVersion() {
		// Check runtime Java version
		String runtimeJavaVersion = System.getProperty("java.version");
		log.debug("Runtime Java version: {}", runtimeJavaVersion);

		try {
			// Parse runtime major version
			int runtimeMajorVersion = parseJavaMajorVersion(runtimeJavaVersion);

			if (runtimeMajorVersion < 11) {
				String errorMessage = String.format(
						"Runtime Java version %s is not supported. Minimum required version is Java 11. Current runtime version: %s",
						runtimeMajorVersion, runtimeJavaVersion
				);
				log.error(errorMessage);
				throw new CodeGenerationException(errorMessage);
			}

			log.info("Runtime Java version validation passed. Current version: {} (major: {})", runtimeJavaVersion, runtimeMajorVersion);

		} catch (NumberFormatException e) {
			String errorMessage = String.format(
					"Unable to parse runtime Java version: %s. Please ensure you are running on Java 11 or higher.",
					runtimeJavaVersion
			);
			log.error(errorMessage, e);
			throw new CodeGenerationException(errorMessage);
		}

		// Check command line Java version
		validateCommandLineJavaVersion();
	}

	/**
	 * Validate command line Java version by executing 'java -version'
	 */
	private void validateCommandLineJavaVersion() {
		try {
			log.debug("Checking command line Java version using 'java -version'");

			ProcessBuilder processBuilder = new ProcessBuilder("java", "-version");
			processBuilder.redirectErrorStream(true);

			Process process = processBuilder.start();

			StringBuilder output = new StringBuilder();
			try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
				String line;
				while ((line = reader.readLine()) != null) {
					output.append(line).append("\n");
				}
			}

			int exitCode = process.waitFor();
			if (exitCode != 0) {
				String errorMessage = "Failed to execute 'java -version' command. Please ensure Java is properly installed and available in PATH.";
				log.error(errorMessage);
				throw new CodeGenerationException(errorMessage);
			}

			String versionOutput = output.toString();
			log.debug("Command line java -version output: {}", versionOutput);

			// Parse version from output
			String commandLineVersion = extractVersionFromJavaVersionOutput(versionOutput);
			int commandLineMajorVersion = parseJavaMajorVersion(commandLineVersion);

			if (commandLineMajorVersion < 11) {
				String errorMessage = String.format(
						"Command line Java version %s is not supported. Minimum required version is Java 11. Current command line version: %s",
						commandLineMajorVersion, commandLineVersion
				);
				log.error(errorMessage);
				throw new CodeGenerationException(errorMessage);
			}

			log.info("Command line Java version validation passed. Version: {} (major: {})", commandLineVersion, commandLineMajorVersion);

		} catch (IOException | InterruptedException e) {
			String errorMessage = "Failed to check command line Java version: " + e.getMessage();
			log.error(errorMessage, e);
			throw new CodeGenerationException(errorMessage);
		}
	}

	/**
	 * Extract version string from 'java -version' command output
	 */
	private String extractVersionFromJavaVersionOutput(String output) {
		// java -version output format examples:
		// Java 8: java version "1.8.0_xxx"
		// Java 11+: java version "11.0.x" or openjdk version "11.0.x"

		String[] lines = output.split("\n");
		for (String line : lines) {
			line = line.trim();
			if (line.contains("version")) {
				// Find version string in quotes
				int startQuote = line.indexOf('"');
				int endQuote = line.lastIndexOf('"');
				if (startQuote != -1 && endQuote != -1 && startQuote < endQuote) {
					return line.substring(startQuote + 1, endQuote);
				}
			}
		}

		throw new NumberFormatException("Unable to extract version from java -version output: " + output);
	}

	/**
	 * Parse major version number from Java version string
	 * Handles both old format (1.8.0_xxx) and new format (11.0.x, 17.0.x)
	 */
	private int parseJavaMajorVersion(String javaVersion) {
		if (javaVersion == null || javaVersion.trim().isEmpty()) {
			throw new NumberFormatException("Java version is null or empty");
		}

		// Remove any leading/trailing whitespace
		javaVersion = javaVersion.trim();

		// Handle old format like "1.8.0_xxx" -> major version is 8
		if (javaVersion.startsWith("1.")) {
			String[] parts = javaVersion.split("\\.");
			if (parts.length >= 2) {
				return Integer.parseInt(parts[1]);
			}
		}

		// Handle new format like "11.0.x", "17.0.x" -> major version is the first number
		String[] parts = javaVersion.split("\\.");
		if (parts.length > 0) {
			// Extract only the numeric part (remove any non-numeric suffixes)
			String majorVersionStr = parts[0].replaceAll("[^0-9]", "");
			if (!majorVersionStr.isEmpty()) {
				return Integer.parseInt(majorVersionStr);
			}
		}

		throw new NumberFormatException("Unable to parse major version from: " + javaVersion);
	}

	/**
	 * Clean up temporary directory
	 */
	private void cleanupTempDirectory(Path tempDir) {
		try {
			if (Files.exists(tempDir)) {
				try (var pathStream = Files.walk(tempDir)) {
					pathStream
							.sorted((a, b) -> b.compareTo(a)) // Delete files first, then directories
							.forEach(path -> {
								try {
									Files.deleteIfExists(path);
								} catch (IOException e) {
									log.warn("Failed to delete temporary file: {}", path, e);
								}
							});
				}
			}
		} catch (IOException e) {
			log.warn("Failed to clean up temporary directory: {}", tempDir, e);
		}
	}
}
