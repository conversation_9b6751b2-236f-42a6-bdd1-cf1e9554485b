<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2021 Hazelcast Inc.
  ~
  ~ Licensed under the Hazelcast Community License (the "License"); you may not use
  ~ this file except in compliance with the License. You may obtain a copy of the
  ~ License at
  ~
  ~ http://hazelcast.com/hazelcast-community-license
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OF ANY KIND, either express or implied. See the License for the
  ~ specific language governing permissions and limitations under the License.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://www.hazelcast.com/schema/spring"
           xmlns:tool="http://www.springframework.org/schema/tool"
           targetNamespace="http://www.hazelcast.com/schema/spring"
           elementFormDefault="qualified">

    <xs:import namespace="http://www.springframework.org/schema/tool"
               schemaLocation="http://www.springframework.org/schema/tool/spring-tool.xsd"/>
    <xs:element name="config">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="hazelcast-bean">
                    <xs:choice minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="spring-aware" type="xs:string" minOccurs="0"/>
                        <xs:element name="instance-name" type="xs:string" minOccurs="0"/>
                        <xs:element name="cluster-name" type="xs:string" minOccurs="0"/>
                        <xs:element name="license-key" type="xs:string" minOccurs="0"/>
                        <xs:element name="management-center" type="management-center" minOccurs="0"/>
                        <xs:element name="properties" type="properties" minOccurs="0"/>
                        <xs:element name="wan-replication" type="wan-replication" minOccurs="0" maxOccurs="unbounded"/>
                        <xs:element name="network" type="network" minOccurs="0"/>
                        <xs:element name="advanced-network" type="advanced-network" minOccurs="0"/>
                        <xs:element name="partition-group" type="partition-group" minOccurs="0"/>
                        <xs:element name="executor-service" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="pool-size" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The number of executor threads per Member for the Executor.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="queue-capacity" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Executor's task queue capacity. 0 means Integer.MAX_VALUE.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="durable-executor-service" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="pool-size" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The number of executor threads per Member for the Executor.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="durability" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The durability of the executor
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="capacity" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Executor's task capacity (per partition)
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="scheduled-executor-service" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:all>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:all>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="pool-size" type="xs:string" default="16">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The number of executor threads per member for the executor.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="durability" type="xs:string" default="1">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The durability of the scheduled executor.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="capacity" type="xs:string" default="100">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The maximum number of tasks that a scheduler can have at any given point
                                            in time as per capacity-policy.
                                            Once the capacity is reached, new tasks will be rejected.
                                            Capacity is ignored upon migrations to prevent any undesirable data-loss.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="capacity-policy" type="scheduled-executor-capacity-policy"
                                              default="PER_NODE">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The active policy for the capacity setting
                                            capacity-policy has these valid values:
                                            PER_NODE: Maximum number of tasks in each Hazelcast instance.
                                            This is the default policy.
                                            PER_PARTITION: Maximum number of tasks within each partition. Storage size
                                            depends on the partition count in a Hazelcast instance.
                                            This attribute should not be used often.
                                            Avoid using this attribute with a small cluster: if the cluster is small it will
                                            be hosting more partitions, and therefore tasks, than that of a larger
                                            cluster.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="cardinality-estimator" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:all>
                                    <xs:element name="backup-count" type="backup-count" minOccurs="0" default="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Number of synchronous backups. For example, if 1 is set as the backup-count,
                                                then the cardinality estimation will be copied to one other JVM for
                                                fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="async-backup-count" type="backup-count" minOccurs="0" default="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Number of asynchronous backups. For example, if 1 is set as the
                                                async-backup-count,
                                                then cardinality estimation will be copied to one other JVM (asynchronously) for
                                                fail-safety. Valid numbers are 0 (no backup), 1, 2 ... 6.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>

                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:all>
                                <xs:attribute name="name" type="xs:string" default="default">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Name of the cardinality estimator.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="queue" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="item-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="item-listener" type="item-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="queue-store" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="properties" type="properties" minOccurs="0"/>
                                            </xs:sequence>
                                            <xs:attribute name="enabled" use="required" type="xs:string"/>
                                            <xs:attribute name="class-name" type="xs:string"/>
                                            <xs:attribute name="factory-class-name" type="xs:string"/>
                                            <xs:attribute name="store-implementation" type="xs:string"/>
                                            <xs:attribute name="factory-implementation" type="xs:string"/>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="priority-comparator-class-name" type="xs:string">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Fully-qualified comparator's class name to be used for the priority queue.
                                                If nothing is provided, then queue behaves as a FIFO queue.

                                                If this value is non-empty, then Hazelcast will ignore the queue store
                                                "memory-limit" configuration value.
                                            </xs:documentation>
                                        </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="max-size" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Value of maximum size of items in the Queue.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of synchronous backups. Remember that, Queue is a non-partitioned
                                            data structure, i.e. all entries of a Set resides in one partition. When
                                            this parameter is '1', it means there will be a backup of that Set in
                                            another node in the cluster. When it is '2', 2 nodes will have the backup.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of asynchronous backups.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="empty-queue-ttl" default="-1">
                                    <xs:simpleType>
                                        <xs:restriction base="xs:int">
                                            <xs:minInclusive value="-1"/>
                                        </xs:restriction>
                                    </xs:simpleType>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="ringbuffer" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="ringbuffer-store" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Includes the ring buffer store factory class name. The store format is the same as
                                                the in-memory-format for the ringbuffer.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="properties" type="properties" minOccurs="0"/>
                                            </xs:sequence>
                                            <xs:attribute name="enabled" use="required" type="parameterized-boolean"/>
                                            <xs:attributeGroup ref="class-or-bean-name">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        Name of the class or bean implementing MapLoader and/or MapStore.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:attributeGroup>
                                            <xs:attribute name="factory-class-name" type="xs:string"/>
                                            <xs:attribute name="factory-implementation" type="xs:string"/>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="capacity" type="parameterized-unsigned-int">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of items in the ringbuffer. If no time-to-live-seconds is set, the size will
                                            always
                                            be equal to capacity after the head completed the first loop around the ring. This is
                                            because no items are getting expired. The default value is 10000.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count" default="1">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of synchronous backups. For example, if 1 is set as the backup-count,
                                            then the items in the ringbuffer are copied to one other JVM for fail-safety.
                                            `backup-count` + `async-backup-count` cannot be bigger than maximum
                                            backup count which is `6`. Valid numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count"
                                              default="0">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of asynchronous backups. For example, if 1 is set as the backup-count,
                                            then the items in the ringbuffer are copied to one other JVM for fail-safety.
                                            `backup-count` + `async-backup-count` cannot be bigger than maximum
                                            backup count which is `6`. Valid numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="time-to-live-seconds" type="parameterized-unsigned-int">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Sets the time to live in seconds which is the maximum number of seconds
                                            for each item to stay in the ringbuffer before being removed.
                                            Entries that are older than time-to-liveSeconds are removed from the
                                            ringbuffer on the next ringbuffer operation (read or write).
                                            Time to live can be disabled by setting time-to-liveSeconds to 0.
                                            It means that items won't get removed because they expire. They may only
                                            be overwritten.
                                            When time-to-liveSeconds is disabled and after the tail does a full
                                            loop in the ring, the ringbuffer size will always be equal to the capacity.
                                            The time-to-liveSeconds can be any integer between 0 and Integer#MAX_VALUE.
                                            0 means infinite. The default is 0.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="in-memory-format" type="in-memory-format" default="BINARY">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Sets the in-memory format.
                                            Setting the in-memory format controls the format of the stored item in the
                                            ringbuffer:
                                            - OBJECT: the item is stored in deserialized format (a regular object)
                                            - BINARY (default): the item is stored in serialized format (a binary blob)
                                            The object in-memory format is useful when:
                                            - the object stored in object format has a smaller footprint than in
                                            binary format
                                            - if there are readers using a filter. Since for every filter
                                            invocation, the object needs to be available in object format.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="reliable-topic" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="message-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="message-listener" type="listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="read-batch-size" type="xs:int" default="10">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The maximum number of items to read in a batch.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="topic-overload-policy" type="topic-overload-policy">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Policy to handle an overloaded topic. Available values are `DISCARD_OLDEST`,
                                            `DISCARD_NEWEST`,
                                            `BLOCK` and `ERROR`. The default value is `BLOCK.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="map" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="eviction" type="eviction-map" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                When maximum size is reached, map is evicted based on the eviction policy.
                                                IMap has no eviction by default.

                                                size:
                                                maximum size can be any integer between 0 and Integer.MAX_VALUE.
                                                For max-size to work, set the eviction-policy property to a value other than NONE.

                                                Default value is 0.

                                                max-size-policy:
                                                max-size-policy has these valid values:
                                                PER_NODE: Maximum number of map entries in each Hazelcast instance.
                                                This is the default policy.
                                                PER_PARTITION: Maximum number of map entries within each partition. Storage size
                                                depends on the partition count in a Hazelcast instance.
                                                This attribute should not be used often.
                                                Avoid using this attribute with a small cluster: if the cluster is small it will
                                                be hosting more partitions, and therefore map entries, than that of a larger
                                                cluster. Thus, for a small cluster, eviction of the entries will decrease
                                                performance (the number of entries is large).
                                                USED_HEAP_SIZE: Maximum used heap size in megabytes per map for each Hazelcast
                                                instance.
                                                USED_HEAP_PERCENTAGE: Maximum used heap size percentage per map for each Hazelcast
                                                instance.
                                                If, for example, JVM is configured to have 1000 MB and this value is 10, then the
                                                map
                                                entries will be evicted when used heap size exceeds 100 MB.
                                                FREE_HEAP_SIZE: Minimum free heap size in megabytes for each JVM.
                                                FREE_HEAP_PERCENTAGE: Minimum free heap size percentage for each JVM.
                                                For example, if JVM is configured to have 1000 MB and this value is 10,
                                                then the map entries will be evicted when free heap size is below 100 MB.
                                                USED_NATIVE_MEMORY_SIZE: Maximum used native memory size in megabytes per map
                                                for each Hazelcast instance.
                                                USED_NATIVE_MEMORY_PERCENTAGE: Maximum used native memory size percentage per map
                                                for each Hazelcast instance.
                                                FREE_NATIVE_MEMORY_SIZE: Minimum free native memory size in megabytes
                                                for each Hazelcast instance.
                                                FREE_NATIVE_MEMORY_PERCENTAGE: Minimum free native memory size percentage
                                                for each Hazelcast instance.

                                                eviction-policy:
                                                Eviction policy has these valid values:
                                                LRU (Least Recently Used),
                                                LFU (Least Frequently Used),
                                                RANDOM,
                                                NONE.

                                                Default value is "NONE".
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="map-store" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="properties" type="properties" minOccurs="0"/>
                                            </xs:sequence>
                                            <xs:attribute name="enabled" use="required" type="xs:string"/>
                                            <xs:attributeGroup ref="class-or-bean-name">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        Name of the class or bean implementing MapLoader and/or MapStore.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:attributeGroup>
                                            <xs:attribute name="factory-class-name" type="xs:string"/>
                                            <xs:attribute name="factory-implementation" type="xs:string"/>
                                            <xs:attribute name="write-delay-seconds" use="required" type="xs:string">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        Number of seconds to delay to call the MapStore.store(key,
                                                        value).
                                                        If the value is zero then it is write-through so
                                                        MapStore.store(key, value) will be called as soon as the
                                                        entry is updated. Otherwise it is write-behind so updates will
                                                        be stored after write-delay-seconds value by calling
                                                        Hazelcast.storeAll(map). Default value is 0.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:attribute>
                                            <xs:attribute name="write-batch-size" type="xs:string">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        Used to create batch chunks when writing map store. In default
                                                        mode all entries will be tried to persist in one go. To create
                                                        batch chunks, minimum meaningful value for write-batch-size is
                                                        2.
                                                        For values smaller than 2, it works as in default mode.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:attribute>
                                            <xs:attribute name="write-coalescing" type="parameterized-boolean"
                                                          default="true">
                                                <xs:annotation>
                                                    <xs:documentation>
                                                        Setting write-coalescing is meaningful if you are using
                                                        write-behind map-store. Otherwise it has no effect.
                                                        When write-coalescing is true, only the latest
                                                        store operation on a key in the write-delay-seconds
                                                        time-window will be reflected to the map-store.
                                                    </xs:documentation>
                                                </xs:annotation>
                                            </xs:attribute>
                                            <xs:attribute name="initial-mode">
                                                <xs:simpleType>
                                                    <xs:restriction base="non-space-string">
                                                        <xs:enumeration value="LAZY"/>
                                                        <xs:enumeration value="EAGER"/>
                                                    </xs:restriction>
                                                </xs:simpleType>
                                            </xs:attribute>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="near-cache" type="near-cache" minOccurs="0"/>
                                    <xs:element name="query-caches" type="query-caches" minOccurs="0"/>
                                    <xs:element name="wan-replication-ref" type="wan-replication-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Hazelcast can replicate some or all of the cluster data. For example,
                                                you can have 5 different maps but you want only one of these maps
                                                replicating across clusters. To achieve this you mark the maps
                                                to be replicated by adding this element.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="indexes" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                This configuration lets you define map indexes.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="index" type="index" minOccurs="0" maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="attributes" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                This configuration lets you define extractors for custom attributes.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="attribute" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:complexType>
                                                        <xs:attribute name="name" type="xs:string" use="required"/>
                                                        <xs:attribute name="extractor-class-name" type="xs:string"
                                                                      use="required"/>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="entry-listeners" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                This configuration lets you add listeners (listener classes) for the
                                                map entries.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="entry-listener" type="entry-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                    <xs:element name="partition-lost-listeners" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>List of partition lost listeners</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="partition-lost-listener" type="listener"
                                                            minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="merkle-tree" type="merkle-tree" minOccurs="0"/>
                                    <xs:element name="hot-restart" type="hot-restart" minOccurs="0"/>
                                    <xs:element name="event-journal" type="event-journal" minOccurs="0"/>
                                    <xs:element name="partition-strategy" type="xs:string" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="in-memory-format" type="xs:string" default="BINARY">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Data type used to store entries.
                                            Possible values:
                                            BINARY (default): keys and values are stored as binary data.
                                            OBJECT: values are stored in their object forms.
                                            NATIVE: keys and values are stored in native memory. Only available on Hazelcast
                                            Enterprise.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="metadata-policy" type="metadata-policy" default="CREATE_ON_UPDATE">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Metadata policy for this map. Hazelcast may process objects of supported types ahead
                                            of time to
                                            create additional metadata about them. This metadata then is used to make querying and
                                            indexing faster.
                                            Turning on preprocessing may decrease put throughput.
                                            Valid values are:
                                            CREATE_ON_UPDATE (default): Objects of supported types are pre-processed when they are
                                            created and updated.
                                            OFF: No metadata is created.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            You can retrieve some statistics like owned entry count, backup entry count,
                                            last update time, locked entry count by setting this parameter's value
                                            as "true". The method for retrieving the statistics is `getLocalMapStats()`.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="per-entry-stats-enabled" type="parameterized-boolean"
                                              default="false">
                                    <xs:annotation>
                                        <xs:documentation>
                                             Whether statistical information (hits, creation
                                             time, last access time etc.) should be gathered
                                             and stored. You have to enable this if you plan to
                                             implement a custom eviction policy, out-of-the-box
                                             eviction policies work regardless of this setting.
                                             The method for retrieving the statistics is `getLocalMapStats()`.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="cache-deserialized-values" type="parameterized-cache-deserialized">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Control caching of de-serialized values. Caching makes query evaluation faster, but it
                                            cost memory.
                                            Possible Values:
                                            NEVER: Never cache de-serialized object
                                            INDEX-ONLY: Cache values only when they are inserted into an index.
                                            ALWAYS: Always cache de-serialized values.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of sync backups. If 1 is set as the backup-count for example, then
                                            all
                                            entries of the map will be copied to another JVM for fail-safety. Valid
                                            numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of async backups. If 1 is set as the backup-count for example, then
                                            all
                                            entries of the map will be copied to another JVM for fail-safety. Valid
                                            numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="time-to-live-seconds" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Maximum number of seconds for each entry to stay in the map. Entries that
                                            are older than `time-to-live-seconds` and not updated for
                                            `time-to-live-seconds` will get automatically evicted from the map. Any
                                            integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="max-idle-seconds" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Maximum number of seconds for each entry to stay idle in the map. Entries
                                            that are idle(not touched) for more than max-idle-seconds will get
                                            automatically evicted from the map. Entry is touched if get, put or
                                            containsKey is called. Any integer between 0 and Integer.MAX_VALUE. 0 means
                                            infinite. Default is 0.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="read-backup-data" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This boolean parameter enables reading local backup entries when set as
                                            `true`.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="cache" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="eviction" type="eviction" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                When maximum size is reached, cache is evicted based on the eviction policy.

                                                size:
                                                maximum size can be any integer between 0 and Integer.MAX_VALUE.

                                                Default value is 0.

                                                max-size-policy:
                                                max-size-policy has these valid values:
                                                ENTRY_COUNT (Maximum number of cache entries in the cache),
                                                USED_NATIVE_MEMORY_SIZE (Maximum used native memory size in megabytes per cache
                                                for each Hazelcast instance),
                                                USED_NATIVE_MEMORY_PERCENTAGE (Maximum used native memory size percentage per
                                                cache
                                                for each Hazelcast instance),
                                                FREE_NATIVE_MEMORY_SIZE (Minimum free native memory size in megabytes for each
                                                Hazelcast instance),
                                                FREE_NATIVE_MEMORY_PERCENTAGE (Minimum free native memory size percentage for each
                                                Hazelcast instance).

                                                Default value is "ENTRY_COUNT".

                                                eviction-policy:
                                                Eviction policy has these valid values:
                                                LRU (Least Recently Used),
                                                LFU (Least Frequently Used).

                                                Default value is "LRU".
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="cache-entry-listeners" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>List of cache entry listeners</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="cache-entry-listener" type="cache-entry-listener"
                                                            minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="partition-lost-listeners" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>List of partition lost listeners</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="partition-lost-listener" type="listener"
                                                            minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="expiry-policy-factory" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Defines the expiry policy factory class name or
                                                defines the expiry policy factory from predefined ones with duration
                                                configuration.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="timed-expiry-policy-factory"
                                                            type="timed-expiry-policy-factory"
                                                            minOccurs="0"/>
                                            </xs:sequence>
                                            <xs:attribute name="class-name"/>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="wan-replication-ref" type="wan-replication-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Hazelcast can replicate some or all of the cluster data. For example,
                                                you can have 5 different caches but you want only one of these caches
                                                replicating across clusters. To achieve this you mark the caches
                                                to be replicated by adding this element.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="hot-restart" type="hot-restart" minOccurs="0"/>
                                    <xs:element name="event-journal" type="event-journal" minOccurs="0"/>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" type="xs:string" use="required">
                                    <xs:annotation>
                                        <xs:documentation>Name of the cache.</xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="key-type" type="non-space-string">
                                    <xs:annotation>
                                        <xs:documentation>the type of keys provided as full class name
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="value-type" type="non-space-string">
                                    <xs:annotation>
                                        <xs:documentation>the type of values provided as full class name
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines whether statistics gathering is enabled on a cache.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="management-enabled" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines whether management is enabled on a cache.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="read-through" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Set if read-through caching should be used.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="disable-per-entry-invalidation-events" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Disables invalidation events for per entry but full-flush invalidation events are
                                            still enabled.
                                            Full-flush invalidation event means that invalidation events for all entries on clear.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="write-through" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Set if write-through caching should be used.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="in-memory-format" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Data type that will be used for storing records. Possible values:
                                            BINARY (default): keys and values will be stored as binary data
                                            OBJECT : values will be stored in their object forms
                                            NATIVE : keys and values will be stored in native memory.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="cache-loader-factory" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines the cache loader factory class name.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="cache-loader" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines the cache loader class name.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="cache-writer-factory" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines the cache writer factory class name.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="cache-writer" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines the cache writer class name.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="expiry-policy-factory" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Defines the expiry policy factory class name.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of synchronous backups. For example, if `1` is set as the `backup-count`,
                                            then all entries of the cache are copied to one other instance as synchronous for
                                            fail-safety.
                                            `backup-count` + `async-backup-count` cannot be bigger than maximum backup count which
                                            is `6`.
                                            Valid numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of asynchronous backups. For example, if `1` is set as the
                                            `async-backup-count`,
                                            then all entries of the cache are copied to one other instance as asynchronous for
                                            fail-safety.
                                            `backup-count` + `async-backup-count` cannot be bigger than maximum backup count which
                                            is `6`.
                                            Valid numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="hot-restart-enabled" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This boolean parameter enables hot-restart feature when set as true.
                                            Only available on Hazelcast Enterprise.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="multimap" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="entry-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="entry-listener" type="entry-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" type="xs:string" default="default"/>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of sync backups. If 1 is set as the backup-count for example, then
                                            all
                                            entries of the map will be copied to another JVM for fail-safety. Valid
                                            numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Number of async backups. If 1 is set as the backup-count for example, then
                                            all
                                            entries of the map will be copied to another JVM for fail-safety. Valid
                                            numbers are 0 (no backup), 1, 2 ... 6.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="value-collection-type" type="xs:string" default="SET">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Type of value collection. It can be Set or List.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean"
                                              default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            You can retrieve some statistics like owned entry count, backup entry count,
                                            last update time, locked entry count by setting this parameter's value
                                            as "true". The method for retrieving the statistics is `getLocalMultiMapStats()`.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="binary" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            By default, BINARY in-memory format is used, meaning that the object is stored
                                            in a serialized form. You can set it to false, then, the OBJECT in-memory format
                                            is used, which is useful when the OBJECT in-memory format has a smaller memory
                                            footprint than BINARY.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="list" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="item-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="item-listener" type="item-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the split brain
                                                protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="max-size" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Maximum size. Any integer between 0 and Integer.MAX_VALUE. 0 means
                                            Integer.MAX_VALUE. Default is 0.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of synchronous backups. Remember that, List is a non-partitioned data
                                            structure, i.e. all entries of a List resides in one partition. When this
                                            parameter is '1', it means there will be a backup of that List in another
                                            node in the cluster. When it is '2', 2 nodes will have the backup.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of asynchronous backups.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="set" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="item-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="item-listener" type="item-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the
                                                split-brain-protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" use="required" type="xs:string"/>
                                <xs:attribute name="max-size" type="xs:string">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Maximum size. Any integer between 0 and Integer.MAX_VALUE. 0 means
                                            Integer.MAX_VALUE. Default is 0.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of synchronous backups. Remember that, Set is a non-partitioned data
                                            structure, i.e. all entries of a List resides in one partition. When this
                                            parameter is '1', it means there will be a backup of that List in another
                                            node in the cluster. When it is '2', 2 nodes will have the backup.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="async-backup-count" type="parameterized-backup-count">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Count of asynchronous backups.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="topic" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="statistics-enabled" type="parameterized-boolean" minOccurs="0"
                                                default="true">
                                        <xs:annotation>
                                            <xs:documentation>
                                                If set as `true`, you can retrieve statistics for the topic using the
                                                method `getLocalTopicStats()`.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="global-ordering-enabled" type="parameterized-boolean" minOccurs="0"
                                                default="false">
                                        <xs:annotation>
                                            <xs:documentation>
                                                By default, it is false, meaning there is no global order
                                                guarantee by default.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="message-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="message-listener" type="listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="multi-threading-enabled" type="parameterized-boolean" minOccurs="0"
                                                default="false">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Default is `false`, meaning only one dedicated thread will handle topic messages.
                                                When multi-threading enabled (true) all threads from event thread pool can be used
                                                for message handling.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="name" type="xs:string" default="default"/>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="replicatedmap" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    A replicated map is a implementation
                                    of the map
                                    interface which is not
                                    partitioned but fully replicates all data to all members.
                                    Due to the nature of weak consistency there is a chance of reading staled data
                                    and no
                                    guarantee is given to retrieve the same value on multiple get calls.
                                    ReplicatedMap was added in Hazelcast 3.2.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="entry-listeners" minOccurs="0">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="entry-listener" type="entry-listener" minOccurs="0"
                                                            maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the
                                                split-brain-protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="merge-policy" type="merge-policy" minOccurs="0"/>
                                </xs:sequence>
                                <xs:attribute name="name" type="xs:string" default="default"/>
                                <xs:attribute name="in-memory-format" type="in-memory-format" default="OBJECT"/>
                                <xs:attribute name="async-fillup" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            This value defines it the replicated map is available for reads before the
                                            initial
                                            replication is completed. Default is true. If set to false no Exception will
                                            be
                                            thrown when replicated map is not yet ready but call will block until
                                            finished.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true"/>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="listeners" type="listeners" minOccurs="0"/>
                        <xs:element name="serialization" type="serialization" minOccurs="0"/>
                        <xs:element name="native-memory" type="native-memory" minOccurs="0"/>
                        <xs:element name="security" type="security" minOccurs="0"/>
                        <xs:element name="member-attributes" minOccurs="0">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="attribute" type="attribute" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Specify the name, type and value of your attribute here.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                            <xs:unique name="uniqueAttributeConstraint">
                                <xs:selector xpath="./*"/>
                                <xs:field xpath="@name"/>
                            </xs:unique>
                        </xs:element>
                        <xs:element name="split-brain-protection" type="split-brain-protection" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="lite-member" minOccurs="0">
                            <xs:complexType>
                                <xs:attribute name="enabled" type="parameterized-boolean">
                                    <xs:annotation>
                                        <xs:documentation>
                                            True to set the node as a lite member, false otherwise.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="hot-restart-persistence" type="hot-restart-persistence" minOccurs="0"/>
                        <xs:element name="flake-id-generator" type="flake-id-generator" minOccurs="0"/>
                        <xs:element name="crdt-replication" type="crdt-replication" minOccurs="0"/>
                        <xs:element name="pn-counter" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:all>
                                    <xs:element name="replica-count" type="crdt-replica-count" minOccurs="0"
                                                default="2147483647">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Number of replicas on which the CRDT state will be kept. The updates are
                                                replicated
                                                asynchronously between replicas.
                                                The number must be greater than 1 and up to 2147483647 (Integer.MAX_VALUE).
                                                The default value is 2147483647 (Integer.MAX_VALUE).
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="split-brain-protection-ref" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Adds the Split Brain Protection for this data-structure which you configure using
                                                the split-brain-protection element.
                                                You should set the split-brain-protection-ref's value as the
                                                split-brain-protection's name.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:all>
                                <xs:attribute name="name" type="xs:string" default="default">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Name of the PN counter.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Enable/disable statistics for this PN counter.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="cp-subsystem" type="cp-subsystem" minOccurs="0"/>
                        <xs:element name="metrics" type="metrics" minOccurs="0"/>
                        <xs:element name="instance-tracking" type="instance-tracking" minOccurs="0"/>
                        <xs:element name="sql" type="sql" minOccurs="0"/>
                        <xs:element name="auditlog" type="auditlog" minOccurs="0"/>
                    </xs:choice>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="data-serializable-factories">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="data-serializable-factory" type="serialization-factory" minOccurs="0"
                            maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Custom classes implementing com.hazelcast.nio.serialization.DataSerializableFactory to be registered.
                            These can be used to speed up serialization/deserialization of objects.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="portable-factories">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="portable-factory" type="serialization-factory" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            PortableFactory class to be registered.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="serializers">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="global-serializer" type="global-serializer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            Global serializer class to be registered if no other serializer is applicable.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="serializer" type="serializer" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Defines the class name of the serializer implementation.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="java-serialization-filter">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="blacklist" type="filter-list" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            Blacklist used for deserialization class filtering.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="whitelist" type="filter-list" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>
                            Blacklist used for deserialization class filtering.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="defaults-disabled" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Disables including default list entries (hardcoded in Hazelcast source code).
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:boolean"/>
                </xs:simpleType>
            </xs:attribute>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="filter-list">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="class" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Name of a class to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="package" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Name of a package to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="prefix" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Class name prefix to be included in the list.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="serialization-factory">
        <xs:attributeGroup ref="class-or-bean-name"/>
        <xs:attribute name="factory-id" type="xs:string" use="required"/>
    </xs:complexType>

    <xs:complexType name="global-serializer">
        <xs:attributeGroup ref="class-or-bean-name"/>
        <xs:attribute name="override-java-serialization" type="parameterized-boolean" default="false"/>
    </xs:complexType>

    <xs:complexType name="serializer">
        <xs:attributeGroup ref="class-or-bean-name"/>
        <xs:attribute name="type-class" type="xs:string" use="required"/>
    </xs:complexType>

    <xs:element name="hazelcast">
        <xs:annotation>
            <xs:documentation>
                Configure the hazelcast instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.core.HazelcastInstance"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="hazelcast-bean">
                    <xs:sequence>
                        <xs:element ref="config" minOccurs="0"/>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>

    <xs:element name="client-failover">
        <xs:annotation>
            <xs:documentation>
                Configure the hazelcast client with multiple configs. Client will connect to first one and when disconnected
                from that it will connect to next available cluster via given alternative configs.
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.core.HazelcastInstance"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>

        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="hazelcast-bean">
                    <xs:sequence>
                        <xs:element name="client" type="client-type" maxOccurs="unbounded"/>
                    </xs:sequence>
                    <xs:attribute name="try-count" type="xs:int" default="0"/>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>


    <xs:element name="client" type="client-type">
        <xs:annotation>
            <xs:documentation>
                Configure the hazelcast client
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.core.HazelcastInstance"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="client-type">
        <xs:complexContent>
            <xs:extension base="hazelcast-bean">
                <xs:choice minOccurs="0" maxOccurs="unbounded">
                    <xs:element name="spring-aware" type="xs:string" minOccurs="0"/>
                    <xs:element name="instance-name" type="xs:string" minOccurs="0"/>
                    <xs:element name="cluster-name" type="xs:string" minOccurs="0"/>
                    <xs:element name="properties" type="properties" minOccurs="0"/>
                    <xs:element name="labels" type="labels" minOccurs="0"/>
                    <xs:element name="backup-ack-to-client-enabled" type="xs:boolean" minOccurs="0" default="true"/>
                    <xs:element name="network" type="network-client" minOccurs="0"/>
                    <xs:element name="security" type="client-security" minOccurs="0"/>
                    <xs:element name="listeners" type="listeners" minOccurs="0"/>
                    <xs:element name="serialization" type="serialization" minOccurs="0"/>
                    <xs:element name="proxy-factories" type="proxy-factories" minOccurs="0"/>
                    <xs:element name="load-balancer" type="load-balancer" minOccurs="0"/>
                    <xs:element name="near-cache" type="near-cache-client" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="query-caches" type="query-caches-client" minOccurs="0"/>
                    <xs:element name="connection-strategy" type="connection-strategy" minOccurs="0"/>
                    <xs:element name="user-code-deployment" type="user-code-deployment-client" minOccurs="0"/>
                    <xs:element name="flake-id-generator" type="client-flake-id-generator" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="reliable-topic" type="client-reliable-topic" minOccurs="0" maxOccurs="unbounded"/>
                    <xs:element name="metrics" type="client-metrics" minOccurs="0"/>
                    <xs:element name="instance-tracking" type="instance-tracking" minOccurs="0"/>
                    <xs:element name="native-memory" type="native-memory" minOccurs="0"/>
                </xs:choice>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="map" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IMap instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.map.IMap"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>

    <xs:element name="cache-manager">
        <xs:annotation>
            <xs:documentation>
                Retrieve a JCache cache manager from specified Hazelcast instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="javax.cache.CacheManager"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="hazelcast-bean">
                    <xs:sequence>
                        <xs:element name="properties" type="properties" minOccurs="0"/>
                    </xs:sequence>
                    <xs:attribute name="instance-ref" type="xs:string">
                        <xs:annotation>
                            <xs:documentation>
                                <![CDATA[The name of the HazelcastInstance that this bean depends on.]]>
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="name" type="xs:string" use="required">
                        <xs:annotation>
                            <xs:documentation>
                                <![CDATA[The name of this bean in Hazelcast context (HazelcastInstance.getMap(name)).]]>
                            </xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                    <xs:attribute name="uri" type="xs:string"/>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>
    <xs:element name="multiMap" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast MultiMap instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.multimap.MultiMap"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="replicatedMap" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ReplicatedMap instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.replicatedmap.ReplicatedMap"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="queue" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IQueue instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.collection.IQueue"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="ringbuffer" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast Ringbuffer instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.ringbuffer.Ringbuffer"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="topic" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ITopic instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.topic.ITopic"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="reliableTopic" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ITopic instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.topic.ITopic"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="set" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ISet instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.collection.ISet"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="list" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IList instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.collection.IList"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="executorService" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IExecutorService instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.core.IExecutorService"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="durableExecutorService" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IExecutorService instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.durableexecutor.DurableExecutorService"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="scheduledExecutorService" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IScheduledExecutorService instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.scheduledexecutor.IScheduledExecutorService"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="flakeIdGenerator" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast FlakeIdGenerator instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.flakeidgen.FlakeIdGenerator"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="cardinalityEstimator" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast CardinalityEstimator instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cardinality.CardinalityEstimator"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="atomicLong" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IAtomicLong instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cp.IAtomicLong"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="atomicReference" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast IAtomicReference instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cp.IAtomicReference"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="countDownLatch" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ICountDownLatch instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cp.ICountDownLatch"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="semaphore" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast ISemaphore instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cp.ISemaphore"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="lock" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast FencedLock instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.cp.lock.FencedLock"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>
    <xs:element name="PNCounter" type="hazelcast-type">
        <xs:annotation>
            <xs:documentation>
                Retrieve a Hazelcast PNCounter instance
            </xs:documentation>
            <xs:appinfo>
                <tool:annotation>
                    <tool:exports type="com.hazelcast.crdt.pncounter.PNCounter"/>
                </tool:annotation>
            </xs:appinfo>
        </xs:annotation>
    </xs:element>

    <xs:element name="hibernate-region-factory" type="hibernate-cache"/>

    <!-- internal elements -->
    <xs:complexType name="hazelcast-bean">
        <xs:attribute name="id" type="xs:string">
            <xs:annotation>
                <xs:documentation>
                    <![CDATA[The unique identifier for a bean.
                    A bean ID may not be used more than once within the same <beans> element.]]>
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="lazy-init" type="xs:string" default="false">
            <xs:annotation>
                <xs:documentation>
                    <![CDATA[Indicates whether or not this bean is to be lazily initialized.
                    If false, it will be instantiated on startup by bean factories that perform
                    eager initialization of singletons.]]>
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="scope" type="xs:string" default="singleton">
            <xs:annotation>
                <xs:documentation>
                    <![CDATA[The scope of this bean: typically "singleton", or "prototype".]]>
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="depends-on" type="xs:string">
            <xs:annotation>
                <xs:documentation>
                    <![CDATA[The names of the beans that this bean depends on being initialized.]]>
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="hazelcast-type">
        <xs:complexContent>
            <xs:extension base="hazelcast-bean">
                <xs:sequence/>
                <xs:attribute name="instance-ref" type="xs:string" use="required">
                    <xs:annotation>
                        <xs:documentation>
                            <![CDATA[The name of the HazelcastInstance that this bean depends on.]]>
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
                <xs:attribute name="name" type="xs:string" use="required">
                    <xs:annotation>
                        <xs:documentation>
                            <![CDATA[The name of this bean in Hazelcast context (HazelcastInstance.getMap(name)).]]>
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="network">
        <xs:sequence>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0"/>
            <xs:element name="join" type="join" minOccurs="0"/>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="ssl" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as DES/ECB/PKCS5Padding, PBEWithMD5AndDES, AES/CBC/PKCS5Padding,
                        Blowfish, DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="reuse-address" type="parameterized-boolean" minOccurs="0" default="false"/>
            <xs:element name="member-address-provider" type="member-address-provider" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This configuration is not intended to provide addresses of other cluster members with
                        which the hazelcast instance will form a cluster. This is an SPI for advanced use in
                        cases where the DefaultAddressPicker does not pick suitable addresses to bind to
                        and publish to other cluster members. For instance, this could allow easier
                        deployment in some cases when running on Docker, AWS or other cloud environments.
                        That said, if you are just starting with Hazelcast, you will probably want to
                        set the member addresses by using the tcp-ip or multicast configuration
                        or adding a discovery strategy.
                        Member address provider allows to plug in own strategy to customize:
                        1. What address Hazelcast will bind to
                        2. What address Hazelcast will advertise to other members on which they can bind to

                        In most environments you don't need to customize this and the default strategy will work just
                        fine. However in some cloud environments the default strategy does not make the right choice and
                        the member address provider delegates the process of address picking to external code.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="failure-detector" type="failure-detector" minOccurs="0"/>
            <xs:element name="rest-api" type="rest-api" minOccurs="0"/>
            <xs:element name="memcache-protocol" type="memcache-protocol" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="public-address" type="xs:string"/>
        <xs:attribute name="port" type="xs:string" use="required"/>
        <xs:attribute name="port-auto-increment" type="xs:string" default="true"/>
        <xs:attribute name="port-count" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="tcp-ip">
        <xs:sequence>
            <xs:element name="required-member" type="xs:string" minOccurs="0"/>
            <xs:choice>
                <xs:element name="members" type="members" default="127.0.0.1"/>
                <xs:sequence>
                    <xs:element name="member" type="member" default="127.0.0.1" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
                <xs:sequence>
                    <xs:element name="interface" type="interface" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:string" default="false"/>
        <xs:attribute name="connection-timeout-seconds" type="xs:string" default="5"/>
    </xs:complexType>

    <xs:complexType name="multicast">
        <xs:sequence>
            <xs:element name="trusted-interfaces" type="trusted-interfaces" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Includes IP addresses of trusted members. When a node wants to join to the cluster,
                        its join request will be rejected if it is not a trusted member. You can give an IP
                        addresses range using the wildcard (*) on the last digit of the IP address
                        (e.g. 192.168.1.* or *************-110).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:string" default="true"/>
        <xs:attribute name="multicast-group" type="xs:string" default="*********"/>
        <xs:attribute name="multicast-port" type="xs:string" default="54327"/>
        <xs:attribute name="multicast-timeout-seconds" type="xs:string" default="2"/>
        <xs:attribute name="multicast-time-to-live" type="xs:string" default="32"/>
        <xs:attribute name="loopback-mode-enabled" type="parameterized-boolean" default="false"/>
    </xs:complexType>

    <xs:complexType name="aliased-discovery-strategy">
        <xs:sequence/>
        <xs:anyAttribute processContents="skip"/>
    </xs:complexType>

    <xs:complexType name="discovery-strategies">
        <xs:sequence>
            <xs:element name="node-filter" type="discovery-node-filter" minOccurs="0"/>
            <xs:element name="discovery-strategy" type="discovery-strategy" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="discovery-service-provider" type="discovery-service-provider" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="discovery-service-provider">
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>
    <xs:complexType name="discovery-node-filter">
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>
    <xs:complexType name="discovery-strategy">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attributeGroup ref="class-or-bean-name"/>
        <xs:attribute name="discovery-strategy-factory" type="non-space-string"/>
    </xs:complexType>

    <xs:complexType name="auto-detection">
        <xs:attribute name="enabled" type="xs:string" default="false"/>
    </xs:complexType>

    <xs:complexType name="wan-sync">
        <xs:all>
            <xs:element name="consistency-check-strategy" type="consistency-check-strategy" minOccurs="0" default="NONE">
                <xs:annotation>
                    <xs:documentation>
                        Sets the strategy for checking consistency of data between source and
                        target cluster. Any inconsistency will not be reconciled, it will be
                        merely reported via the usual mechanisms (e.g. statistics, diagnostics).
                        The user must initiate WAN sync to reconcile there differences. For the
                        check procedure to work properly, the target cluster should support the
                        chosen strategy.
                        Default value is NONE, which means the check is disabled.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="merge-policy">
        <xs:annotation>
            <xs:documentation>
                While recovering from split-brain (network partitioning), data structure entries in the small cluster
                merge into the bigger cluster based on the policy set here. When an entry merges into the cluster,
                an entry with the same key (or value) might already exist in the cluster.
                The merge policy resolves these conflicts with different out-of-the-box or custom strategies.
                The out-of-the-box merge polices can be references by their simple class name.
                For custom merge policies you have to provide a fully qualified class name.
                <p>
                    The out-of-the-box policies are:
                    <br/>DiscardMergePolicy: the entry from the smaller cluster will be discarded.
                    <br/>HigherHitsMergePolicy: the entry with the higher number of hits wins.
                    <br/>LatestAccessMergePolicy: the entry with the latest access wins.
                    <br/>LatestUpdateMergePolicy: the entry with the latest update wins.
                    <br/>PassThroughMergePolicy: the entry from the smaller cluster wins.
                    <br/>PutIfAbsentMergePolicy: the entry from the smaller cluster wins if it doesn't exist in the cluster.
                    <br/>The default policy is: PutIfAbsentMergePolicy
                </p>
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="batch-size" default="100" type="xs:positiveInteger"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="merge-policies">
        <xs:sequence>
            <xs:element name="map-merge-policy" type="map-merge-policy" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="map-merge-policy">
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>

    <xs:complexType name="outbound-ports">
        <xs:sequence>
            <xs:element name="ports" minOccurs="0" maxOccurs="unbounded">
                <xs:simpleType>
                    <xs:restriction base="xs:string"/>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="join">
        <xs:sequence>
            <xs:element name="multicast" type="multicast" minOccurs="0"/>
            <xs:element name="tcp-ip" type="tcp-ip" minOccurs="0"/>
            <xs:element name="aws" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="gcp" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="azure" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="kubernetes" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="eureka" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="discovery-strategies" type="discovery-strategies" minOccurs="0"/>
            <xs:element name="auto-detection" type="auto-detection" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="interfaces">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="interface" type="interface" default="127.0.0.1"/>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:string" default="false"/>
    </xs:complexType>

    <xs:simpleType name="interface">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

    <xs:simpleType name="cache-deserialized">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NEVER"/>
            <xs:enumeration value="ALWAYS"/>
            <xs:enumeration value="INDEX-ONLY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="parameterized-cache-deserialized">
        <xs:union memberTypes="cache-deserialized parameterizedValueType"/>
    </xs:simpleType>

    <xs:simpleType name="member">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

    <xs:simpleType name="members">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

    <xs:simpleType name="propertyNameEnum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="hazelcast.merge.first.run.delay.seconds"/>
            <xs:enumeration value="hazelcast.merge.next.run.delay.seconds"/>
            <xs:enumeration value="hazelcast.redo.wait.millis"/>
            <xs:enumeration value="hazelcast.socket.bind.any"/>
            <xs:enumeration value="hazelcast.serializer.gzip.enabled"/>
            <xs:enumeration value="hazelcast.serializer.shared"/>
            <xs:enumeration value="hazelcast.shutdownhook.enabled"/>
            <xs:enumeration value="hazelcast.wait.seconds.before.join"/>
            <xs:enumeration value="hazelcast.max.no.heartbeat.seconds"/>
            <xs:enumeration value="hazelcast.initial.wait.seconds"/>
            <xs:enumeration value="hazelcast.restart.on.max.idle"/>
            <xs:enumeration value="hazelcast.partition.count"/>
            <xs:enumeration value="hazelcast.map.remove.delay.seconds"/>
            <xs:enumeration value="hazelcast.map.cleanup.delay.seconds"/>
            <xs:enumeration value="hazelcast.executor.query.thread.count"/>
            <xs:enumeration value="hazelcast.executor.event.thread.count"/>
            <xs:enumeration value="hazelcast.executor.migration.thread.count"/>
            <xs:enumeration value="hazelcast.executor.client.thread.count"/>
            <xs:enumeration value="hazelcast.executor.store.thread.count"/>
            <xs:enumeration value="hazelcast.log.state"/>
            <xs:enumeration value="hazelcast.jmx"/>
            <xs:enumeration value="hazelcast.jmx.detailed"/>
            <xs:enumeration value="hazelcast.map.load.chunk.size"/>
            <xs:enumeration value="hazelcast.in.thread.priority"/>
            <xs:enumeration value="hazelcast.out.thread.priority"/>
            <xs:enumeration value="hazelcast.service.thread.priority"/>
            <xs:enumeration value="hazelcast.socket.receive.buffer.size"/>
            <xs:enumeration value="hazelcast.socket.send.buffer.size"/>
            <xs:enumeration value="hazelcast.socket.keep.alive"/>
            <xs:enumeration value="hazelcast.socket.no.delay"/>
            <xs:enumeration value="hazelcast.heartbeat.interval.seconds"/>
            <xs:enumeration value="hazelcast.icmp.enabled"/>
            <xs:enumeration value="hazelcast.initial.min.cluster.size"/>
            <xs:enumeration value="hazelcast.mc.atomiclong.excludes"/>
            <xs:enumeration value="hazelcast.mc.countdownlatch.excludes"/>
            <xs:enumeration value="hazelcast.mc.map.excludes"/>
            <xs:enumeration value="hazelcast.mc.queue.excludes"/>
            <xs:enumeration value="hazelcast.mc.semaphore.excludes"/>
            <xs:enumeration value="hazelcast.mc.topic.excludes"/>
            <xs:enumeration value="hazelcast.phone.home.enabled"/>
            <xs:enumeration value="hazelcast.map.max.backup.count"/>
            <xs:enumeration value="hazelcast.max.wait.seconds.before.join"/>
            <xs:enumeration value="hazelcast.logging.type"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="propertyNameString">
        <xs:restriction base="non-space-string"/>
    </xs:simpleType>
    <xs:simpleType name="propertyName">
        <xs:union memberTypes="propertyNameEnum propertyNameString"/>
    </xs:simpleType>
    <xs:complexType name="property">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" use="required" type="propertyName"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="properties">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="property" type="property"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="attributeName">
        <xs:restriction base="non-space-string"/>
    </xs:simpleType>
    <xs:complexType name="attribute">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="name" use="required" type="attributeName"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="attributes">
        <xs:sequence>
            <xs:element name="attribute" type="attribute" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="labels">
        <xs:sequence>
            <xs:element name="label" type="non-space-string" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="mutual-auth">
        <xs:all>
            <xs:element name="factory-class-name" type="xs:string" minOccurs="0"/>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="enabled" default="false" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="ssl">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="enabled" default="false" type="xs:string"/>
        <xs:attribute name="factory-class-name" type="xs:string"/>
        <xs:attribute name="factory-implementation" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="socket-interceptor">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="enabled" default="false" type="xs:string"/>
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>

    <xs:complexType name="symmetric-encryption">
        <xs:sequence/>
        <xs:attribute name="enabled" type="xs:string" default="false"/>
        <xs:attribute name="algorithm" type="xs:string"/>
        <xs:attribute name="salt" type="xs:string" default="thesalt"/>
        <xs:attribute name="password" type="xs:string" default="thepassword"/>
        <xs:attribute name="iteration-count" type="xs:string" default="19"/>
    </xs:complexType>

    <xs:complexType name="member-address-provider">
        <xs:all>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:all>
        <xs:attributeGroup ref="class-or-bean-name">
            <xs:annotation>
                <xs:documentation>
                    The name of the class implementing the com.hazelcast.spi.MemberAddressProvider interface.
                    If both the implementation and the class name are provided, the implementation is used
                    and the class name is ignored.
                </xs:documentation>
            </xs:annotation>
        </xs:attributeGroup>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    Specifies whether the member address provider SPI is enabled or not. Values can be true or false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="icmp">
        <xs:annotation>
            <xs:documentation>
                ICMP can be used in addition to the other detectors. It operates at layer 3 detects network
                and hardware issues more quickly
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="timeout-milliseconds" type="xs:integer" minOccurs="0" default="1000">
                <xs:annotation>
                    <xs:documentation>Timeout in Milliseconds before declaring a failed ping</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ttl" type="xs:integer" minOccurs="0" default="255">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of times the IP Datagram (ping) can be forwarded, in most cases
                        all Hazelcast cluster members would be within one network switch/router therefore
                        default of 0 is usually sufficient
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="parallel-mode" type="xs:boolean" minOccurs="0" default="true">
                <xs:annotation>
                    <xs:documentation>Run ICMP detection in parallel with the Heartbeat failure detector</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fail-fast-on-startup" type="xs:boolean" minOccurs="0" default="true">
                <xs:annotation>
                    <xs:documentation>
                        Cluster Member will fail to start if it is unable to action an ICMP ping command when ICMP is enabled.
                        Failure is usually due to OS level restrictions.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-attempts" type="xs:integer" minOccurs="0" default="2">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of consecutive failed attempts before declaring a member suspect
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="interval-milliseconds" type="xs:integer" minOccurs="0" default="1000">
                <xs:annotation>
                    <xs:documentation>Time in milliseconds between each ICMP ping</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" default="false">
            <xs:annotation>
                <xs:documentation>Enables ICMP Pings to detect and suspect dead members</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="failure-detector">
        <xs:all>
            <xs:element name="icmp" type="icmp" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="hibernate-cache">
        <xs:complexContent>
            <xs:extension base="hazelcast-bean">
                <xs:attribute name="instance-ref" type="xs:string" use="required"/>
                <!-- valid values are DISTRIBUTED and LOCAL -->
                <xs:attribute name="mode" type="xs:string" default="DISTRIBUTED"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="non-space-string">
        <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:pattern value="\S+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="wan-replication">
        <xs:sequence>
            <xs:element name="batch-publisher"
                        type="wan-batch-publisher"
                        minOccurs="0"
                        maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Configuration object for the built-in WAN publisher (available in
                        Hazelcast Enterprise). The publisher sends events to another Hazelcast
                        cluster in batches, sending when either when enough events are enqueued
                        or enqueued events have waited for enough time.
                        The endpoint can be a different cluster defined by static IP's or
                        discovered using a cloud discovery mechanism.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="custom-publisher"
                        type="wan-custom-publisher"
                        minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Configuration object for a custom WAN publisher. A single publisher defines how
                        WAN events are sent to a specific endpoint.
                        The endpoint can be some other external system which is
                        not a Hazelcast cluster (e.g. JMS queue).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="consumer" type="wan-consumer" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="wan-batch-publisher" mixed="true">
        <xs:all>
            <xs:element name="cluster-name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Sets the cluster name used as an endpoint cluster name for authentication
                        on the target endpoint.
                        If there is no separate publisher ID property defined, this cluster name
                        will also be used as a WAN publisher ID. This ID is then used for
                        identifying the publisher in a WanReplicationConfig.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="snapshot-enabled" type="parameterized-boolean" default="false" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets if key-based coalescing is configured for this WAN publisher.
                        When enabled, only the latest WanReplicationEvent
                        of a key is sent to target.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="initial-publisher-state"
                        type="initial-publisher-state"
                        default="REPLICATING" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Defines the initial state in which a WAN publisher is started.
                        - REPLICATING (default):
                        State where both enqueuing new events is allowed, enqueued events are replicated to the target cluster
                        and WAN sync is enabled.
                        - PAUSED:
                        State where new events are enqueued but they are not dequeued. Some events which have been dequeued before
                        the state was switched may still be replicated to the target cluster but further events will not be
                        replicated. WAN sync is enabled.
                        - STOPPED:
                        State where neither new events are enqueued nor dequeued. As with the PAUSED state, some events might
                        still be replicated after the publisher has switched to this state. WAN sync is enabled.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queue-capacity" type="parameterized-positive-integer" default="10000" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the capacity of the primary and backup queue for WAN replication events.
                        One hazelcast instance can have up to 2*queueCapacity events since
                        we keep up to queueCapacity primary events (events with keys for
                        which the instance is the owner) and queueCapacity backup events
                        (events with keys for which the instance is the backup).
                        Events for IMap and ICache count against this limit collectively.
                        When the queue capacity is reached, backup events are dropped while normal
                        replication events behave as determined by the queue-full-behavior.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="batch-size" type="parameterized-positive-integer" default="500" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum batch size that can be sent to target cluster.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="batch-max-delay-millis" type="parameterized-positive-integer" default="1000" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum amount of time in milliseconds to wait before sending a
                        batch of events to target cluster, if batch-size of events
                        have not arrived within this duration.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="response-timeout-millis" type="parameterized-positive-integer" default="60000" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the duration in milliseconds for the waiting time before retrying to
                        send the events to target cluster again in case of acknowledgement
                        is not arrived.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="queue-full-behavior"
                        type="wan-queue-full-behavior"
                        default="DISCARD_AFTER_MUTATION"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the configured behaviour of this WAN publisher when the WAN queue is
                        full.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="acknowledge-type"
                        type="wan-acknowledge-type"
                        default="ACK_ON_OPERATION_COMPLETE"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the strategy for when the target cluster should acknowledge that
                        a WAN event batch has been processed.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="discovery-period-seconds"
                        type="parameterized-positive-integer"
                        default="10"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the period in seconds in which WAN tries to discover new target
                        endpoints and reestablish connections to failed endpoints.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-target-endpoints"
                        type="parameterized-positive-integer"
                        default="2147483647"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Returns the maximum number of endpoints that WAN will connect to when
                        using a discovery mechanism to define endpoints.
                        This property has no effect when static endpoint addresses are defined
                        using target-endpoints.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-concurrent-invocations"
                        type="xs:int"
                        default="-1"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum number of WAN event batches being sent to the target
                        cluster concurrently.
                        Setting this property to anything less than 2 will only allow a
                        single batch of events to be sent to each target endpoint and will
                        maintain causality of events for a single partition.
                        Setting this property to 2 or higher will allow multiple batches
                        of WAN events to be sent to each target endpoint. Since this allows
                        reordering or batches due to network conditions, causality and ordering
                        of events for a single partition is lost and batches for a single
                        partition are now sent randomly to any available target endpoint.
                        This, however, does present faster WAN replication for certain scenarios
                        such as replicating immutable, independent map entries which are only
                        added once and where ordering of when these entries are added is not
                        necessary.
                        Keep in mind that if you set this property to a value which is less than
                        the target endpoint count, you will lose performance as not all target
                        endpoints will be used at any point in time to process WAN event batches.
                        So, for instance, if you have a target cluster with 3 members (target
                        endpoints) and you want to use this property, it makes sense to set it
                        to a value higher than 3. Otherwise, you can simply disable it
                        by setting it to less than 2 in which case WAN will use the
                        default replication strategy and adapt to the target endpoint count
                        while maintaining causality.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="use-endpoint-private-address"
                        type="parameterized-boolean"
                        default="false" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets whether the WAN connection manager should connect to the
                        endpoint on the private address returned by the discovery SPI.
                        By default this property is false which means the WAN connection
                        manager will always use the public address.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idle-min-park-ns"
                        type="parameterized-unsigned-long"
                        default="10000000"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the minimum duration in nanoseconds that the WAN replication thread
                        will be parked if there are no events to replicate.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="idle-max-park-ns"
                        type="parameterized-unsigned-long"
                        default="250000000"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the maximum duration in nanoseconds that the WAN replication thread
                        will be parked if there are no events to replicate.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="publisher-id" type="xs:string"
                        minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the publisher ID used for identifying the publisher in a
                        WanReplicationConfig.
                        If there is no publisher ID defined (it is empty), the cluster name will
                        be used as a publisher ID.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target-endpoints" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Comma separated list of target cluster members,
                        e.g. 127.0.0.1:5701, 127.0.0.1:5702.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="aws" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="gcp" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="azure" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="kubernetes" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="eureka" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="discovery-strategies" type="discovery-strategies" minOccurs="0"/>
            <xs:element name="sync" type="wan-sync" minOccurs="0"/>
            <xs:element name="endpoint" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Reference to the name of a WAN endpoint config or WAN server socket endpoint config.
                        The network settings from the referenced endpoint configuration will setup the network
                        configuration of connections to the target WAN cluster members.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="wan-custom-publisher" mixed="true">
        <xs:all>
            <xs:element name="publisher-id" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Sets the publisher ID used for identifying the publisher in a
                        WanReplicationConfig.
                        If there is no publisher ID defined (it is empty), the cluster name will
                        be used as a publisher ID.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:all>
        <xs:attributeGroup ref="class-or-bean-name">
            <xs:annotation>
                <xs:documentation>
                    Fully qualified class name of WAN Replication implementation WanPublisher.
                </xs:documentation>
            </xs:annotation>
        </xs:attributeGroup>
    </xs:complexType>


    <xs:complexType name="wan-consumer">
        <xs:all>
            <xs:element name="properties" type="properties" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Properties for the custom WAN consumer. These properties are
                        accessible when initalizing the WAN consumer.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attributeGroup ref="class-or-bean-name">
            <xs:annotation>
                <xs:documentation>
                    Defines a custom WAN consumer (WanConsumer).
                    If you don't define a class name or implementation, the default processing
                    logic for incoming WAN events will be used.
                </xs:documentation>
            </xs:annotation>
        </xs:attributeGroup>
        <xs:attribute name="persist-wan-replicated-data" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    When true, an incoming event over WAN replication can be persisted to a
                    database for example, otherwise it will not be persisted. Default value
                    is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>


    <xs:complexType name="memcache-protocol">
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
    </xs:complexType>
    <xs:complexType name="rest-api">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="endpoint-group" type="endpoint-group" minOccurs="0" maxOccurs="unbounded"/>
        </xs:choice>
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
    </xs:complexType>
    <xs:complexType name="endpoint-group">
        <xs:attribute name="name" type="endpoint-group-name" use="required"/>
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
    </xs:complexType>
    <xs:simpleType name="endpoint-group-name">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CLUSTER_READ"/>
            <xs:enumeration value="CLUSTER_WRITE"/>
            <xs:enumeration value="HEALTH_CHECK"/>
            <xs:enumeration value="HOT_RESTART"/>
            <xs:enumeration value="WAN"/>
            <xs:enumeration value="DATA"/>
            <xs:enumeration value="MEMCACHE"/>
            <xs:enumeration value="CP"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="partition-group">
        <xs:sequence>
            <xs:element name="member-group" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="interface" type="interface" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="enabled" type="parameterized-boolean" use="required"/>
        <xs:attribute name="group-type" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="management-center">
        <xs:sequence>
            <xs:element name="trusted-interfaces" type="trusted-interfaces" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Includes allowed IP addresses for Management Center connections.
                        More preciselly, the element configures addresses from which clients are allowed to run cluster
                        management tasks over the Hazelcast client protocol.
                        You can give an IP addresses range using the wildcard (*) on the last digit of the IP address
                        (e.g. 192.168.1.* or *************-110).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="scripting-enabled" type="xs:boolean"/>
    </xs:complexType>
    <xs:complexType name="trusted-interfaces">
        <xs:sequence>
            <xs:element name="interface" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="cache-entry-listener">
        <xs:attribute name="cache-entry-listener-factory" type="non-space-string"/>
        <xs:attribute name="cache-entry-event-filter-factory" type="non-space-string"/>
        <xs:attribute name="old-value-required" type="parameterized-boolean" default="false"/>
        <xs:attribute name="synchronous" type="parameterized-boolean" default="false"/>
    </xs:complexType>
    <xs:complexType name="listener">
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>
    <xs:complexType name="item-listener">
        <xs:complexContent>
            <xs:extension base="listener">
                <xs:attribute name="include-value" type="xs:string" default="true"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="entry-listener">
        <xs:complexContent>
            <xs:extension base="item-listener">
                <xs:attribute name="local" type="xs:string" default="false"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="security">
        <xs:sequence>
            <xs:element name="realms" type="realms" minOccurs="0"/>
            <xs:element name="member-authentication" type="realm-reference" minOccurs="0"/>
            <xs:element name="client-authentication" type="realm-reference" minOccurs="0"/>
            <xs:element name="client-permission-policy" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="properties" type="properties" minOccurs="0"/>
                    </xs:sequence>
                    <xs:attributeGroup ref="class-or-bean-name"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="client-permissions" minOccurs="0">
                <xs:complexType>
                    <xs:choice maxOccurs="unbounded">
                        <xs:element name="all-permissions" type="base-permission" minOccurs="0"/>
                        <xs:element name="map-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="queue-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="multimap-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="topic-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="list-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="set-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="lock-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="atomic-long-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="atomic-reference-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="countdown-latch-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="semaphore-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="flake-id-generator-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="executor-service-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="durable-executor-service-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="cardinality-estimator-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="scheduled-executor-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="pn-counter-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="transaction-permission" type="base-permission" minOccurs="0"/>
                        <xs:element name="cache-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="user-code-deployment-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="config-permission" type="base-permission" minOccurs="0"/>
                        <xs:element name="ring-buffer-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="reliable-topic-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="replicatedmap-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="replicatedmap-permission" type="instance-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                        <xs:element name="management-permission" type="management-permission" minOccurs="0"
                                    maxOccurs="unbounded"/>
                    </xs:choice>
                    <xs:attribute name="on-join-operation" type="permission-on-join-operation" default="RECEIVE"/>
                </xs:complexType>
            </xs:element>
            <xs:element name="security-interceptors" type="interceptors" minOccurs="0"/>
            <xs:element name="client-block-unmapped-actions" type="xs:boolean" default="true" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Block or allow actions, submitted as tasks in an Executor from clients and have no permission mappings.

                        true: Blocks all actions that have no permission mapping
                        false: Allows all actions that have no permission mapping
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="enabled" type="xs:string" default="false"/>
    </xs:complexType>
    <xs:simpleType name="permission-on-join-operation">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="RECEIVE"/>
            <xs:enumeration value="SEND"/>
            <xs:enumeration value="NONE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="interceptors">
        <xs:sequence>
            <xs:element name="interceptor" type="interceptor" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="interceptor">
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>

    <xs:complexType name="login-modules">
        <xs:sequence>
            <xs:element name="login-module" type="login-module" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="login-module">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attributeGroup ref="class-or-bean-name"/>
        <xs:attribute name="usage" default="required">
            <xs:simpleType>
                <xs:union>
                    <xs:simpleType>
                        <xs:restriction base="non-space-string">
                            <xs:enumeration value="REQUIRED"/>
                            <xs:enumeration value="OPTIONAL"/>
                            <xs:enumeration value="REQUISITE"/>
                            <xs:enumeration value="SUFFICIENT"/>
                        </xs:restriction>
                    </xs:simpleType>
                    <xs:simpleType>
                        <xs:restriction base="non-space-string"/>
                    </xs:simpleType>
                </xs:union>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="base-permission">
        <xs:sequence>
            <xs:element name="endpoints" type="endpoints" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="principal" type="xs:string" default="*">
            <xs:annotation>
                <xs:documentation>
                    Name of the principal. Wildcards(*) can be used.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="management-permission">
        <xs:complexContent>
            <xs:extension base="base-permission">
                <xs:attribute name="name" type="xs:string" use="optional">
                    <xs:annotation>
                        <xs:documentation>
                            Optional name of the permission. Simple wildcard (*) or prefixes (prefix.*) can be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="instance-permission">
        <xs:complexContent>
            <xs:extension base="base-permission">
                <xs:sequence>
                    <xs:element name="actions" type="actions"/>
                </xs:sequence>
                <xs:attribute name="name" type="xs:string" use="required">
                    <xs:annotation>
                        <xs:documentation>
                            Name of the permission. Wildcards(*) can be used.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="endpoints">
        <xs:sequence>
            <xs:element name="endpoint" maxOccurs="unbounded" default="127.0.0.1">
                <xs:annotation>
                    <xs:documentation>
                        Endpoint address of principal. Wildcards(*) can be used.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string"/>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="actions">
        <xs:sequence>
            <xs:element name="action" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Permission actions that are permitted on Hazelcast instance objects.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:union>
                        <xs:simpleType>
                            <xs:restriction base="non-space-string">
                                <xs:enumeration value="all"/>
                                <xs:enumeration value="create"/>
                                <xs:enumeration value="destroy"/>
                                <xs:enumeration value="modify"/>
                                <xs:enumeration value="read"/>
                                <xs:enumeration value="remove"/>
                                <xs:enumeration value="lock"/>
                                <xs:enumeration value="listen"/>
                                <xs:enumeration value="release"/>
                                <xs:enumeration value="acquire"/>
                                <xs:enumeration value="put"/>
                                <xs:enumeration value="add"/>
                                <xs:enumeration value="index"/>
                                <xs:enumeration value="intercept"/>
                                <xs:enumeration value="publish"/>
                            </xs:restriction>
                        </xs:simpleType>
                        <xs:simpleType>
                            <xs:restriction base="non-space-string"/>
                        </xs:simpleType>
                    </xs:union>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="serialization">
        <xs:sequence>
            <xs:element ref="data-serializable-factories" minOccurs="0"/>
            <xs:element ref="portable-factories" minOccurs="0"/>
            <xs:element ref="serializers" minOccurs="0"/>
            <xs:element ref="java-serialization-filter" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="use-native-byte-order" type="xs:string" default="false"/>
        <xs:attribute name="byte-order" default="BIG_ENDIAN">
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="BIG_ENDIAN"/>
                    <xs:enumeration value="LITTLE_ENDIAN"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="portable-version" type="xs:string"/>
        <xs:attribute name="check-class-def-errors" type="xs:string" default="true"/>
        <xs:attribute name="enable-compression" type="xs:string" default="false"/>
        <xs:attribute name="enable-shared-object" type="xs:string" default="true"/>
        <xs:attribute name="allow-unsafe" type="xs:string" default="false"/>
        <xs:attribute name="allow-override-default-serializers" type="xs:string" default="false"/>
    </xs:complexType>

    <xs:complexType name="network-client">
        <xs:sequence>
            <xs:element name="member" type="member" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="socket-options" type="socket-options" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="ssl" type="ssl" minOccurs="0"/>
            <xs:element name="aws" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="gcp" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="azure" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="kubernetes" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="eureka" type="aliased-discovery-strategy" minOccurs="0"/>
            <xs:element name="discovery-strategies" type="discovery-strategies" minOccurs="0"/>
            <xs:element name="auto-detection" type="auto-detection" minOccurs="0"/>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0"/>
            <xs:element name="icmp-ping" type="icmp-ping-client" minOccurs="0"/>
            <xs:element name="hazelcast-cloud" type="hazelcast-cloud-client" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="smart-routing" type="parameterized-boolean" default="true"/>
        <xs:attribute name="redo-operation" type="parameterized-boolean" default="false"/>
        <xs:attribute name="connection-timeout" type="parameterized-positive-integer" default="5000"/>
    </xs:complexType>

    <xs:complexType name="socket-options">
        <xs:attribute name="tcp-no-delay" type="parameterized-boolean" default="false"/>
        <xs:attribute name="keep-alive" type="parameterized-boolean" default="true"/>
        <xs:attribute name="reuse-address" type="parameterized-boolean" default="true"/>
        <xs:attribute name="linger-seconds" type="parameterized-unsigned-int" default="3"/>
        <xs:attribute name="buffer-size" default="32">
            <xs:simpleType>
                <xs:restriction base="xs:unsignedInt">
                    <xs:minInclusive value="1"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="icmp-ping-client">
        <xs:all>
            <xs:element name="timeout-milliseconds" type="xs:long" minOccurs="0" default="1000"/>
            <xs:element name="interval-milliseconds" type="xs:long" minOccurs="0" default="1000"/>
            <xs:element name="ttl" type="xs:int" minOccurs="0" default="255"/>
            <xs:element name="max-attempts" type="xs:int" minOccurs="0" default="2"/>
            <xs:element name="echo-fail-fast-on-startup" type="xs:boolean" minOccurs="0" default="true"/>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false"/>
    </xs:complexType>

    <xs:complexType name="hazelcast-cloud-client">
        <xs:all>
            <xs:element name="discovery-token" type="xs:string"/>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="required"/>
    </xs:complexType>

    <xs:simpleType name="parameterizedValueType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[$#]\{([^=^:]+)\}"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="parameterized-boolean">
        <xs:union memberTypes="xs:boolean parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="parameterized-unsigned-int">
        <xs:union memberTypes="xs:unsignedInt parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="parameterized-unsigned-long">
        <xs:union memberTypes="xs:unsignedLong parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="parameterized-double">
        <xs:union memberTypes="xs:double parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="parameterized-positive-integer">
        <xs:union memberTypes="xs:positiveInteger parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="parameterized-backup-count">
        <xs:union memberTypes="backup-count parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="metadata-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CREATE_ON_UPDATE"/>
            <xs:enumeration value="OFF"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="backup-count">
        <xs:restriction base="xs:byte">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="6"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="crdt-replica-count">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="2147483647"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="parameterized-non-negative-integer">
        <xs:union memberTypes="xs:nonNegativeInteger parameterizedValueType"/>
    </xs:simpleType>
    <xs:simpleType name="cp-group-size">
        <xs:restriction base="xs:unsignedInt">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="7"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="listeners">
        <xs:sequence>
            <xs:element name="listener" type="listener" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="client-security">
        <xs:sequence>
            <xs:choice minOccurs="0">
                <xs:element name="username-password" type="username-password"/>
                <xs:element name="credentials-factory" type="credentials-factory"/>
                <xs:element name="token" type="token"/>
                <xs:element name="kerberos" type="kerberos-identity"/>
                <xs:element name="credentials-ref" type="xs:string"/>
            </xs:choice>
            <xs:element name="realms" type="realms" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="proxy-factories">
        <xs:sequence>
            <xs:element name="proxy-factory" type="proxy-factory" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="proxy-factory">
        <xs:attribute name="service">
            <xs:simpleType>
                <xs:restriction base="non-space-string"/>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="class-name">
            <xs:simpleType>
                <xs:restriction base="non-space-string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="load-balancer">
        <xs:attribute name="type" use="required">
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="random"/>
                    <xs:enumeration value="round-robin"/>
                    <xs:enumeration value="custom"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>
    <xs:complexType name="near-cache-client">
        <xs:complexContent>
            <xs:extension base="near-cache">
                <xs:attribute name="name" use="required">
                    <xs:simpleType>
                        <xs:restriction base="xs:string"/>
                    </xs:simpleType>
                </xs:attribute>
                <xs:attribute name="local-update-policy" type="local-update-policy-enum" default="INVALIDATE"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="near-cache">
        <xs:sequence>
            <xs:element name="eviction" type="eviction" minOccurs="0"/>
            <xs:element name="preloader" type="preloader" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="in-memory-format" type="in-memory-format" default="BINARY"/>
        <xs:attribute name="serialize-keys" type="parameterized-boolean" default="false"/>
        <xs:attribute name="invalidate-on-change" type="parameterized-boolean" default="true"/>
        <xs:attribute name="time-to-live-seconds" type="xs:string" default="0"/>
        <xs:attribute name="max-idle-seconds" type="xs:string" default="0"/>
        <xs:attribute name="cache-local-entries" type="xs:string" default="false"/>
    </xs:complexType>

    <xs:complexType name="query-caches">
        <xs:sequence>
            <xs:element name="query-cache" type="query-cache" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="query-cache">
        <xs:all>
            <xs:element name="include-value" type="parameterized-boolean" minOccurs="0" default="true"/>
            <xs:element name="predicate" type="predicate"/>
            <xs:element name="entry-listeners" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This configuration lets you add listeners (listener classes) for the
                        map entries.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="entry-listener" type="entry-listener" minOccurs="0"
                                    maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" default="BINARY"/>
            <xs:element name="populate" type="parameterized-boolean" minOccurs="0" default="true"/>
            <xs:element name="coalesce" type="parameterized-boolean" minOccurs="0" default="false"/>
            <xs:element name="delay-seconds" type="parameterized-unsigned-int" minOccurs="0" default="0"/>
            <xs:element name="batch-size" type="parameterized-unsigned-int" minOccurs="0" default="1"/>
            <xs:element name="buffer-size" type="parameterized-unsigned-int" minOccurs="0" default="16"/>
            <xs:element name="eviction" type="eviction" minOccurs="0"/>
            <xs:element name="indexes" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This configuration lets you index the attributes and also order them.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="index" type="index" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="query-caches-client">
        <xs:sequence>
            <xs:element name="query-cache" type="query-cache-client" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="query-cache-client">
        <xs:all>
            <xs:element name="include-value" type="parameterized-boolean" minOccurs="0" default="true"/>
            <xs:element name="predicate" type="predicate"/>
            <xs:element name="entry-listeners" type="entry-listeners" minOccurs="0"/>
            <xs:element name="in-memory-format" type="in-memory-format" minOccurs="0" default="BINARY"/>
            <xs:element name="populate" type="parameterized-boolean" minOccurs="0" default="true"/>
            <xs:element name="coalesce" type="parameterized-boolean" minOccurs="0" default="false"/>
            <xs:element name="delay-seconds" type="parameterized-unsigned-int" minOccurs="0" default="0"/>
            <xs:element name="batch-size" type="parameterized-unsigned-int" minOccurs="0" default="1"/>
            <xs:element name="buffer-size" type="parameterized-unsigned-int" minOccurs="0" default="16"/>
            <xs:element name="eviction" type="eviction" minOccurs="0"/>
            <xs:element name="indexes" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This configuration lets you index the attributes and also order them.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="index" type="index" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="mapName" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="user-code-deployment-client">
        <xs:all>
            <xs:element name="jarPaths" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="jarPath" type="xs:string" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>

            <xs:element name="classNames" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="className" type="xs:string" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" use="required">
            <xs:annotation>
                <xs:documentation>
                    True to enable User Code Deployment on this client, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="flake-id-generator">
        <xs:attribute name="name" type="xs:string" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the Flake ID Generator.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="prefetchCount" default="100">
            <xs:annotation>
                <xs:documentation>
                    Sets how many IDs are pre-fetched on the background when one call to
                    FlakeIdGenerator.newId() is made. Value must be in the range 1..100,000, default
                    is 100.

                    This setting pertains only to newId() calls made on the member that configured it.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:int">
                    <xs:minInclusive value="1"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="prefetchValidityMillis" type="xs:long" default="600000">
            <xs:annotation>
                <xs:documentation>
                    Sets for how long the pre-fetched IDs can be used. If this time elapses, a new batch of IDs
                    will be fetched. Time unit is milliseconds, default is 600,000 (10 minutes).

                    The IDs contain timestamp component, which ensures rough global ordering of IDs. If an
                    ID is assigned to an object that was created much later, it will be much out of order. If you
                    don't care about ordering, set this value to 0.

                    This setting pertains only to newId() calls made on the member that configured it.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="epochStart" type="xs:long" default="1514764800000">
            <xs:annotation>
                <xs:documentation>
                    Sets the offset of timestamp component. Time unit is milliseconds, default is 1514764800000
                    (1.1.2018 0:00 UTC).
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="nodeIdOffset" type="xs:long" default="0">
            <xs:annotation>
                <xs:documentation>
                    Sets the offset that will be added to the node ID assigned to cluster member for this generator.
                    Might be useful in A/B deployment scenarios where you have cluster A which you want to upgrade.
                    You create cluster B and for some time both will generate IDs and you want to have them unique.
                    In this case, configure node ID offset for generators on cluster B.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bitsSequence" default="6">
            <xs:annotation>
                <xs:documentation>
                    Sets the bit-length of the sequence component, default is 6 bits.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:int">
                    <xs:minInclusive value="0"/>
                    <xs:maxInclusive value="63"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="bitsNodeId" default="16">
            <xs:annotation>
                <xs:documentation>
                    Sets the bit-length of node id component. Default value is 16 bits.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:int">
                    <xs:minInclusive value="0"/>
                    <xs:maxInclusive value="63"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="allowedFutureMillis" default="15000">
            <xs:annotation>
                <xs:documentation>
                    Sets how far to the future is the generator allowed to go to generate IDs without blocking, default is 15
                    seconds.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:long">
                    <xs:minInclusive value="0"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="statistics-enabled" type="parameterized-boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Enable/disable statistics.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="client-flake-id-generator">
        <xs:attribute name="name" type="xs:string" default="default">
            <xs:annotation>
                <xs:documentation>
                    Name of the Flake ID Generator.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="prefetchCount" default="100">
            <xs:annotation>
                <xs:documentation>
                    Sets how many IDs are pre-fetched on the background when one call to
                    FlakeIdGenerator.newId() is made. Value must be in the range 1..100,000, default
                    is 100.

                    This setting pertains only to newId() calls made on the member that configured it.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:int">
                    <xs:minInclusive value="1"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="prefetchValidityMillis" type="xs:long" default="600000">
            <xs:annotation>
                <xs:documentation>
                    Sets for how long the pre-fetched IDs can be used. If this time elapses, a new batch of IDs
                    will be fetched. Time unit is milliseconds, default is 600,000 (10 minutes).

                    The IDs contain timestamp component, which ensures rough global ordering of IDs. If an
                    ID is assigned to an object that was created much later, it will be much out of order. If you
                    don't care about ordering, set this value to 0.

                    This setting pertains only to newId() calls made on the member that configured it.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="client-reliable-topic">
        <xs:all>
            <xs:element name="topic-overload-policy" minOccurs="0" default="BLOCK">
                <xs:annotation>
                    <xs:documentation>
                        A policy to deal with an overloaded topic; so topic where there is no place to store new messages.
                        This policy can only be used in combination with the
                        com.hazelcast.core.HazelcastInstance#getReliableTopic(String).
                        The reliable topic uses a com.hazelcast.ringbuffer.Ringbuffer to
                        store the messages. A ringbuffer doesn't track where readers are, so
                        it has no concept of a slow consumers. This provides many advantages like
                        high performance reads, but it also gives the ability to the reader to
                        re-read the same message multiple times in case of an error.
                        A ringbuffer has a limited, fixed capacity. A fast producer may overwrite
                        old messages that are still being read by a slow consumer. To prevent
                        this, we may configure a time-to-live on the ringbuffer (see
                        com.hazelcast.config.RingbufferConfig#setTimeToLiveSeconds(int).
                        Once the time-to-live is configured, the TopicOverloadPolicy
                        controls how the publisher is going to deal with the situation that a
                        ringbuffer is full and the oldest item in the ringbuffer is not old
                        enough to get overwritten.
                        Keep in mind that this retention period (time-to-live) can keep messages
                        from being overwritten, even though all readers might have already completed reading.
                        Its default value is BLOCK. Available values are as follows:
                        - DISCARD_OLDEST:
                        Using this policy, a message that has not expired can be overwritten.
                        No matter the retention period set, the overwrite will just overwrite
                        the item.
                        This can be a problem for slow consumers because they were promised a
                        certain time window to process messages. But it will benefit producers
                        and fast consumers since they are able to continue. This policy sacrifices
                        the slow producer in favor of fast producers/consumers.
                        - DISCARD_NEWEST:
                        Message that was to be published is discarded.
                        - BLOCK:
                        The caller will wait until there is space in the Ringbuffer.
                        - ERROR:
                        The publish call fails immediately.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="non-space-string">
                        <xs:enumeration value="DISCARD_OLDEST"/>
                        <xs:enumeration value="DISCARD_NEWEST"/>
                        <xs:enumeration value="BLOCK"/>
                        <xs:enumeration value="ERROR"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="read-batch-size" type="xs:int" minOccurs="0" default="10">
                <xs:annotation>
                    <xs:documentation>
                        Sets the read batch size.
                        The ReliableTopic tries to read a batch of messages from the ringbuffer.
                        It will get at least one, but if there are more available, then it will
                        try to get more to increase throughput. The maximum read batch size can
                        be influenced using the read batch size.
                        Apart from influencing the number of messages to retrieve, the
                        readBatchSize also determines how many messages will be processed
                        by the thread running the MessageListener before it returns back
                        to the pool to look for other MessageListeners that need to be
                        processed. The problem with returning to the pool and looking for new work
                        is that interacting with an executor is quite expensive due to contention
                        on the work-queue. The more work that can be done without retuning to the
                        pool, the smaller the overhead.
                        If the readBatchSize is 10 and there are 50 messages available,
                        10 items are retrieved and processed consecutively before the thread goes
                        back to the pool and helps out with the processing of other messages.
                        If the readBatchSize is 10 and there are 2 items available,
                        2 items are retrieved and processed consecutively.
                        If the readBatchSize is an issue because a thread will be busy
                        too long with processing a single MessageListener and it can't
                        help out other MessageListeners, increase the size of the
                        threadpool so the other MessageListeners don't need to wait for
                        a thread, but can be processed in parallel.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required">
            <xs:annotation>
                <xs:documentation>
                    Name of the Reliable Topic.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="client-metrics">
        <xs:all>
            <xs:element name="jmx" type="metrics-jmx" minOccurs="0"/>
            <xs:element name="collection-frequency-seconds" type="xs:unsignedInt" default="5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the metrics collection frequency in seconds.
                        By default, metrics are collected every 5 seconds.
                        May be overridden by 'hazelcast.metrics.collection.frequency'
                        system property.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Master-switch for the metrics system. Controls whether
                    the metrics are collected and publishers are enabled.
                    May be overridden by 'hazelcast.metrics.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="predicate">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="type" use="required">
                    <xs:simpleType>
                        <xs:restriction base="non-space-string">
                            <xs:enumeration value="class-name"/>
                            <xs:enumeration value="sql"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="index">
        <xs:all>
            <xs:element name="attributes" type="index-attributes"/>
            <xs:element name="bitmap-index-options" type="bitmap-index-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name"/>
        <xs:attribute name="type" type="index-type" default="SORTED"/>
    </xs:complexType>
    <xs:complexType name="index-attributes">
        <xs:sequence>
            <xs:element name="attribute" type="xs:string" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="index-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="SORTED"/>
            <xs:enumeration value="HASH"/>
            <xs:enumeration value="BITMAP"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="bitmap-index-options">
        <xs:all>
            <xs:element name="unique-key" type="xs:string" default="__key" minOccurs="0"/>
            <xs:element name="unique-key-transformation" type="bitmap-index-unique-key-transformation"
                        default="OBJECT" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:simpleType name="bitmap-index-unique-key-transformation">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="OBJECT"/>
            <xs:enumeration value="LONG"/>
            <xs:enumeration value="RAW"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="entry-listeners">
        <xs:sequence>
            <xs:element name="entry-listener" type="entry-listener" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="listener-base">
        <xs:annotation>
            <xs:documentation>One of membership-listener, instance-listener or migration-listener
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="non-space-string"/>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="eviction-policy-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="LRU"/>
            <xs:enumeration value="LFU"/>
            <xs:enumeration value="RANDOM"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="eviction-policy">
        <xs:union memberTypes="eviction-policy-enum non-space-string"/>
    </xs:simpleType>

    <xs:simpleType name="in-memory-format-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="BINARY"/>
            <xs:enumeration value="OBJECT"/>
            <xs:enumeration value="NATIVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="in-memory-format">
        <xs:union memberTypes="in-memory-format-enum non-space-string"/>
    </xs:simpleType>

    <xs:simpleType name="local-update-policy-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="INVALIDATE"/>
            <xs:enumeration value="CACHE_ON_UPDATE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="topic-overload-policy-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="DISCARD_OLDEST"/>
            <xs:enumeration value="DISCARD_NEWEST"/>
            <xs:enumeration value="BLOCK"/>
            <xs:enumeration value="ERROR"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="topic-overload-policy">
        <xs:union memberTypes="topic-overload-policy-enum non-space-string"/>
    </xs:simpleType>

    <xs:simpleType name="time-unit">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NANOSECONDS"/>
            <xs:enumeration value="MICROSECONDS"/>
            <xs:enumeration value="MILLISECONDS"/>
            <xs:enumeration value="SECONDS"/>
            <xs:enumeration value="MINUTES"/>
            <xs:enumeration value="HOURS"/>
            <xs:enumeration value="DAYS"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="expiry-policy-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="ACCESSED"/>
            <xs:enumeration value="ETERNAL"/>
            <xs:enumeration value="MODIFIED"/>
            <xs:enumeration value="TOUCHED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="timed-expiry-policy-factory">
        <xs:attribute name="expiry-policy-type" type="expiry-policy-type" use="required"/>
        <xs:attribute name="duration-amount" type="xs:unsignedLong"/>
        <xs:attribute name="time-unit" type="time-unit"/>
    </xs:complexType>

    <xs:simpleType name="topology-changed-strategy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="CANCEL_RUNNING_OPERATION"/>
            <xs:enumeration value="DISCARD_AND_RESTART"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="max-size-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="ENTRY_COUNT"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_PERCENTAGE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_PERCENTAGE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="max-size-policy-map">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="PER_NODE"/>
            <xs:enumeration value="PER_PARTITION"/>
            <xs:enumeration value="USED_HEAP_SIZE"/>
            <xs:enumeration value="USED_HEAP_PERCENTAGE"/>
            <xs:enumeration value="FREE_HEAP_SIZE"/>
            <xs:enumeration value="FREE_HEAP_PERCENTAGE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="USED_NATIVE_MEMORY_PERCENTAGE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_SIZE"/>
            <xs:enumeration value="FREE_NATIVE_MEMORY_PERCENTAGE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="scheduled-executor-capacity-policy">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="PER_NODE"/>
            <xs:enumeration value="PER_PARTITION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="eviction">
        <xs:attribute name="size" type="xs:nonNegativeInteger" default="10000"/>
        <xs:attribute name="max-size-policy" type="max-size-policy" default="ENTRY_COUNT"/>
        <xs:attribute name="eviction-policy" type="eviction-policy" default="LRU"/>
        <xs:attribute name="comparator-class-name" type="xs:string"/>
        <xs:attribute name="comparator-bean" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="eviction-map">
        <xs:attribute name="size" type="xs:nonNegativeInteger" default="0"/>
        <xs:attribute name="max-size-policy" type="max-size-policy-map" default="PER_NODE"/>
        <xs:attribute name="eviction-policy" type="eviction-policy" default="LRU"/>
        <xs:attribute name="comparator-class-name" type="xs:string"/>
        <xs:attribute name="comparator-bean" type="xs:string"/>
    </xs:complexType>

    <xs:complexType name="preloader">
        <xs:attribute name="enabled" type="parameterized-boolean" default="false"/>
        <xs:attribute name="directory" type="xs:string" default=""/>
        <xs:attribute name="store-initial-delay-seconds" type="xs:positiveInteger" default="600"/>
        <xs:attribute name="store-interval-seconds" type="xs:positiveInteger" default="600"/>
    </xs:complexType>

    <xs:complexType name="wan-replication-ref">
        <xs:all>
            <xs:element name="filters" type="wan-replication-ref-filters" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the list of class names implementing the CacheWanEventFilter or
                        MapWanEventFilter for filtering outbound WAN replication events.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" use="required" type="xs:string">
            <xs:annotation>
                <xs:documentation>
                    Name of the wan-replication configuration. IMap or ICache instance uses this wan-replication config.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="merge-policy-class-name" type="xs:string"
                      default="com.hazelcast.spi.merge.PassThroughMergePolicy">
            <xs:annotation>
                <xs:documentation>
                    Sets the merge policy sent to the WAN replication target to merge
                    replicated entries with existing target entries.
                    The default merge policy is com.hazelcast.spi.merge.PassThroughMergePolicy.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="republishing-enabled" type="parameterized-boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    When enabled, an incoming event to a member is forwarded to the target cluster of that member.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="native-memory">
        <xs:all>
            <xs:element name="size" type="memory-size" minOccurs="0"/>
            <xs:element name="persistent-memory" type="persistent-memory" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="min-block-size" type="xs:positiveInteger"/>
        <xs:attribute name="page-size" type="xs:positiveInteger"/>
        <xs:attribute name="metadata-space-percentage">
            <xs:simpleType>
                <xs:restriction base="xs:decimal">
                    <xs:totalDigits value="3"/>
                    <xs:fractionDigits value="1"/>
                    <xs:minInclusive value="5"/>
                    <xs:maxInclusive value="95"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="allocator-type" default="POOLED" type="memory-allocator-type"/>
        <xs:attribute name="enabled" default="true" type="parameterized-boolean"/>
        <xs:attribute name="persistent-memory-directory" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="memory-size">
        <xs:attribute name="value" type="parameterized-non-negative-integer" default="128"/>
        <xs:attribute name="unit" type="memory-unit" default="MEGABYTES"/>
    </xs:complexType>
    <xs:simpleType name="memory-unit">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="BYTES"/>
            <xs:enumeration value="KILOBYTES"/>
            <xs:enumeration value="MEGABYTES"/>
            <xs:enumeration value="GIGABYTES"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="memory-allocator-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="STANDARD"/>
            <xs:enumeration value="POOLED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="persistent-memory">
        <xs:annotation>
            <xs:documentation>
                Configuration for persistent memory (e.g. Intel Optane) devices.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="directories" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        List of directories where the persistent memory
                        is mounted to. Requires the mode attribute of persistent-memory
                        to be MOUNTED (default).

                        If the specified directories are not unique either in the directories
                        themselves or in the NUMA nodes specified for them,
                        the configuration is treated as invalid. Setting the NUMA
                        node on the subset of the configured directories while leaving
                        not set on others also makes the configuration invalid.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice maxOccurs="unbounded">
                        <xs:element name="directory" type="persistent-memory-directory"/>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" default="false" type="xs:boolean">
            <xs:annotation>
                <xs:documentation>
                    Sets if using persistent memory as Hazelcast native memory is enabled.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="mode" default="MOUNTED" type="persistent-memory-mode">
            <xs:annotation>
                <xs:documentation>
                    Sets the operational mode of the persistent memory configured
                    on the machine.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:simpleType name="persistent-memory-mode">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="MOUNTED">
                <xs:annotation>
                    <xs:documentation>
                        The persistent memory is mounted into the file system (also known as FS DAX mode).
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SYSTEM_MEMORY">
                <xs:annotation>
                    <xs:documentation>
                        The persistent memory is onlined as system memory (also known as KMEM DAX mode).
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="persistent-memory-directory">
        <xs:annotation>
            <xs:documentation>
                The directory where persistent memory is mounted to.

                If the specified directory id not unique either in the
                directory itself or in the NUMA node specified, the
                configuration will be treated as invalid. Setting the NUMA
                node on the subset of the configured directories while leaving
                not set on others also results in invalid configuration.
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="numa-node" type="xs:int" default="-1">
                    <xs:annotation>
                        <xs:documentation>
                            The NUMA node that the persistent memory mounted
                            to the given directory is attached to.

                            NUMA nodes have to be unique across the entire
                            persistent memory configuration, otherwise the
                            configuration will be treated as invalid. Similarly,
                            setting the NUMA node on the subset of the configured
                            directories while leaving not set on others also
                            results in invalid configuration.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="split-brain-protection">
        <xs:all>
            <xs:element name="minimum-cluster-size" type="minimum-cluster-size" minOccurs="0"/>
            <xs:element name="protect-on" type="protect-on" minOccurs="0"/>
            <xs:element name="function-class-name" type="xs:string" minOccurs="0"/>
            <xs:element name="listeners" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="listener" type="split-brain-protection-listener" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element ref="choice-of-split-brain-protection-function" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="enabled" type="parameterized-boolean" use="required"/>
        <xs:attribute name="name" use="required">
            <xs:simpleType>
                <xs:restriction base="xs:string"/>
            </xs:simpleType>
        </xs:attribute>
    </xs:complexType>

    <xs:element name="choice-of-split-brain-protection-function" abstract="true"/>

    <xs:element name="probabilistic-split-brain-protection" substitutionGroup="choice-of-split-brain-protection-function">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>
                    A probabilistic split brain protection function based on Phi Accrual failure detector. See
                    com.hazelcast.internal.cluster.fd.PhiAccrualClusterFailureDetector for implementation
                    details. Configuration:
                    <br/>
                    - <code>acceptable-heartbeat-pause</code>: duration in milliseconds corresponding to number
                    of potentially lost/delayed heartbeats that will be accepted before considering it to be an anomaly.
                    This margin is important to be able to survive sudden, occasional, pauses in heartbeat arrivals,
                    due to for example garbage collection or network drops.
                    <br/>
                    - <code>threshold</code>: threshold for suspicion level. A low threshold is prone to generate
                    many wrong suspicions but ensures a quick detection in the event of a real crash. Conversely, a high
                    threshold generates fewer mistakes but needs more time to detect actual crashes.
                    <br/>
                    - <code>max-sample-size</code>: number of samples to use for calculation of mean and standard
                    deviation of inter-arrival times.
                    <br/>
                    - <code>first-heartbeat-estimate</code>: bootstrap the stats with heartbeats that corresponds to
                    this duration in milliseconds, with a rather high standard deviation (since environment is unknown
                    in the beginning)
                    <br/>
                    - <code>min-std-deviation</code>: minimum standard deviation (in milliseconds) to use for the normal
                    distribution used when calculating phi. Too low standard deviation might result in too much
                    sensitivity for sudden, but normal, deviations in heartbeat inter arrival times.
                </xs:documentation>
            </xs:annotation>
            <xs:attribute name="acceptable-heartbeat-pause-millis" type="parameterized-unsigned-int" default="60000"/>
            <xs:attribute name="suspicion-threshold" type="parameterized-double" default="10"/>
            <xs:attribute name="max-sample-size" type="parameterized-unsigned-int" default="200"/>
            <xs:attribute name="min-std-deviation-millis" type="parameterized-unsigned-long" default="100"/>
            <xs:attribute name="heartbeat-interval-millis" type="parameterized-unsigned-long" default="5000"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="recently-active-split-brain-protection" substitutionGroup="choice-of-split-brain-protection-function">
        <xs:complexType>
            <xs:annotation>
                <xs:documentation>
                    A split brain protection function that keeps track of the last heartbeat timestamp per each member.
                    For a member to be considered live (for the purpose to conclude whether the minimum cluster size
                    property is satisfied), a heartbeat must have been received at most
                    <code>heartbeat-tolerance</code>
                    milliseconds before current time.
                </xs:documentation>
            </xs:annotation>
            <xs:attribute name="heartbeat-tolerance-millis" type="parameterized-unsigned-int" default="5000"/>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="protect-on">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="READ"/>
            <xs:enumeration value="WRITE"/>
            <xs:enumeration value="READ_WRITE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="minimum-cluster-size">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="2"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="split-brain-protection-listener">
        <xs:complexContent>
            <xs:extension base="listener">
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="wan-replication-ref-filters">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="filter-impl" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="wan-queue-full-behavior-format-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="DISCARD_AFTER_MUTATION"/>
            <xs:enumeration value="THROW_EXCEPTION"/>
            <xs:enumeration value="THROW_EXCEPTION_ONLY_IF_REPLICATION_ACTIVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="initial-publisher-state-format-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="REPLICATING"/>
            <xs:enumeration value="PAUSED"/>
            <xs:enumeration value="STOPPED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="consistency-check-strategy-format-enum">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="MERKLE_TREES"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="wan-queue-full-behavior">
        <xs:union memberTypes="wan-queue-full-behavior-format-enum non-space-string"/>
    </xs:simpleType>

    <xs:simpleType name="wan-acknowledge-type">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="ACK_ON_OPERATION_COMPLETE"/>
            <xs:enumeration value="ACK_ON_RECEIPT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="initial-publisher-state">
        <xs:union memberTypes="initial-publisher-state-format-enum non-space-string"/>
    </xs:simpleType>

    <xs:simpleType name="consistency-check-strategy">
        <xs:union memberTypes="consistency-check-strategy-format-enum non-space-string"/>
    </xs:simpleType>

    <xs:complexType name="crdt-replication">
        <xs:attribute name="replication-period-millis" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    The period between two replications of CRDT states in milliseconds.
                    A lower value will increase the speed at which changes are disseminated
                    to other cluster members at the expense of burst-like behaviour - less
                    updates will be batched together in one replication message and one
                    update to a CRDT may cause a sudden burst of replication messages in a
                    short time interval.
                    The value must be a positive non-null integer.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="max-concurrent-replication-targets" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    The maximum number of target members that we replicate the CRDT states
                    to in one period. A higher count will lead to states being disseminated
                    more rapidly at the expense of burst-like behaviour - one update to a
                    CRDT will lead to a sudden burst in the number of replication messages
                    in a short time interval.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:element name="secure-store-substitution-group" abstract="true"/>

    <xs:complexType name="keystore">
        <xs:all>
            <xs:element name="path" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The path of the KeyStore file.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="xs:string" minOccurs="0" default="PKCS12">
                <xs:annotation>
                    <xs:documentation>
                        The type of the KeyStore (PKCS12, JCEKS, etc.).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="password" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The password to access the KeyStore (JCEKS, PKCS12, etc.).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="current-key-alias" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The alias for the current encryption key entry (optional).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="polling-interval" type="xs:int" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The polling interval for checking for changes in the KeyStore.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:element name="keystore" type="keystore" substitutionGroup="secure-store-substitution-group">
        <xs:annotation>
            <xs:documentation>
                Java KeyStore Secure Store configuration.
            </xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="vault">
        <xs:all>
            <xs:element name="address" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The address of the Vault REST API server.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="secret-path" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The Vault secret path.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="token" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The Vault access token.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="polling-interval" type="xs:int" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The polling interval for checking for changes in Vault.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ssl" type="ssl" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        SSL/TLS configuration for HTTPS connections.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:element name="vault" type="vault" substitutionGroup="secure-store-substitution-group">
        <xs:annotation>
            <xs:documentation>
                HashiCorp Vault Secure Store configuration.
            </xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:complexType name="secure-store">
        <xs:sequence>
            <xs:element ref="secure-store-substitution-group" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="encryption-at-rest">
        <xs:annotation>
            <xs:documentation>
                Configuration for hot restart (symmetric) encryption at rest. Encryption is based
                on Java Cryptography Architecture.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="algorithm" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as AES/CBC/PKCS5Padding, DES/ECB/PKCS5Padding, Blowfish,
                        or DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="salt" type="xs:string" minOccurs="0" default="thesalt">
                <xs:annotation>
                    <xs:documentation>
                        Salt value to use when generating the secret key.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="key-size" type="xs:int" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        The key size in bits used when generating encryption keys (optional).
                        The default value of 0 implies falling back to the cipher-specific
                        default key size.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="secure-store" type="secure-store" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The Secure Store configuration. Not required when encryption at rest is disabled.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True to enable symmetric encryption, false to disable.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="hot-restart-persistence">
        <xs:sequence>
            <xs:element name="base-dir" type="xs:string" minOccurs="0" default="hot-restart">
                <xs:annotation>
                    <xs:documentation>
                        Base directory for all hot-restart data. Can be an absolute or relative path to the node startup
                        directory.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="backup-dir" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Base directory for hot backups. Each new backup will be created in a separate directory inside this one.
                        Can be an absolute or relative path to the node startup directory.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="encryption-at-rest" type="encryption-at-rest" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Specifies parameters for encryption of Hot Restart data. This includes the encryption algorithm
                        to be used (such as AES, DESede etc.) and the Secure Store configuration for retrieving the
                        encryption keys.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="enabled" type="parameterized-boolean">
            <xs:annotation>
                <xs:documentation>
                    True to enable hot-restart, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="validation-timeout-seconds" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    Validation timeout for hot-restart process, includes validating
                    cluster members expected to join and partition table on all cluster.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="data-load-timeout-seconds" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    Data load timeout for hot-restart process,
                    all members in the cluster should complete restoring their local data
                    before this timeout.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="cluster-data-recovery-policy" default="FULL_RECOVERY_ONLY">
            <xs:annotation>
                <xs:documentation>
                    Specifies the policy that will be respected during hot restart cluster start. Valid values are :
                    FULL_RECOVERY_ONLY : Starts the cluster only when all expected nodes are present and correct.
                    Otherwise, it fails.
                    PARTIAL_RECOVERY_MOST_RECENT : Starts the cluster with the members which have most up-to-date
                    partition table and successfully restored their data. All other members will leave the cluster and
                    force-start themselves. If no member restores its data successfully, cluster start fails.
                    PARTIAL_RECOVERY_MOST_COMPLETE : Starts the cluster with the largest group of members which have the
                    same partition table version and successfully restored their data. All other members will leave the
                    cluster and force-start themselves. If no member restores its data successfully, cluster start fails.
                </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:restriction base="non-space-string">
                    <xs:enumeration value="FULL_RECOVERY_ONLY"/>
                    <xs:enumeration value="PARTIAL_RECOVERY_MOST_RECENT"/>
                    <xs:enumeration value="PARTIAL_RECOVERY_MOST_COMPLETE"/>
                </xs:restriction>
            </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="auto-remove-stale-data" type="parameterized-boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Sets whether or not automatically removal of stale Hot Restart data is enabled.
                    When a member terminates or crashes when cluster state is ACTIVE, remaining members
                    redistributes data among themselves and data persisted on terminated member's storage becomes
                    stale. That terminated member cannot rejoin the cluster without removing Hot Restart data.
                    When auto-removal of stale Hot Restart data is enabled, while restarting that member,
                    Hot Restart data is automatically removed and it joins the cluster as a completely new member.
                    Otherwise, Hot Restart data should be removed manually.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="event-journal">
        <xs:annotation>
            <xs:documentation>
                Configuration for an event journal. The event journal keeps events related
                to a specific partition and data structure. For instance, it could keep
                map add, update, remove, merge events along with the key, old value, new value and so on.
            </xs:documentation>
        </xs:annotation>
        <xs:attribute name="enabled" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if the event journal is enabled, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="capacity" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    Number of items in the event journal. If no time-to-live-seconds
                    is set, the size will always be equal to capacity after the event
                    journal has been filled. This is because no items are getting retired.
                    The default value is 10000.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="time-to-live-seconds" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    Maximum number of seconds for each entry to stay in the event journal.
                    Entries that are older than &lt;time-to-live-seconds&gt; are evicted from the journal.
                    Any integer between 0 and Integer.MAX_VALUE. 0 means infinite. Default is 0.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="hot-restart">
        <xs:attribute name="fsync" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if disk write should be followed by an fsync() system call,
                    false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="enabled" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if hot-restart is enabled, false otherwise
                    Only available on Hazelcast Enterprise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="merkle-tree">
        <xs:annotation>
            <xs:documentation>
                Configuration for a merkle tree.
                The merkle tree is a data structure used for efficient comparison of the
                difference in the contents of large data structures. The precision of
                such a comparison mechanism is defined by the depth of the merkle tree.
                A larger depth means that a data synchronization mechanism will be able
                to pinpoint a smaller subset of the data structure contents in which a
                change occurred. This causes the synchronization mechanism to be more
                efficient. On the other hand, a larger tree depth means the merkle tree
                will consume more memory.
                A smaller depth means the data synchronization mechanism will have to
                transfer larger chunks of the data structure in which a possible change
                happened. On the other hand, a shallower tree consumes less memory.
                The depth must be between 2 and 27 (exclusive).
                As the comparison mechanism is iterative, a larger depth will also prolong
                the duration of the comparison mechanism. Care must be taken to not have
                large tree depths if the latency of the comparison operation is high.
                The default depth is 10.
                See https://en.wikipedia.org/wiki/Merkle_tree.
            </xs:documentation>
        </xs:annotation>
        <xs:attribute name="enabled" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    True if the merkle tree is enabled, false otherwise.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="depth" type="parameterized-unsigned-int">
            <xs:annotation>
                <xs:documentation>
                    The depth of the merkle tree.
                    A larger depth means that a data synchronization mechanism will be able
                    to pinpoint a smaller subset of the data structure contents in which a
                    change occurred. This causes the synchronization mechanism to be more
                    efficient. On the other hand, a larger tree depth means the merkle tree
                    will consume more memory.
                    A smaller depth means the data synchronization mechanism will have to
                    transfer larger chunks of the data structure in which a possible change
                    happened. On the other hand, a shallower tree consumes less memory.
                    The depth must be between 2 and 27 (exclusive). The default depth is 10.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="connection-strategy">
        <xs:choice>
            <xs:element name="connection-retry" type="connection-retry" minOccurs="0"/>
        </xs:choice>
        <xs:attribute name="async-start" type="xs:boolean" default="false"/>
        <xs:attribute name="reconnect-mode" type="reconnect-mode" default="ON"/>
    </xs:complexType>

    <xs:complexType name="connection-retry">
        <xs:all>
            <xs:element name="initial-backoff-millis" type="xs:int" minOccurs="0" default="1000"/>
            <xs:element name="max-backoff-millis" type="xs:int" minOccurs="0" default="30000"/>
            <xs:element name="multiplier" type="xs:double" minOccurs="0" default="1.05"/>
            <xs:element name="cluster-connect-timeout-millis" type="xs:long" minOccurs="0" default="-1"/>
            <xs:element name="jitter" type="xs:double" minOccurs="0" default="0"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="reconnect-mode">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="OFF"/>
            <xs:enumeration value="ON"/>
            <xs:enumeration value="ASYNC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:attributeGroup name="class-or-bean-name">
        <xs:attribute name="class-name" type="non-space-string"/>
        <xs:attribute name="implementation" type="non-space-string"/>
    </xs:attributeGroup>

    <xs:complexType name="advanced-network">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="join" type="join" minOccurs="0"/>
            <xs:element name="failure-detector" type="failure-detector" minOccurs="0"/>
            <xs:element name="member-address-provider" type="member-address-provider" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        This configuration is not intended to provide addresses of other cluster members with
                        which the hazelcast instance will form a cluster. This is an SPI for advanced use in
                        cases where the DefaultAddressPicker does not pick suitable addresses to bind to
                        and publish to other cluster members. For instance, this could allow easier
                        deployment in some cases when running on Docker, AWS or other cloud environments.
                        That said, if you are just starting with Hazelcast, you will probably want to
                        set the member addresses by using the tcp-ip or multicast configuration
                        or adding a discovery strategy.
                        Member address provider allows to plug in own strategy to customize:
                        1. What address Hazelcast will bind to
                        2. What address Hazelcast will advertise to other members on which they can bind to

                        In most environments you don't need to customize this and the default strategy will work just
                        fine. However in some cloud environments the default strategy does not make the right choice and
                        the member address provider delegates the process of address picking to external code.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="wan-endpoint-config" type="endpoint-config" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="wan-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="member-server-socket-endpoint-config" type="server-socket-endpoint-config"/>
            <xs:element name="client-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0"/>
            <xs:element name="rest-server-socket-endpoint-config" type="rest-server-socket-endpoint-config" minOccurs="0"/>
            <xs:element name="memcache-server-socket-endpoint-config" type="server-socket-endpoint-config" minOccurs="0"/>
        </xs:choice>
        <xs:attribute name="enabled" type="parameterized-boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    Indicates whether the advanced network configuration is enabled or not. Default is false.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="endpoint-config">
        <xs:all>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0"/>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="ssl" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as DES/ECB/PKCS5Padding, PBEWithMD5AndDES, AES/CBC/PKCS5Padding,
                        Blowfish, DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="socket-options" type="endpoint-socket-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" default=""/>
    </xs:complexType>

    <xs:complexType name="server-socket-endpoint-config">
        <xs:all>
            <xs:element name="reuse-address" type="parameterized-boolean" minOccurs="0" default="false"/>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0"/>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="ssl" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as DES/ECB/PKCS5Padding, PBEWithMD5AndDES, AES/CBC/PKCS5Padding,
                        Blowfish, DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="socket-options" type="endpoint-socket-options" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string"/>
        <xs:attribute name="public-address" type="xs:string"/>
        <xs:attribute name="port" type="xs:string" use="required"/>
        <xs:attribute name="port-auto-increment" type="xs:string" default="true"/>
        <xs:attribute name="port-count" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="rest-server-socket-endpoint-config">
        <xs:all>
            <xs:element name="reuse-address" type="parameterized-boolean" minOccurs="0" default="false"/>
            <xs:element name="outbound-ports" type="outbound-ports" minOccurs="0"/>
            <xs:element name="interfaces" type="interfaces" minOccurs="0"/>
            <xs:element name="ssl" type="ssl" minOccurs="0"/>
            <xs:element name="socket-interceptor" type="socket-interceptor" minOccurs="0"/>
            <xs:element name="symmetric-encryption" type="symmetric-encryption" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Encryption algorithm such as DES/ECB/PKCS5Padding, PBEWithMD5AndDES, AES/CBC/PKCS5Padding,
                        Blowfish, DESede.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="socket-options" type="endpoint-socket-options" minOccurs="0"/>
            <xs:element name="endpoint-groups" type="endpoint-groups" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables named REST endpoint group.
                        If a group is not listed within the rest-api configuration, then it's 'enabledByDefault' flag is used
                        to control the behavior of the group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="name" type="xs:string" default=""/>
        <xs:attribute name="public-address" type="xs:string"/>
        <xs:attribute name="port" type="xs:string" use="required"/>
        <xs:attribute name="port-auto-increment" type="xs:string" default="true"/>
        <xs:attribute name="port-count" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="endpoint-socket-options">
        <xs:all>
            <xs:element name="buffer-direct" type="parameterized-boolean" minOccurs="0"/>
            <xs:element name="tcp-no-delay" type="parameterized-boolean" minOccurs="0"/>
            <xs:element name="keep-alive" type="parameterized-boolean" minOccurs="0"/>
            <xs:element name="connect-timeout-seconds" type="parameterized-non-negative-integer" minOccurs="0"/>
            <xs:element name="send-buffer-size-kb" type="parameterized-positive-integer" minOccurs="0"/>
            <xs:element name="receive-buffer-size-kb" type="parameterized-positive-integer" minOccurs="0"/>
            <xs:element name="linger-seconds" type="parameterized-non-negative-integer" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="endpoint-groups">
        <xs:choice maxOccurs="unbounded">
            <xs:element name="endpoint-group" type="endpoint-group">
                <xs:annotation>
                    <xs:documentation>
                        Enables or disables named REST endpoint group.
                        If a group is not listed within the rest-api configuration, then it's 'enabledByDefault' flag is used
                        to control the behavior of the group.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="cp-subsystem">
        <xs:all>
            <xs:element name="cp-member-count" type="xs:unsignedInt" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of CP members to initialize CP Subsystem. It is 0 by default,
                        meaning that CP Subsystem is disabled. CP Subsystem is enabled when
                        a positive value is set. After CP Subsystem is initialized successfully,
                        more CP members can be added at run-time and the number of active CP
                        members can go beyond the configured CP member count. The number of CP
                        members can be smaller than total member count of the Hazelcast cluster.
                        For instance, you can run 5 CP members in a Hazelcast cluster of
                        20 members. If set, must be greater than or equal to group-size.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="group-size" type="cp-group-size" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of CP members to form CP groups. If set, it must be an odd
                        number between 3 and 7.
                        Otherwise, cp-member-count is respected while forming CP groups.
                        If set, must be smaller than or equal to cp-member-count.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="session-time-to-live-seconds" type="xs:unsignedInt" minOccurs="0" default="60">
                <xs:annotation>
                    <xs:documentation>
                        Duration for a CP session to be kept alive after the last received
                        session heartbeat. A CP session is closed if no session heartbeat is
                        received during this duration. Session TTL must be decided wisely. If
                        a very small value is set, a CP session can be closed prematurely if
                        its owner Hazelcast instance temporarily loses connectivity to CP
                        Subsystem because of a network partition or a GC pause. In such an
                        occasion, all CP resources of this Hazelcast instance, such as
                        FencedLock or ISemaphore, are released. On the other
                        hand, if a very large value is set, CP resources can remain assigned to
                        an actually crashed Hazelcast instance for too long and liveliness
                        problems can occur. CP Subsystem offers an API in
                        CPSessionManagementService to deal with liveliness issues
                        related to CP sessions. In order to prevent premature session expires,
                        session TTL configuration can be set a relatively large value and
                        CPSessionManagementService#forceCloseSession(String, long)
                        can be manually called to close CP session of a crashed Hazelcast
                        instance.
                        Must be greater than session-heartbeat-interval-seconds, and
                        smaller than or equal to missing-cp-member-auto-removal-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="session-heartbeat-interval-seconds" type="xs:unsignedInt" minOccurs="0" default="5">
                <xs:annotation>
                    <xs:documentation>
                        Interval for the periodically-committed CP session heartbeats.
                        A CP session is started on a CP group with the first session-based
                        request of a Hazelcast instance. After that moment, heartbeats are
                        periodically committed to the CP group.
                        Must be smaller than session-time-to-live-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="missing-cp-member-auto-removal-seconds" type="xs:unsignedInt" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Duration to wait before automatically removing a missing CP member from
                        CP Subsystem. When a CP member leaves the Hazelcast cluster, it is not
                        automatically removed from CP Subsystem, since it could be still alive
                        and left the cluster because of a network problem. On the other hand,
                        if a missing CP member actually crashed, it creates a danger for CP
                        groups, because it is still part of majority calculations. This
                        situation could lead to losing majority of CP groups if multiple CP
                        members leave the cluster over time.
                        With the default configuration, missing CP members are automatically
                        removed from CP Subsystem after 4 hours. This feature is very useful
                        in terms of fault tolerance when CP member count is also configured
                        to be larger than group size. In this case, a missing CP member is
                        safely replaced in its CP groups with other available CP members
                        in CP Subsystem. This configuration also implies that no network
                        partition is expected to be longer than the configured duration.
                        If a missing CP member comes back alive after it is removed from CP
                        Subsystem with this feature, that CP member must be terminated manually.
                        Must be greater than or equal to session-time-to-live-seconds.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fail-on-indeterminate-operation-state" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Offers a choice between at-least-once and at-most-once execution
                        of operations on top of the Raft consensus algorithm. It is disabled by
                        default and offers at-least-once execution guarantee. If enabled, it
                        switches to at-most-once execution guarantee. When you invoke an API
                        method on a CP data structure proxy, it sends an internal operation
                        to the corresponding CP group. After this operation is committed on
                        the majority of this CP group by the Raft leader node, it sends
                        a response for the public API call. If a failure causes loss of
                        the response, then the calling side cannot determine if the operation is
                        committed on the CP group or not. In this case, if this configuration is
                        disabled, the operation is replicated again to the CP group, and hence
                        could be committed multiple times. If it is enabled, the public API call
                        fails with com.hazelcast.core.IndeterminateOperationStateException.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="persistence-enabled" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Flag to denote whether or not CP Subsystem Persistence is enabled.
                        If enabled, CP members persist their local CP data to stable storage and
                        can recover from crashes.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="base-dir" type="xs:string" minOccurs="0" default="cp-data">
                <xs:annotation>
                    <xs:documentation>
                        Base directory to store all CP data when persistence-enabled
                        is true. This directory can be shared between multiple CP members.
                        Each CP member creates a unique directory for itself under the base
                        directory. This is especially useful for cloud environments where CP
                        members generally use a shared filesystem.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data-load-timeout-seconds" type="xs:unsignedInt" minOccurs="0" default="120">
                <xs:annotation>
                    <xs:documentation>
                        Timeout duration for CP members to restore their data from disk.
                        A CP member fails its startup if it cannot complete its CP data restore
                        process in the configured duration.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="raft-algorithm" type="raft-algorithm" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Contains configuration options for Hazelcast's Raft consensus algorithm implementation
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="semaphores" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Configurations for CP semaphore instances
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="semaphore" type="semaphore" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="locks" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Configurations for FencedLock instances
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="fenced-lock" type="fenced-lock" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="raft-algorithm">
        <xs:all>
            <xs:element name="leader-election-timeout-in-millis" type="xs:unsignedInt" minOccurs="0" default="2000">
                <xs:annotation>
                    <xs:documentation>
                        Leader election timeout in milliseconds. If a candidate cannot win
                        majority of the votes in time, a new leader election round is initiated.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="leader-heartbeat-period-in-millis" type="xs:unsignedInt" minOccurs="0" default="5000">
                <xs:annotation>
                    <xs:documentation>
                        Duration in milliseconds for a Raft leader node to send periodic
                        heartbeat messages to its followers in order to denote its liveliness.
                        Periodic heartbeat messages are actually append entries requests and
                        can contain log entries for the lagging followers. If a too small value
                        is set, heartbeat messages are sent from Raft leaders to followers too
                        frequently and it can cause an unnecessary usage of CPU and network.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="max-missed-leader-heartbeat-count" type="xs:unsignedInt" minOccurs="0" default="5">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of missed Raft leader heartbeats for a follower to
                        trigger a new leader election round. For instance, if
                        leader-heartbeat-period-in-millis is 1 second and this value is set
                        to 5, then a follower triggers a new leader election round if 5 seconds
                        pass after the last heartbeat message of the current Raft leader node.
                        If this duration is too small, new leader election rounds can be
                        triggered unnecessarily if the current Raft leader temporarily slows
                        down or a network congestion occurs. If it is too large, it takes longer
                        to detect failures of Raft leaders.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="append-request-max-entry-count" type="xs:unsignedInt" minOccurs="0" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of Raft log entries that can be sent as a batch
                        in a single append entries request. In Hazelcast's Raft consensus
                        algorithm implementation, a Raft leader maintains a separate replication
                        pipeline for each follower. It sends a new batch of Raft log entries to
                        a follower after the follower acknowledges the last append entries
                        request sent by the leader.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="commit-index-advance-count-to-snapshot" type="xs:unsignedInt" minOccurs="0" default="10000">
                <xs:annotation>
                    <xs:documentation>
                        Number of new commits to initiate a new snapshot after the last snapshot
                        taken by the local Raft node. This value must be configured wisely as it
                        effects performance of the system in multiple ways. If a small value is
                        set, it means that snapshots are taken too frequently and Raft nodes keep
                        a very short Raft log. If snapshots are large and CP Subsystem
                        Persistence is enabled, this can create an unnecessary overhead on IO
                        performance. Moreover, a Raft leader can send too many snapshots to
                        followers and this can create an unnecessary overhead on network.
                        On the other hand, if a very large value is set, it can create a memory
                        overhead since Raft log entries are going to be kept in memory until
                        the next snapshot.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="uncommitted-entry-count-to-reject-new-appends" type="xs:unsignedInt" minOccurs="0" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of uncommitted log entries in the leader's Raft log
                        before temporarily rejecting new requests of callers. Since Raft leaders
                        send log entries to followers in batches, they accumulate incoming
                        requests in order to improve the throughput. You can configure this
                        field by considering your degree of concurrency in your callers.
                        For instance, if you have at most 1000 threads sending requests to
                        a Raft leader, you can set this field to 1000 so that callers do not
                        get retry responses unnecessarily.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="append-request-backoff-timeout-in-millis" type="xs:unsignedInt" minOccurs="0" default="100">
                <xs:annotation>
                    <xs:documentation>
                        Timeout duration in milliseconds to apply backoff on append entries
                        requests. After a Raft leader sends an append entries request to
                        a follower, it will not send a subsequent append entries request either
                        until the follower responds or this timeout occurs. Backoff durations
                        are increased exponentially if followers remain unresponsive.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>

        </xs:all>
    </xs:complexType>

    <xs:complexType name="semaphore">
        <xs:all>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Name of the CP semaphore
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="jdk-compatible" type="xs:boolean" minOccurs="0" default="false">
                <xs:annotation>
                    <xs:documentation>
                        Enables / disables JDK compatibility of CP ISemaphore.
                        When it is JDK compatible, just as in the Semaphore#release()
                        method, a permit can be released without acquiring it first, because
                        acquired permits are not bound to threads. However, there is no
                        auto-cleanup mechanism for acquired permits upon Hazelcast
                        server / client failures. If a permit holder fails, its permits must be
                        released manually. When JDK compatibility is disabled,
                        a HazelcastInstance must acquire permits before releasing them
                        and it cannot release a permit that it has not acquired. It means, you
                        can acquire a permit from one thread and release it from another thread
                        using the same HazelcastInstance, but not different
                        HazelcastInstances. In this mode, acquired permits are
                        automatically released upon failure of the holder
                        HazelcastInstance. So there is a minor behavioral difference
                        to the Semaphore#release() method.
                        JDK compatibility is disabled by default.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="initial-permits" type="xs:unsignedInt" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Number of permits to initialize the Semaphore. If a positive value is
                        set, the Semaphore is initialized with the given number of permits.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="fenced-lock">
        <xs:all>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        Name of the FencedLock
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lock-acquire-limit" type="xs:nonNegativeInteger" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Maximum number of reentrant lock acquires. Once a caller acquires
                        the lock this many times, it will not be able to acquire the lock again,
                        until it makes at least one unlock() call.
                        By default, no upper bound is set for the number of reentrant lock
                        acquires, which means that once a caller acquires a FencedLock,
                        all of its further lock() calls will succeed. However, for instance,
                        if you set lock-acquire-limit to 2, once a caller acquires
                        the lock, it will be able to acquire it once more, but its third lock()
                        call will not succeed.
                        If lock-acquire-limit is set to 1, then the lock becomes non-reentrant.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="instance-tracking">
        <xs:annotation>
            <xs:documentation>
                Configures tracking of a running Hazelcast instance. For now, this is
                limited to writing information about the Hazelcast instance to a file
                while the instance is starting.
                The file is overwritten on every start of the Hazelcast instance and if
                multiple instance share the same file system, every instance will
                overwrite the tracking file of a previously started instance.
                If this instance is unable to write the file, the exception is logged and
                the instance is allowed to start.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="file-name" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the name of the file which will contain the tracking metadata. If left unset
                        a file named "Hazelcast.process" will be created in the directory as returned by
                        System.getProperty("java.io.tmpdir").
                        The filename can contain placeholders that will be resolved in the same way
                        as placeholders for the format pattern.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="format-pattern" type="xs:string" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the pattern used to render the contents of the instance tracking file.
                        It may contain placeholders for these properties:
                        - "product": The instance product name, e.g. "Hazelcast" or "Hazelcast Enterprise".
                        - "version": The instance version.
                        - "mode": The instance mode which can be "server", "embedded" or "client".
                        - "start_timestamp": The timestamp of when the instance was started expressed the difference,
                        measured in milliseconds, between the current time and midnight, January 1, 1970 UTC
                        - "licensed": If this instance is using a license or not. The value 0 signifies
                        that there is no license set and the value 1 signifies that a license is in use.
                        - "pid": Attempts to get the process ID value. The algorithm does not guarantee to get the
                        process ID on all JVMs and operating systems so please test before use. In case we are unable to
                        get the PID, the value will be -1.

                        The placeholders are defined by a $HZ_INSTANCE_TRACKING{ prefix and followed by }.
                        For instance, a placeholder for the "start_timestamp" would be $HZ_INSTANCE_TRACKING{start_timestamp}.
                        The placeholders are resolved in a fail-safe manner. Any incorrect syntax
                        is ignored and only the known properties are resolved, placeholders for
                        any parameters which do not have defined values will be ignored. This also
                        means that if there is a missing closing bracket in one of the placeholders,
                        the property name will be resolved as anything from the opening bracket
                        to the next closing bracket, which might contain additional opening brackets.
                        If unset, a JSON formatted output will be used.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="false">
            <xs:annotation>
                <xs:documentation>
                    Enables or disables instance tracking.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="metrics">
        <xs:all>
            <xs:element name="management-center" type="metrics-management-center" minOccurs="0"/>
            <xs:element name="jmx" type="metrics-jmx" minOccurs="0"/>
            <xs:element name="collection-frequency-seconds" type="xs:unsignedInt" default="5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the metrics collection frequency in seconds.
                        By default, metrics are collected every 5 seconds.
                        May be overridden by 'hazelcast.metrics.collection.frequency'
                        system property.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Master-switch for the metrics system. Controls whether
                    the metrics are collected and publishers are enabled.
                    May be overridden by 'hazelcast.metrics.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="metrics-management-center">
        <xs:all>
            <xs:element name="retention-seconds" type="xs:unsignedInt" default="5" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the number of seconds the metrics will be retained on the
                        instance. By default, metrics are retained for 5 seconds (that is for
                        one collection of metrics values, if default "collection-frequency-seconds"
                        collection frequency is used). More retention means more heap memory, but
                        allows for longer client hiccups without losing a value (for example to
                        restart the Management Center).
                        May be overridden by 'hazelcast.metrics.mc.retention'
                        system property.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Controls whether the metrics collected are exposed to
                    Hazelcast Management Center. It is enabled by default.
                    Please note that the metrics are polled by the
                    Hazelcast Management Center, hence the members need to
                    buffer the collected metrics between two polls. The aim
                    for this switch is to reduce memory consumption of the
                    metrics system if the Hazelcast Management Center is not
                    used.
                    In order to expose the metrics, the metrics system need
                    to be enabled via the enabled master-switch attribute.
                    May be overridden by 'hazelcast.metrics.mc.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="metrics-jmx">
        <xs:attribute name="enabled" type="xs:boolean" default="true">
            <xs:annotation>
                <xs:documentation>
                    Controls whether the metrics collected are exposed to
                    through JMX. It is enabled by default.
                    In order to expose the metrics, the metrics system need
                    to be enabled via the enabled master-switch attribute.
                    May be overridden by 'hazelcast.metrics.jmx.enabled'
                    system property.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="sql">
        <xs:annotation>
            <xs:documentation>
                SQL service configuration.
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="executor-pool-size" type="xs:int" minOccurs="0" default="-1">
                <xs:annotation>
                    <xs:documentation>
                        Sets the number of threads responsible for execution of SQL statements.
                        The default value -1 sets the pool size equal to the number of CPU cores, and should be good enough
                        for most workloads.
                        Setting the value to less than the number of CPU cores will limit the degree of parallelism of the SQL
                        subsystem. This may be beneficial if you would like to prioritize other CPU-intensive workloads on the
                        same machine.
                        It is not recommended to set the value of this parameter higher than the number of CPU cores because it
                        may decrease the system's overall performance due to excessive context switches.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="statement-timeout-millis" type="xs:unsignedLong" minOccurs="0" default="0">
                <xs:annotation>
                    <xs:documentation>
                        Sets the timeout in milliseconds that is applied to SQL statements without an explicit timeout.
                        It is possible to set a timeout through the SqlStatement.setTimeout(long) method. If the statement
                        timeout is not set, then the value of this parameter will be used.
                        Zero value means no timeout. Negative values are prohibited.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="realms">
        <xs:sequence>
            <xs:element name="realm" type="realm" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="realm">
        <xs:all>
            <xs:element name="authentication" type="authentication" minOccurs="0"/>
            <xs:element name="identity" type="identity" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="required"/>
    </xs:complexType>

    <xs:complexType name="authentication">
        <xs:choice>
            <xs:element name="jaas" type="login-modules"/>
            <xs:element name="tls" type="tls-authentication"/>
            <xs:element name="ldap" type="ldap-authentication"/>
            <xs:element name="kerberos" type="kerberos-authentication"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="tls-authentication">
        <xs:attribute name="roleAttribute" type="xs:string" default="cn"/>
    </xs:complexType>
    <xs:complexType name="ldap-authentication">
        <xs:all>
            <xs:element name="url" type="xs:string"/>
            <xs:element name="socket-factory-class-name" type="xs:string" minOccurs="0"/>

            <xs:element name="parse-dn" type="xs:boolean" minOccurs="0"/>
            <xs:element name="role-context" type="xs:string" minOccurs="0"/>
            <xs:element name="role-filter" type="xs:string" minOccurs="0"/>
            <xs:element name="role-mapping-attribute" type="xs:string" minOccurs="0"/>
            <xs:element name="role-mapping-mode" type="ldap-role-mapping-mode" minOccurs="0"/>
            <xs:element name="role-name-attribute" type="xs:string" minOccurs="0"/>
            <xs:element name="role-recursion-max-depth" type="xs:int" minOccurs="0"/>
            <xs:element name="role-search-scope" type="ldap-search-scope" minOccurs="0"/>
            <xs:element name="user-name-attribute" type="xs:string" minOccurs="0"/>

            <xs:element name="system-user-dn" type="xs:string" minOccurs="0"/>
            <xs:element name="system-user-password" type="xs:string" minOccurs="0"/>
            <xs:element name="system-authentication" type="xs:string" minOccurs="0"/>
            <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
            <xs:element name="password-attribute" type="xs:string" minOccurs="0"/>
            <xs:element name="user-context" type="xs:string" minOccurs="0"/>
            <xs:element name="user-filter" type="xs:string" minOccurs="0"/>
            <xs:element name="user-search-scope" type="ldap-search-scope" minOccurs="0"/>
            <xs:element name="skip-authentication" type="xs:boolean" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:simpleType name="ldap-role-mapping-mode">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="attribute"/>
            <xs:enumeration value="direct"/>
            <xs:enumeration value="reverse"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ldap-search-scope">
        <xs:restriction base="non-space-string">
            <xs:enumeration value="object"/>
            <xs:enumeration value="one-level"/>
            <xs:enumeration value="subtree"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="kerberos-authentication">
        <xs:all>
            <xs:element name="relax-flags-check" type="xs:boolean" minOccurs="0"/>
            <xs:element name="use-name-without-realm" type="xs:boolean" minOccurs="0"/>
            <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
            <xs:element name="principal" type="xs:string" minOccurs="0"/>
            <xs:element name="keytab-file" type="xs:string" minOccurs="0"/>
            <xs:element name="ldap" type="ldap-authentication" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="identity">
        <xs:choice>
            <xs:element name="username-password" type="username-password"/>
            <xs:element name="credentials-factory" type="credentials-factory"/>
            <xs:element name="token" type="token"/>
            <xs:element name="kerberos" type="kerberos-identity"/>
            <xs:element name="credentials-ref" type="xs:string"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="username-password">
        <xs:attribute name="username" type="xs:string" use="required"/>
        <xs:attribute name="password" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="token">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:attribute name="encoding">
                    <xs:simpleType>
                        <xs:restriction base="non-space-string">
                            <xs:enumeration value="none"/>
                            <xs:enumeration value="base64"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:complexType name="kerberos-identity">
        <xs:all>
            <xs:element name="realm" type="xs:string" minOccurs="0"/>
            <xs:element name="security-realm" type="xs:string" minOccurs="0"/>
            <xs:element name="principal" type="xs:string" minOccurs="0"/>
            <xs:element name="keytab-file" type="xs:string" minOccurs="0"/>
            <xs:element name="service-name-prefix" type="xs:string" minOccurs="0"/>
            <xs:element name="spn" type="xs:string" minOccurs="0"/>
            <xs:element name="use-canonical-hostname" type="xs:boolean" minOccurs="0"/>
        </xs:all>
    </xs:complexType>
    <xs:complexType name="realm-reference">
        <xs:attribute name="realm" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="credentials-factory">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attributeGroup ref="class-or-bean-name"/>
    </xs:complexType>

    <xs:complexType name="auditlog">
        <xs:sequence>
            <xs:element name="properties" type="properties" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="enabled" default="false" type="xs:string"/>
        <xs:attribute name="factory-class-name" type="xs:string"/>
    </xs:complexType>
</xs:schema>
