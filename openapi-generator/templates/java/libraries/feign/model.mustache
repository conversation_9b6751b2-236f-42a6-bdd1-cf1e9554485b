{{>licenseInfo}}

package {{package}};

{{#useReflectionEqualsHashCode}}
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
{{/useReflectionEqualsHashCode}}
{{#models}}
{{#model}}
{{#additionalPropertiesType}}
import java.util.Map;
import java.util.HashMap;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
{{/additionalPropertiesType}}
{{/model}}
{{/models}}
import java.util.Objects;
import java.util.Arrays;
{{#imports}}
import {{import}};
{{/imports}}
{{#serializableModel}}
import java.io.Serializable;
{{/serializableModel}}
{{#jackson}}
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;
{{#withXml}}
import com.fasterxml.jackson.dataformat.xml.annotation.*;
{{/withXml}}
{{#vendorExtensions.x-has-readonly-properties}}
import com.fasterxml.jackson.annotation.JsonCreator;
{{/vendorExtensions.x-has-readonly-properties}}
{{/jackson}}
{{#withXml}}
import {{javaxPackage}}.xml.bind.annotation.*;
import {{javaxPackage}}.xml.bind.annotation.adapters.*;
import io.github.threetenjaxb.core.*;
{{/withXml}}
{{#jsonb}}
import java.lang.reflect.Type;
import {{javaxPackage}}.json.bind.annotation.JsonbTypeDeserializer;
import {{javaxPackage}}.json.bind.annotation.JsonbTypeSerializer;
import {{javaxPackage}}.json.bind.serializer.DeserializationContext;
import {{javaxPackage}}.json.bind.serializer.JsonbDeserializer;
import {{javaxPackage}}.json.bind.serializer.JsonbSerializer;
import {{javaxPackage}}.json.bind.serializer.SerializationContext;
import {{javaxPackage}}.json.stream.JsonGenerator;
import {{javaxPackage}}.json.stream.JsonParser;
import {{javaxPackage}}.json.bind.annotation.JsonbProperty;
{{#vendorExtensions.x-has-readonly-properties}}
import {{javaxPackage}}.json.bind.annotation.JsonbCreator;
{{/vendorExtensions.x-has-readonly-properties}}
{{/jsonb}}
{{#parcelableModel}}
import android.os.Parcelable;
import android.os.Parcel;
{{/parcelableModel}}
{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;
{{/useBeanValidation}}
{{#performBeanValidation}}
import org.hibernate.validator.constraints.*;
{{/performBeanValidation}}
{{#supportUrlQuery}}
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.StringJoiner;
{{/supportUrlQuery}}

{{#models}}
{{#model}}
{{#isEnum}}{{>modelEnum}}{{/isEnum}}{{^isEnum}}{{#vendorExtensions.x-is-one-of-interface}}{{>oneof_interface}}{{/vendorExtensions.x-is-one-of-interface}}{{^vendorExtensions.x-is-one-of-interface}}{{>pojo}}{{/vendorExtensions.x-is-one-of-interface}}{{/isEnum}}
{{/model}}
{{/models}}
