package {{package}};

import {{invokerPackage}}.ApiClient;
import {{invokerPackage}}.EncodingUtils;
{{#legacyDates}}
import {{invokerPackage}}.ParamExpander;
{{/legacyDates}}
import {{modelPackage}}.ApiResponse;

{{#imports}}import {{import}};
{{/imports}}

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
import feign.*;

{{>generatedAnnotation}}
public interface {{classname}} extends ApiClient.Api {

{{#operations}}{{#operation}}
  /**
   * {{summary}}
   * {{notes}}
{{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}
{{/allParams}}
{{#returnType}}
   * @return {{.}}
{{/returnType}}
{{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
{{/externalDocs}}
{{#isDeprecated}}
   * @deprecated
{{/isDeprecated}}
   */
{{#isDeprecated}}
  @Deprecated
{{/isDeprecated}}
  @RequestLine("{{httpMethod}} {{{path}}}{{#hasQueryParams}}?{{/hasQueryParams}}{{#queryParams}}{{baseName}}={{=<% %>=}}{<%paramName%>}<%={{ }}=%>{{^-last}}&{{/-last}}{{/queryParams}}")
  @Headers({
{{#vendorExtensions.x-content-type}}    "Content-Type: {{{vendorExtensions.x-content-type}}}",
{{/vendorExtensions.x-content-type}}    "Accept: {{#vendorExtensions.x-accepts}}{{{.}}}{{^-last}},{{/-last}}{{/vendorExtensions.x-accepts}}",{{#headerParams}}
    "{{baseName}}: {{=<% %>=}}{<%paramName%>}<%={{ }}=%>"{{^-last}},
    {{/-last}}{{/headerParams}}
  })
  {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{nickname}}({{#allParams}}{{^isBodyParam}}{{^isFormParam}}{{^legacyDates}}@Param("{{paramName}}") {{/legacyDates}}{{#legacyDates}}@Param(value="{{paramName}}", expander=ParamExpander.class) {{/legacyDates}}{{/isFormParam}}{{#isFormParam}}@Param("{{baseName}}") {{/isFormParam}}{{/isBodyParam}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}});

  /**
   * {{summary}}
   * Similar to <code>{{operationId}}</code> but it also returns the http response headers .
   * {{notes}}
{{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}
{{/allParams}}
{{#returnType}}
   * @return A ApiResponse that wraps the response boyd and the http headers.
{{/returnType}}
{{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
{{/externalDocs}}
{{#isDeprecated}}
   * @deprecated
{{/isDeprecated}}
   */
{{#isDeprecated}}
  @Deprecated
{{/isDeprecated}}
  @RequestLine("{{httpMethod}} {{{path}}}{{#hasQueryParams}}?{{/hasQueryParams}}{{#queryParams}}{{baseName}}={{=<% %>=}}{<%paramName%>}<%={{ }}=%>{{^-last}}&{{/-last}}{{/queryParams}}")
  @Headers({
{{#vendorExtensions.x-content-type}}    "Content-Type: {{{vendorExtensions.x-content-type}}}",
{{/vendorExtensions.x-content-type}}    "Accept: {{#vendorExtensions.x-accepts}}{{{.}}}{{^-last}},{{/-last}}{{/vendorExtensions.x-accepts}}",{{#headerParams}}
    "{{baseName}}: {{=<% %>=}}{<%paramName%>}<%={{ }}=%>"{{^-last}},
    {{/-last}}{{/headerParams}}
  })
  ApiResponse<{{#returnType}}{{{.}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> {{nickname}}WithHttpInfo({{#allParams}}{{^isBodyParam}}{{^isFormParam}}{{^legacyDates}}@Param("{{paramName}}") {{/legacyDates}}{{#legacyDates}}@Param(value="{{paramName}}", expander=ParamExpander.class) {{/legacyDates}}{{/isFormParam}}{{#isFormParam}}@Param("{{baseName}}") {{/isFormParam}}{{/isBodyParam}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}});


  {{#hasQueryParams}}
  /**
   * {{summary}}
   * {{notes}}
   * Note, this is equivalent to the other <code>{{operationId}}</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link {{operationIdCamelCase}}QueryParams} class that allows for
   * building up this map in a fluent style.
      {{#allParams}}
        {{^isQueryParam}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}
        {{/isQueryParam}}
      {{/allParams}}
   * @param queryParams Map of query parameters as name-value pairs
   *   <p>The following elements may be specified in the query map:</p>
   *   <ul>
      {{#queryParams}}
   *   <li>{{paramName}} - {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}</li>
      {{/queryParams}}
   *   </ul>
      {{#returnType}}
   * @return {{.}}
      {{/returnType}}
      {{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
      {{/externalDocs}}
      {{#isDeprecated}}
   * @deprecated
      {{/isDeprecated}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  @RequestLine("{{httpMethod}} {{{path}}}?{{#queryParams}}{{baseName}}={{=<% %>=}}{<%paramName%>}<%={{ }}=%>{{^-last}}&{{/-last}}{{/queryParams}}")
  @Headers({
{{#vendorExtensions.x-content-type}}  "Content-Type: {{{vendorExtensions.x-content-type}}}",
{{/vendorExtensions.x-content-type}}  "Accept: {{#vendorExtensions.x-accepts}}{{{.}}}{{^-last}},{{/-last}}{{/vendorExtensions.x-accepts}}",{{#headerParams}}
      "{{baseName}}: {{=<% %>=}}{<%paramName%>}<%={{ }}=%>"{{^-last}},
      {{/-last}}{{/headerParams}}
  })
  {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{nickname}}({{#allParams}}{{^isQueryParam}}{{^isBodyParam}}{{^isFormParam}}{{^legacyDates}}@Param("{{paramName}}") {{/legacyDates}}{{#legacyDates}}@Param(value="{{paramName}}", expander=ParamExpander.class) {{/legacyDates}}{{/isFormParam}}{{#isFormParam}}@Param("{{baseName}}") {{/isFormParam}}{{/isBodyParam}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/isQueryParam}}{{/allParams}}@QueryMap(encoded=true) {{operationIdCamelCase}}QueryParams queryParams);

  /**
  * {{summary}}
  * {{notes}}
  * Note, this is equivalent to the other <code>{{operationId}}</code> that receives the query parameters as a map,
  * but this one also exposes the Http response headers
  {{#allParams}}
      {{^isQueryParam}}
              * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}
      {{/isQueryParam}}
  {{/allParams}}
      * @param queryParams Map of query parameters as name-value pairs
      *   <p>The following elements may be specified in the query map:</p>
      *   <ul>
      {{#queryParams}}
          *   <li>{{paramName}} - {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}</li>
      {{/queryParams}}
      *   </ul>
  {{#returnType}}
          * @return {{.}}
  {{/returnType}}
  {{#externalDocs}}
          * {{description}}
          * @see <a href="{{url}}">{{summary}} Documentation</a>
  {{/externalDocs}}
  {{#isDeprecated}}
          * @deprecated
  {{/isDeprecated}}
      */
  {{#isDeprecated}}
          @Deprecated
  {{/isDeprecated}}
      @RequestLine("{{httpMethod}} {{{path}}}?{{#queryParams}}{{baseName}}={{=<% %>=}}{<%paramName%>}<%={{ }}=%>{{^-last}}&{{/-last}}{{/queryParams}}")
      @Headers({
  {{#vendorExtensions.x-content-type}}  "Content-Type: {{{vendorExtensions.x-content-type}}}",
  {{/vendorExtensions.x-content-type}}  "Accept: {{#vendorExtensions.x-accepts}}{{{.}}}{{^-last}},{{/-last}}{{/vendorExtensions.x-accepts}}",{{#headerParams}}
          "{{baseName}}: {{=<% %>=}}{<%paramName%>}<%={{ }}=%>"{{^-last}},
      {{/-last}}{{/headerParams}}
      })
   ApiResponse<{{#returnType}}{{{.}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> {{nickname}}WithHttpInfo({{#allParams}}{{^isQueryParam}}{{^isBodyParam}}{{^isFormParam}}{{^legacyDates}}@Param("{{paramName}}") {{/legacyDates}}{{#legacyDates}}@Param(value="{{paramName}}", expander=ParamExpander.class) {{/legacyDates}}{{/isFormParam}}{{#isFormParam}}@Param("{{baseName}}") {{/isFormParam}}{{/isBodyParam}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/isQueryParam}}{{/allParams}}@QueryMap(encoded=true) {{operationIdCamelCase}}QueryParams queryParams);


   /**
   * A convenience class for generating query parameters for the
   * <code>{{operationId}}</code> method in a fluent style.
   */
  public static class {{operationIdCamelCase}}QueryParams extends HashMap<String, Object> {
      {{#queryParams}}
    public {{operationIdCamelCase}}QueryParams {{paramName}}({{>nullable_var_annotations}} final {{{dataType}}} value) {
        {{#collectionFormat}}
      put("{{baseName}}", EncodingUtils.encodeCollection(value, "{{collectionFormat}}"));
        {{/collectionFormat}}
        {{^collectionFormat}}
      put("{{baseName}}", EncodingUtils.encode(value));
        {{/collectionFormat}}
      return this;
    }
      {{/queryParams}}
  }
    {{/hasQueryParams}}
  {{/operation}}
{{/operations}}
}
