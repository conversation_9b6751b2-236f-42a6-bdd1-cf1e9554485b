{{>licenseInfo}}

package {{invokerPackage}}.auth;

import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

{{>generatedAnnotation}}
public interface Authentication {
    /**
     * Apply authentication settings to header and / or query parameters.
     *
     * @param queryParams The query parameters for the request
     * @param headerParams The header parameters for the request
     * @param cookieParams The cookie parameters for the request
     */
    void applyToParams(MultiValueMap<String, String> queryParams, HttpHeaders headerParams, MultiValueMap<String, String> cookieParams);
}
