package {{package}};

import {{invokerPackage}}.ApiClient;

{{#imports}}import {{import}};
{{/imports}}

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.client.http.EmptyContent;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.HttpContent;
import com.google.api.client.http.InputStreamContent;
import com.google.api.client.http.HttpMethods;
import com.google.api.client.http.HttpResponse;
import com.google.api.client.json.Json;

import {{javaxPackage}}.ws.rs.core.UriBuilder;
import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

{{>generatedAnnotation}}
{{#operations}}
public class {{classname}} {
    private ApiClient apiClient;

    public {{classname}}() {
        this(new ApiClient());
    }

    public {{classname}}(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    {{#operation}}
  /**{{#summary}}
    * {{.}}{{/summary}}{{#notes}}
    * {{.}}{{/notes}}{{#responses}}
    * <p><b>{{code}}</b>{{#message}} - {{.}}{{/message}}{{/responses}}{{#allParams}}
    * @param {{paramName}} {{description}}{{^description}}The {{paramName}} parameter{{/description}}{{/allParams}}{{#returnType}}
    * @return {{.}}{{/returnType}}
    * @throws IOException if an error occurs while attempting to invoke the API{{#externalDocs}}
    * {{description}}
    * @see <a href="{{url}}">{{summary}} Documentation</a>
    {{/externalDocs}}{{#isDeprecated}}
    * @deprecated
    {{/isDeprecated}}

    **/
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws IOException {
        {{#returnType}}HttpResponse response = {{/returnType}}{{operationId}}ForHttpResponse({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});{{#returnType}}
        TypeReference<{{{returnType}}}> typeRef = new TypeReference<{{{returnType}}}>() {};
        return apiClient.getObjectMapper().readValue(response.getContent(), typeRef);{{/returnType}}
    }

  /**{{#summary}}
    * {{.}}{{/summary}}{{#notes}}
    * {{.}}{{/notes}}{{#responses}}
    * <p><b>{{code}}</b>{{#message}} - {{.}}{{/message}}{{/responses}}{{#requiredParams}}
    * @param {{paramName}} {{description}}{{^description}}The {{paramName}} parameter{{/description}}{{/requiredParams}}
    * @param params Map of query params. A collection will be interpreted as passing in multiple instances of the same query param.{{#returnType}}
    * @return {{.}}{{/returnType}}
    * @throws IOException if an error occurs while attempting to invoke the API{{#externalDocs}}
    * {{description}}
    * @see <a href="{{url}}">{{summary}} Documentation</a>
    {{/externalDocs}}{{#isDeprecated}}
    * @deprecated
    {{/isDeprecated}}

    **/
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#bodyParam}}{{^required}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/required}}{{/bodyParam}}{{#requiredParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}}{{#hasRequiredParams}}, {{/hasRequiredParams}}Map<String, Object> params) throws IOException {
        {{#returnType}}HttpResponse response = {{/returnType}}{{operationId}}ForHttpResponse({{#bodyParam}}{{^required}}{{paramName}}, {{/required}}{{/bodyParam}}{{#requiredParams}}{{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}}{{#hasRequiredParams}}, {{/hasRequiredParams}}params);{{#returnType}}
        TypeReference<{{{returnType}}}> typeRef = new TypeReference<{{{returnType}}}>() {};
        return apiClient.getObjectMapper().readValue(response.getContent(), typeRef);{{/returnType}}
    }

    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public HttpResponse {{operationId}}ForHttpResponse({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws IOException {
        {{#allParams}}{{#required}}// verify the required parameter '{{paramName}}' is set
        if ({{paramName}} == null) {
            throw new IllegalArgumentException("Missing the required parameter '{{paramName}}' when calling {{operationId}}");
        }{{/required}}{{/allParams}}
        {{#hasPathParams}}
        // create a map of path variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();{{#pathParams}}
        uriVariables.put("{{baseName}}", {{{paramName}}});{{/pathParams}}
        {{/hasPathParams}}
        UriBuilder uriBuilder = UriBuilder.fromUri(apiClient.getBasePath() + "{{{path}}}");{{#hasQueryParams}}
        {{#queryParams}}
        if ({{paramName}} != null) {
            String key = "{{baseName}}";
            Object value = {{paramName}};
            if (value instanceof Collection) {
                uriBuilder = uriBuilder.queryParam(key, ((Collection) value).toArray());
            } else if (value instanceof Object[]) {
                uriBuilder = uriBuilder.queryParam(key, (Object[]) value);
            } else {
                uriBuilder = uriBuilder.queryParam(key, value);
            }
        }{{/queryParams}}{{/hasQueryParams}}

        String localVarUrl = uriBuilder{{#hasPathParams}}.buildFromMap(uriVariables).toString();{{/hasPathParams}}{{^hasPathParams}}.build().toString();{{/hasPathParams}}
        GenericUrl genericUrl = new GenericUrl(localVarUrl);

        HttpContent content = {{#isBodyAllowed}}{{#bodyParam}}apiClient.new JacksonJsonHttpContent({{paramName}}){{/bodyParam}}{{^bodyParam}}new EmptyContent(){{/bodyParam}}{{/isBodyAllowed}}{{^isBodyAllowed}}null{{/isBodyAllowed}};
        return apiClient.getHttpRequestFactory().buildRequest(HttpMethods.{{httpMethod}}, genericUrl, content).execute();
    }{{#bodyParam}}

      {{#isDeprecated}}
      @Deprecated
      {{/isDeprecated}}
      public HttpResponse {{operationId}}ForHttpResponse({{#allParams}}{{#isBodyParam}}java.io.InputStream {{paramName}}{{/isBodyParam}}{{^isBodyParam}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{/isBodyParam}}{{^-last}}, {{/-last}}{{/allParams}}, String mediaType) throws IOException {
          {{#allParams}}{{#required}}// verify the required parameter '{{paramName}}' is set
              if ({{paramName}} == null) {
              throw new IllegalArgumentException("Missing the required parameter '{{paramName}}' when calling {{operationId}}");
              }{{/required}}{{/allParams}}
          {{#hasPathParams}}
                  // create a map of path variables
                  final Map<String, Object> uriVariables = new HashMap<String, Object>();{{#pathParams}}
                      uriVariables.put("{{baseName}}", {{{paramName}}});{{/pathParams}}
          {{/hasPathParams}}
              UriBuilder uriBuilder = UriBuilder.fromUri(apiClient.getBasePath() + "{{{path}}}");{{#hasQueryParams}}
              {{#queryParams}}
              if ({{paramName}} != null) {
                  String key = "{{baseName}}";
                  Object value = {{paramName}};
                  if (value instanceof Collection) {
                    uriBuilder = uriBuilder.queryParam(key, ((Collection) value).toArray());
                  } else if (value instanceof Object[]) {
                    uriBuilder = uriBuilder.queryParam(key, (Object[]) value);
                  } else {
                    uriBuilder = uriBuilder.queryParam(key, value);
                  }
              }{{/queryParams}}{{/hasQueryParams}}

              String localVarUrl = uriBuilder{{#hasPathParams}}.buildFromMap(uriVariables).toString();{{/hasPathParams}}{{^hasPathParams}}.build().toString();{{/hasPathParams}}
              GenericUrl genericUrl = new GenericUrl(localVarUrl);

              HttpContent content = {{#bodyParam}}{{paramName}} == null ?
                apiClient.new JacksonJsonHttpContent(null) :
                new InputStreamContent(mediaType == null ? Json.MEDIA_TYPE : mediaType, {{paramName}}){{/bodyParam}};
              return apiClient.getHttpRequestFactory().buildRequest(HttpMethods.{{httpMethod}}, genericUrl, content).execute();
      }{{/bodyParam}}

    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public HttpResponse {{operationId}}ForHttpResponse({{#bodyParam}}{{^required}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/required}}{{/bodyParam}}{{#requiredParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}}{{#hasRequiredParams}}, {{/hasRequiredParams}}Map<String, Object> params) throws IOException {
        {{#allParams}}{{#required}}// verify the required parameter '{{paramName}}' is set
        if ({{paramName}} == null) {
            throw new IllegalArgumentException("Missing the required parameter '{{paramName}}' when calling {{operationId}}");
        }{{/required}}{{/allParams}}
        {{#hasPathParams}}
        // create a map of path variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();{{#pathParams}}
        uriVariables.put("{{baseName}}", {{{paramName}}});{{/pathParams}}
        {{/hasPathParams}}
        UriBuilder uriBuilder = UriBuilder.fromUri(apiClient.getBasePath() + "{{{path}}}");

        // Copy the params argument if present, to allow passing in immutable maps
        Map<String, Object> allParams = params == null ? new HashMap<String, Object>() : new HashMap<String, Object>(params);{{#queryParams}}{{#required}}
        // Add the required query param '{{paramName}}' to the map of query params
        allParams.put("{{paramName}}", {{paramName}});{{/required}}{{/queryParams}}

        for (Map.Entry<String, Object> entry: allParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (key != null && value != null) {
                if (value instanceof Collection) {
                    uriBuilder = uriBuilder.queryParam(key, ((Collection) value).toArray());
                } else if (value instanceof Object[]) {
                    uriBuilder = uriBuilder.queryParam(key, (Object[]) value);
                } else {
                    uriBuilder = uriBuilder.queryParam(key, value);
                }
            }
        }

        String localVarUrl = uriBuilder{{#hasPathParams}}.buildFromMap(uriVariables).toString();{{/hasPathParams}}{{^hasPathParams}}.build().toString();{{/hasPathParams}}
        GenericUrl genericUrl = new GenericUrl(localVarUrl);

        HttpContent content = {{#isBodyAllowed}}{{#bodyParam}}apiClient.new JacksonJsonHttpContent({{paramName}}){{/bodyParam}}{{^bodyParam}}new EmptyContent(){{/bodyParam}}{{/isBodyAllowed}}{{^isBodyAllowed}}null{{/isBodyAllowed}};
        return apiClient.getHttpRequestFactory().buildRequest(HttpMethods.{{httpMethod}}, genericUrl, content).execute();
    }


    {{/operation}}
}
{{/operations}}
