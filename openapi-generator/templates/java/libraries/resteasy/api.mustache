package {{package}};

import {{invokerPackage}}.ApiException;
import {{invokerPackage}}.ApiClient;
import {{invokerPackage}}.Configuration;
import {{invokerPackage}}.Pair;

import {{javaxPackage}}.ws.rs.core.GenericType;

{{#imports}}import {{import}};
{{/imports}}

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{>generatedAnnotation}}
{{#operations}}
public class {{classname}} {
  private ApiClient apiClient;

  public {{classname}}() {
    this(Configuration.getDefaultApiClient());
  }

  public {{classname}}(ApiClient apiClient) {
    this.apiClient = apiClient;
  }

  public ApiClient getApiClient() {
    return apiClient;
  }

  public void setApiClient(ApiClient apiClient) {
    this.apiClient = apiClient;
  }

  {{#operation}}
  /**
   * {{summary}}
   * {{notes}}{{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{.}}{{/defaultValue}}){{/required}}{{/allParams}}{{#returnType}}
   * @return a {@code {{{returnType}}}}{{/returnType}}
   * @throws ApiException if fails to make API call
   {{#isDeprecated}}
   * @deprecated
   {{/isDeprecated}}
   {{#externalDocs}}
   * {{description}}
   * @see <a href="{{url}}">{{summary}} Documentation</a>
   {{/externalDocs}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  public {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
    Object localVarPostBody = {{#bodyParam}}{{paramName}}{{/bodyParam}}{{^bodyParam}}null{{/bodyParam}};
    {{#allParams}}{{#required}}
    // verify the required parameter '{{paramName}}' is set
    if ({{paramName}} == null) {
      throw new ApiException(400, "Missing the required parameter '{{paramName}}' when calling {{operationId}}");
    }
    {{/required}}{{/allParams}}
    // create path and map variables
    String localVarPath = "{{{path}}}".replaceAll("\\{format\\}","json"){{#pathParams}}
      .replaceAll("\\{" + "{{baseName}}" + "\\}", apiClient.escapeString({{{paramName}}}.toString())){{/pathParams}};

    // query params
    List<Pair> localVarQueryParams = new ArrayList<Pair>();
    Map<String, String> localVarHeaderParams = new HashMap<String, String>();
    Map<String, String> localVarCookieParams = new HashMap<String, String>();
    Map<String, Object> localVarFormParams = new HashMap<String, Object>();

    {{#queryParams}}
    localVarQueryParams.addAll(apiClient.parameterToPairs("{{{collectionFormat}}}", "{{baseName}}", {{paramName}}));
    {{/queryParams}}

    {{#headerParams}}if ({{paramName}} != null)
      localVarHeaderParams.put("{{baseName}}", apiClient.parameterToString({{paramName}}));
    {{/headerParams}}

    {{#cookieParams}}if ({{paramName}} != null)
      localVarCookieParams.put("{{baseName}}", apiClient.parameterToString({{paramName}}));
    {{/cookieParams}}

    {{#formParams}}if ({{paramName}} != null)
      localVarFormParams.put("{{baseName}}", {{paramName}});
    {{/formParams}}

    final String[] localVarAccepts = {
      {{#produces}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/produces}}
    };
    final String localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);

    final String[] localVarContentTypes = {
      {{#consumes}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/consumes}}
    };
    final String localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

    String[] localVarAuthNames = new String[] { {{#authMethods}}"{{name}}"{{^-last}}, {{/-last}}{{/authMethods}} };

    {{#returnType}}
    GenericType<{{{returnType}}}> localVarReturnType = new GenericType<{{{returnType}}}>() {};
    return apiClient.invokeAPI(localVarPath, "{{httpMethod}}", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localVarReturnType);
    {{/returnType}}{{^returnType}}
    apiClient.invokeAPI(localVarPath, "{{httpMethod}}", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, null);
    {{/returnType}}
  }
  {{/operation}}
}
{{/operations}}
