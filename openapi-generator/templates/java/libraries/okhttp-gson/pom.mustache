<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>{{artifactVersion}}</version>
    <url>{{artifactUrl}}</url>
    <description>{{artifactDescription}}</description>
    <scm>
        <connection>{{scmConnection}}</connection>
        <developerConnection>{{scmDeveloperConnection}}</developerConnection>
        <url>{{scmUrl}}</url>
    </scm>
{{#parentOverridden}}
    <parent>
        <groupId>{{{parentGroupId}}}</groupId>
        <artifactId>{{{parentArtifactId}}}</artifactId>
        <version>{{{parentVersion}}}</version>
    </parent>
{{/parentOverridden}}

    <licenses>
        <license>
            <name>{{licenseName}}</name>
            <url>{{licenseUrl}}</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>{{developerName}}</name>
            <email>{{developerEmail}}</email>
            <organization>{{developerOrganization}}</organization>
            <organizationUrl>{{developerOrganizationUrl}}</organizationUrl>
        </developer>
    </developers>

    <build>
        <plugins>
           <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <fork>true</fork>
                    <meminitial>128m</meminitial>
                    <maxmem>512m</maxmem>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-J-Xss4m</arg><!-- Compiling the generated JSON.java file may require larger stack size. -->
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <systemPropertyVariables>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemPropertyVariables>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                    <threadCount>10</threadCount>
                </configuration>
                <dependencies>
                    <!--Custom provider and engine for Junit 5 to surefire-->
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit-version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Create fat JAR with all dependencies -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>{{artifactId}}</finalName>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Built-By>OpenAPI Generator</Built-By>
                                        <Implementation-Title>{{artifactId}}</Implementation-Title>
                                        <Implementation-Version>{{artifactVersion}}</Implementation-Version>
                                    </manifestEntries>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.6.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>{{{sourceFolder}}}</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.6.3</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <doclint>none</doclint>
                    <tags>
                        <tag>
                            <name>http.response.details</name>
                            <placement>a</placement>
                            <head>Http Response Details:</head>
                        </tag>
                    </tags>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Use spotless plugin to automatically format code, remove unused import, etc
                 To apply changes directly to the file, run `mvn spotless:apply`
                 Ref: https://github.com/diffplug/spotless/tree/main/plugin-maven
            -->
            <plugin>
              <groupId>com.diffplug.spotless</groupId>
              <artifactId>spotless-maven-plugin</artifactId>
              <version>${spotless.version}</version>
              <configuration>
                <formats>
                  <!-- you can define as many formats as you want, each is independent -->
                  <format>
                    <!-- define the files to apply to -->
                    <includes>
                      <include>.gitignore</include>
                    </includes>
                    <!-- define the steps to apply to those files -->
                    <trimTrailingWhitespace/>
                    <endWithNewline/>
                    <indent>
                      <spaces>true</spaces> <!-- or <tabs>true</tabs> -->
                      <spacesPerTab>4</spacesPerTab> <!-- optional, default is 4 -->
                    </indent>
                  </format>
                </formats>
                <!-- define a language-specific format -->
                <java>
                  <!-- no need to specify files, inferred automatically, but you can if you want -->

                  <!-- apply a specific flavor of google-java-format and reflow long strings -->
                  <googleJavaFormat>
                    <version>1.8</version>
                    <style>AOSP</style>
                    <reflowLongStrings>true</reflowLongStrings>
                  </googleJavaFormat>

                  <removeUnusedImports/>
                  <importOrder/>

                </java>
              </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.2.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        {{#swagger1AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger2AnnotationLibrary}}
        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson-version}</version>
        </dependency>
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>${gson-fire-version}</version>
        </dependency>
        {{#hasOAuthMethods}}
        <dependency>
            <groupId>org.apache.oltu.oauth2</groupId>
            <artifactId>org.apache.oltu.oauth2.client</artifactId>
            <version>1.0.2</version>
        </dependency>
        {{/hasOAuthMethods}}
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3-version}</version>
        </dependency>
        {{#joda}}
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${jodatime-version}</version>
        </dependency>
        {{/joda}}
        {{#dynamicOperations}}
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser-v3</artifactId>
            <version>2.0.30</version>
        </dependency>
        {{/dynamicOperations}}
        {{#useBeanValidation}}
        <!-- Bean Validation API support -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>${beanvalidation-version}</version>
            <scope>provided</scope>
        </dependency>
        {{/useBeanValidation}}
        {{#performBeanValidation}}
        <!-- Bean Validation Impl. used to perform BeanValidation -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.4.3.Final</version>
        </dependency>
        <dependency>
            <groupId>jakarta.el</groupId>
            <artifactId>jakarta.el-api</artifactId>
            <version>${jakarta.el-version}</version>
        </dependency>
        {{/performBeanValidation}}
        {{#parcelableModel}}
        <!-- Needed for Parcelable support-->
        <dependency>
            <groupId>com.google.android</groupId>
            <artifactId>android</artifactId>
            <version>4.1.1.4</version>
            <scope>provided</scope>
        </dependency>
        {{/parcelableModel}}
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>
        {{#openApiNullable}}
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>
        {{/openApiNullable}}
        {{#withAWSV4Signature}}
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>auth</artifactId>
            <version>2.20.157</version>
        </dependency>
        {{/withAWSV4Signature}}
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
            <version>${jakarta.ws.rs-api-version}</version>
        </dependency>
        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <version>${junit-platform-runner.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <gson-fire-version>1.9.0</gson-fire-version>
        {{#swagger1AnnotationLibrary}}
        <swagger-annotations-version>1.6.6</swagger-annotations-version>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <swagger-annotations-version>2.2.15</swagger-annotations-version>
        {{/swagger2AnnotationLibrary}}
        <okhttp-version>4.12.0</okhttp-version>
        <gson-version>2.10.1</gson-version>
        <commons-lang3-version>3.17.0</commons-lang3-version>
        {{#openApiNullable}}
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        {{/openApiNullable}}
        {{#joda}}
        <jodatime-version>2.12.0</jodatime-version>
        {{/joda}}
        {{#useJakartaEe}}
        <jakarta-annotation-version>2.1.1</jakarta-annotation-version>
        <beanvalidation-version>3.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{^useJakartaEe}}
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        <beanvalidation-version>2.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{#performBeanValidation}}
        <jakarta.el-version>3.0.3</jakarta.el-version>
        {{/performBeanValidation}}
        <junit-version>5.10.3</junit-version>
        <junit-platform-runner.version>1.10.0</junit-platform-runner.version>
        <jakarta.ws.rs-api-version>2.1.6</jakarta.ws.rs-api-version>
        <jsr311-api-version>1.1.1</jsr311-api-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spotless.version>2.43.0</spotless.version>
    </properties>
</project>
